<template>
  <div class="javelin-king">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <Position />
              </el-icon>
              <span>标枪王者</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
              <!-- 游戏ID -->
              <el-form-item prop="gameId" label="游戏ID">
                <el-input v-model="form.gameId" placeholder="请输入游戏ID" :disabled="loading.download" clearable />
              </el-form-item>

              <!-- 充值数量 -->
              <el-form-item label="充值数量">
                <el-input-number v-model="form.amount" :min="1" :max="999999999" :step="1000" controls-position="right"
                  style="width: 100%" />
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮 -->
          <div class="action-section">
            <el-button type="primary" :loading="loading.download" @click="handleDownload" :icon="Download"
              class="action-button">
              下载数据
            </el-button>
            <el-button type="success" :loading="loading.diamond" @click="handleDiamondRecharge" :icon="Present"
              class="action-button">
              充值钻石
            </el-button>
            <el-button type="warning" :loading="loading.stone" @click="handleExpStone" :icon="Star"
              class="action-button">
              抽奖经验石
            </el-button>
          </div>

          <!-- 数据显示区域 -->
          <div v-if="userData || hasRechargeHistory" class="data-section">
            <div class="section-title">
              <el-icon>
                <Document />
              </el-icon>
              <span>游戏数据</span>
              <el-tag v-if="!userData" type="warning" effect="plain" size="small" class="data-status">
                仅显示充值记录
              </el-tag>
            </div>

            <el-tabs type="border-card">
              <!-- 基础信息 -->
              <el-tab-pane label="基础信息">
                <!-- 添加头像区域 -->
                <div class="user-profile">
                  <el-avatar :size="80" :src="userData?.headImg || ''" class="user-avatar">
                    <img src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" alt="默认头像" />
                  </el-avatar>
                  <div class="user-title">{{ userData?.name || '未知用户' }}</div>
                </div>

                <el-form :model="formData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="用户ID">
                        <el-input v-model="formData.id" disabled />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="昵称">
                        <el-input v-model="formData.name" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="段位">
                        <el-input v-model="formData.rankingName" disabled />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="金币">
                        <el-input-number v-model="formData.coin" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="钻石">
                        <el-input-number v-model="formData.diamond" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="经验石">
                        <el-input-number v-model="formData.exp" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="奖杯数">
                        <el-input-number v-model="formData.cup" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="胜场/总场">
                        <el-input :value="`${formData.winCount || 0}/${formData.allCount || 0}`" disabled />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="胜率">
                        <el-input v-model="formData.winRate" disabled />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 英雄信息 -->
              <el-tab-pane label="英雄信息">
                <el-table :data="itemsList" border style="width: 100%">
                  <el-table-column label="英雄ID" prop="id" width="100" />
                  <el-table-column label="英雄名称" prop="name" min-width="120" />
                  <el-table-column label="等级" width="200">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.level" :min="1" controls-position="right"
                        style="width: 100%" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>

            <!-- 将操作历史移动到这里 -->
            <div v-if="operationHistory.length" class="history-section">
              <div class="section-title">
                <el-icon>
                  <Timer />
                </el-icon>
                <span>操作记录</span>
              </div>
              <el-timeline>
                <el-timeline-item v-for="(history, index) in operationHistory.slice(0, 5)" :key="index"
                  :type="history.success ? 'success' : 'danger'" :timestamp="history.time" :hollow="true" size="normal">
                  <div class="history-content">
                    <div class="history-header">
                      <span class="history-user">ID: {{ history.userId || userData?.id }} | 昵称: {{ history.userName ||
                        userData?.name }}</span>
                      <div class="history-actions">
                        <el-tag :type="history.success ? 'success' : 'danger'" size="small">
                          {{ history.success ? '成功' : '失败' }}
                        </el-tag>
                        <el-button type="primary" link size="small" @click="handleEdit(history, index)">
                          编辑
                        </el-button>
                      </div>
                    </div>
                    <div class="history-operation">
                      {{ history.operation }}
                    </div>
                    <div class="history-details" v-if="history.details">
                      {{ history.details }}
                    </div>
                  </div>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 修改编辑对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑操作记录" width="500px" destroy-on-close>
      <el-form :model="editForm" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="用户ID">
              <el-input v-model="editForm.userId" type="text" placeholder="请输入用户ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户昵称">
              <el-input v-model="editForm.userName" type="text" placeholder="请输入用户昵称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="操作时间">
          <el-input v-model="editForm.time" type="text" placeholder="请输入操作时间" />
        </el-form-item>
        <el-form-item label="操作内容">
          <el-input v-model="editForm.operation" type="text" placeholder="请输入操作内容" />
        </el-form-item>
        <el-form-item label="详细信息">
          <el-input v-model="editForm.details" type="textarea" :rows="4" placeholder="请输入详细信息" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveEdit">
            保存
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Position,
  Download,
  Present,
  Star,
  Timer,
  Document,
  InfoFilled
} from '@element-plus/icons-vue'

// 表单数据
const form = reactive({
  gameId: '4362668',
  amount: 30000
})

// 表单规则
const rules = {
  gameId: [
    { required: true, message: '请输入游戏ID', trigger: 'blur' }
  ]
}

// 表单引用
const formRef = ref(null)

// 加载状态
const loading = reactive({
  download: false,
  diamond: false,
  stone: false
})

// 操作历史
const operationHistory = ref([])

// 添加用户数据
const userData = ref(null)
const itemsList = ref([])

// 添加表单数据的响应式对象
const formData = reactive({
  id: '',
  name: '',
  rankingName: '',
  coin: 0,
  diamond: 0,
  exp: 0,
  cup: 0,
  winCount: 0,
  allCount: 0,
  winRate: '0%'
})

// 修改编辑相关的响应式变量
const editDialogVisible = ref(false)
const editingHistory = ref(null)
const editForm = reactive({
  userId: '',
  userName: '',
  time: '',
  operation: '',
  details: ''
})

// 监听 userData 的变化，更新 formData
watch(userData, (newValue) => {
  if (newValue) {
    Object.assign(formData, {
      id: newValue.id || '',
      name: newValue.name || '',
      rankingName: newValue.rankingName || '',
      coin: newValue.coin || 0,
      diamond: newValue.diamond || 0,
      exp: newValue.exp || 0,
      cup: newValue.cup || 0,
      winCount: newValue.winCount || 0,
      allCount: newValue.allCount || 0,
      winRate: newValue.winRate || '0%'
    })
  }
}, { immediate: true })

// 生成一个不重复的大范围sid，并支持范围递增
const generateUniqueSid = (retryCount = 0) => {
  let sid
  do {
    // 根据重试次数增加范围
    const minRange = 1000 + (retryCount * 1000)
    const maxRange = 9999 + (retryCount * 1000)
    sid = Math.floor(Math.random() * (maxRange - minRange + 1)) + minRange
  } while (usedSids.has(sid))
  usedSids.add(sid)
  return sid.toString()
}

// 添加全局的 usedSids Set
const usedSids = new Set()

// 修下载数据方法
const handleDownload = async () => {
  try {
    await formRef.value.validate()
    loading.download = true

    // 构造请求参数
    const params = new URLSearchParams({
      id: form.gameId,
      sign: '75eaf3b033138c7a588027079913ac1b',
      time: Date.now(),
      sid: '3',
      vc: '3',
      sk: 'hdJUGjc9oCtt1098VMj35q75r1sWnXay',
      vk: '1184'
    }).toString()

    // 修改请求路径
    const response = await fetch('/javelin-api/user/userinfo', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'xweb_xhr': '1',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9'
      },
      body: params
    })

    // 添加更详细的错误处理
    if (!response.ok) {
      const errorText = await response.text()
      console.error('Response status:', response.status)
      console.error('Response headers:', response.headers)
      console.error('Response body:', errorText)
      throw new Error(`请求失败: ${response.status} - ${errorText}`)
    }

    const data = await response.json()
    console.log('Response data:', data)

    if (data.code !== 0) {
      throw new Error(data.message || '获取数据失败')
    }

    // 处理返回的数据
    userData.value = {
      id: data.data.id,                    // 用户ID
      name: data.data.nick,                // 用户昵称
      headImg: data.data.head_img,         // 头像URL
      coin: data.data.coin,                // 金币数量
      diamond: data.data.diamond,          // 钻石数量
      cup: data.data.cup,                  // 奖杯数
      exp: data.data.exp,                  // 经验值
      rankingName: data.data.ranking_name, // 段位名称
      rankingStar: data.data.ranking_star, // 段位星星
      winCount: data.data.win_count,       // 胜利次
      allCount: data.data.all_count,       // 总场次
      winRate: data.data.all_count ?
        ((data.data.win_count / data.data.all_count) * 100).toFixed(1) + '%' :
        '0%'  // 胜率
    }

    // 处理英雄数据
    const heroList = data.data.heros.map((heroId, index) => ({
      id: heroId,
      level: data.data.heros_level[index],
      name: getHeroName(heroId)
    }))

    itemsList.value = heroList

    operationHistory.value.unshift({
      time: new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operation: '[系统同步] 数据同步完成',
      details: `用户信息: [ID: ${userData.value.id}] [昵称: ${userData.value.name}]\n` +
        `数据概要: 金币(${userData.value.coin}) | 钻石(${userData.value.diamond}) | 经验石(${userData.value.exp})\n` +
        `段位信息: ${userData.value.rankingName} [胜率: ${userData.value.winRate}]`,
      success: true
    })

    ElMessage.success('数据下载成功')
  } catch (error) {
    console.error('Error:', error) // 添加错误日志
    operationHistory.value.unshift({
      time: new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operation: '[系统错误] 数据同步失败',
      details: `错误信息: ${error.message}\n请求ID: ${form.gameId}`,
      success: false
    })
    ElMessage.error(error.message || '下载失败')
  } finally {
    loading.download = false
  }
}

// 添加获取英雄名称的方法
const getHeroName = (id) => {
  const heroNames = {
    1: '标枪手',
    16: '投石手',
    // ... 可以添加更多英雄名称
  }
  return heroNames[id] || `英雄${id}`
}

// 获取道具名称的方法
const getItemName = (id) => {
  const itemNames = {
    '1': '经验石',
    '2': '钻石',
    // ... 添加更多道具名称映射
  }
  return itemNames[id] || `道具${id}`
}

// 修改充值钻石的处理函数
const handleDiamondRecharge = async () => {
  try {
    await formRef.value.validate()
    loading.diamond = true

    // 如果没有用户数据，创建一个基础对象
    if (!userData.value) {
      userData.value = {
        id: form.gameId,
        name: '未下载用户数据',
        headImg: '',
        diamond: 0,
        exp: 0,
        coin: 0,
        cup: 0,
        winCount: 0,
        allCount: 0,
        winRate: '0%',
        rankingName: '未知段位'
      }
    }

    const targetAmount = form.amount // 用户输入的目标数量
    const requestCount = 1 // 只发送一次请求

    let successCount = 0
    let totalAdded = 0
    let usedSids = new Set()

    try {
      // 构造请求参数，使用用户输入的数量
      const params = new URLSearchParams({
        type: '2', // 钻石类型2
        num: targetAmount.toString(), // 直接使用用户输入的数量
        module: '20',
        id: form.gameId,
        sign: 'cf82deccadcc36a7a2721356d7ff66ff',
        time: Date.now(),
        sid: generateUniqueSid(), // 使用生成的唯一sid
        vc: '3',
        sk: 'hdJUGjc9oCtt1098VMj35q75r1sWnXay',
        vk: '1184'
      }).toString()

      const response = await fetch('/javelin-api/add/add', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'xweb_xhr': '1',
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        },
        body: params
      })

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`)
      }

      const result = await response.json()
      if (result.code !== 0) {
        throw new Error(result.message || '添加失败')
      }

      successCount++
      totalAdded += targetAmount

      // 成功后更新用户数据中的钻石数量
      if (userData.value) {
        userData.value.diamond += targetAmount
        // 显示数据区域
        document.querySelector('.data-section').style.display = 'block'
      }

      // 修改成功记录的显示
      operationHistory.value.unshift({
        time: new Date().toLocaleTimeString('zh-CN', {
          hour12: false,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        operation: `[充值系统] 钻石充值成功`,
        details: `用户信息: [ID: ${form.gameId}] [昵称: ${userData.value?.name || '未知'}]\n` +
          `充值数量: ${targetAmount} 钻石\n` +
          `当前余额: ${userData.value?.diamond || targetAmount} 钻石\n` +
          `充值时间: ${new Date().toLocaleString()}`,
        success: true
      })

    } catch (error) {
      console.error(`第 1 次请求失败:`, error)
      operationHistory.value.unshift({
        time: new Date().toLocaleTimeString('zh-CN', {
          hour12: false,
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit'
        }),
        operation: `[充值系统] 钻石充值失败`,
        details: `用户信息: [ID: ${form.gameId}] [昵称: ${userData.value?.name || '未知'}]\n` +
          `失败信息: ${error.message}\n` +
          `当进度: 已充值 ${totalAdded}/${targetAmount}`,
        success: false
      })
    }

    // 最终结果记录
    operationHistory.value.unshift({
      time: new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operation: '[充值系统] 钻石充值完成',
      details: `用户信息: [ID: ${form.gameId}] [昵称: ${userData.value?.name || '未知'}]\n` +
        `充值结果: 成功 ${successCount}/${requestCount} 次\n` +
        `充值数量: ${totalAdded}/${targetAmount} 钻石\n` +
        `当前余额: ${userData.value?.diamond || totalAdded} 钻石`,
      success: successCount > 0
    })

    if (successCount === requestCount) {
      ElMessage.success(`钻石充值成功: ${totalAdded}`)
    } else if (successCount > 0) {
      ElMessage.warning(`部分充值成功: ${totalAdded}/${targetAmount}`)
    } else {
      throw new Error('所有请求失败了')
    }

  } catch (error) {
    console.error('Error:', error)
    operationHistory.value.unshift({
      time: new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operation: '[充值系统] 钻石充值失败',
      details: `错误信息: ${error.message}\n用户ID: ${form.gameId}`,
      success: false
    })
    ElMessage.error(error.message || '充值失败')
  } finally {
    loading.diamond = false
  }
}

// 修改抽奖经验石的处理函数
const handleExpStone = async () => {
  try {
    await formRef.value.validate()
    loading.stone = true

    // 如果没有用户数据，创建一个基础对象
    if (!userData.value) {
      userData.value = {
        id: form.gameId,
        name: '未下载用户数据',
        headImg: '',
        diamond: 0,
        exp: 0,
        coin: 0,
        cup: 0,
        winCount: 0,
        allCount: 0,
        winRate: '0%',
        rankingName: '未知段位'
      }
    }

    const targetAmount = form.amount
    const requestCount = 1
    let retryCount = 0
    const maxRetries = 3
    let success = false
    let successCount = 0
    let totalAdded = 0

    while (!success && retryCount < maxRetries) {
      try {
        // 构造请求参数
        const params = new URLSearchParams({
          type: '1000',
          num: targetAmount.toString(),
          module: '20',
          id: form.gameId,
          sign: '4c05906f06ddfa63f1858c7744fd2732',
          time: Date.now(),
          sid: generateUniqueSid(retryCount),
          vc: '3',
          sk: 'hdJUGjc9oCtt1098VMj35q75r1sWnXay',
          vk: '1184'
        }).toString()

        const response = await fetch('/javelin-api/add/add', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'xweb_xhr': '1',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9'
          },
          body: params
        })

        if (!response.ok) {
          throw new Error(`请求失败: ${response.status}`)
        }

        const result = await response.json()

        if (result.code === 504 && result.msg === 'sid error') {
          // 记录 sid 错误重试信息
          operationHistory.value.unshift({
            time: new Date().toLocaleTimeString('zh-CN', {
              hour12: false,
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            }),
            operation: `[道具系统] SID错误重试 (${retryCount + 1}/${maxRetries})`,
            details: `用户信息: [ID: ${userData.value?.id}] [昵称: ${userData.value?.name}]\n` +
              `重试次数: ${retryCount + 1}\n` +
              `使用SID范围: ${1000 + (retryCount * 1000)}-${9999 + (retryCount * 1000)}`,
            success: false
          })

          retryCount++
          await new Promise(resolve => setTimeout(resolve, 500)) // 重试前等待
        } else if (result.code !== 0) {
          throw new Error(result.message || '添加失败')
        } else {
          // 请求成功
          success = true
          successCount++
          totalAdded += targetAmount

          // 更新用户数据
          if (userData.value) {
            userData.value.exp += targetAmount
            document.querySelector('.data-section').style.display = 'block'
          }

          // 记录成功信息
          operationHistory.value.unshift({
            time: new Date().toLocaleTimeString('zh-CN', {
              hour12: false,
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            }),
            operation: `[道具系统] 经验石发放成功`,
            details: `用户信息: [ID: ${form.gameId}] [昵称: ${userData.value?.name || '未知'}]\n` +
              `发放数量: ${targetAmount} 经验石\n` +
              `当前库存: ${userData.value?.exp || totalAdded} 经验石\n` +
              `发放时间: ${new Date().toLocaleString()}`,
            success: true
          })
        }
      } catch (error) {
        console.error(`请求失败 (重试 ${retryCount}):`, error)
        retryCount++

        if (retryCount >= maxRetries) {
          // 记录最终失败
          operationHistory.value.unshift({
            time: new Date().toLocaleTimeString('zh-CN', {
              hour12: false,
              year: 'numeric',
              month: '2-digit',
              day: '2-digit',
              hour: '2-digit',
              minute: '2-digit',
              second: '2-digit'
            }),
            operation: `[道具系统] 经验石发放失败`,
            details: `用户信息: [ID: ${userData.value?.id}] [昵称: ${userData.value?.name}]\n` +
              `失败信息: ${error.message}\n` +
              `重试次数: ${retryCount}/${maxRetries}`,
            success: false
          })
        }

        await new Promise(resolve => setTimeout(resolve, 500)) // 重试前等待
      }
    }

    // 修改最终结果记录
    operationHistory.value.unshift({
      time: new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operation: '[道具系统] 经验石发放完成',
      details: `用户信息: [ID: ${userData.value?.id}] [昵称: ${userData.value?.name}]\n` +
        `发放结果: 成功 ${successCount}/${requestCount} 次\n` +
        `目标数量: ${targetAmount} 经验石\n` +
        `实际发放: ${successCount * targetAmount} 经验石\n` +
        `当前库存: ${userData.value?.exp || 0} 经验石`,
      success: successCount > 0
    })

    // 修改成功提示信息
    if (successCount === requestCount) {
      ElMessage.success(`经验石添加成功: ${successCount * targetAmount}`)
    } else {
      throw new Error('请求失败')
    }

  } catch (error) {
    console.error('Error:', error)
    operationHistory.value.unshift({
      time: new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }),
      operation: '[道具系统] 经验石发放失败',
      details: `错误信息: ${error.message}\n用户ID: ${form.gameId}`,
      success: false
    })
    ElMessage.error(error.message || '添加失败')
  } finally {
    loading.stone = false
  }
}

// 添加计算属性
const hasRechargeHistory = computed(() => {
  return operationHistory.value.length > 0
})

// 添加数据显示状态
const showDataSection = computed(() => {
  return userData.value || hasRechargeHistory.value
})

// 修改编辑相关的方法
const handleEdit = (history, index) => {
  editingHistory.value = { ...history, index }
  editForm.userId = history.userId || userData.value?.id || ''
  editForm.userName = history.userName || userData.value?.name || ''
  editForm.time = history.time
  editForm.operation = history.operation
  editForm.details = history.details
  editDialogVisible.value = true
}

const handleSaveEdit = () => {
  if (editingHistory.value) {
    const index = editingHistory.value.index
    operationHistory.value[index] = {
      ...operationHistory.value[index],
      userId: editForm.userId,
      userName: editForm.userName,
      time: editForm.time,
      operation: editForm.operation,
      details: editForm.details
    }
  }
  editDialogVisible.value = false
}

// 修改添加操作记录的方法，确保包含用户ID和昵称
const addOperationHistory = (operation, details, success = true) => {
  operationHistory.value.unshift({
    time: new Date().toLocaleString('zh-CN', {
      hour12: false,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }),
    userId: userData.value?.id || form.gameId,
    userName: userData.value?.name || '未知用户',
    operation,
    details,
    success
  })
}
</script>

<style scoped>
.javelin-king {
  padding: 20px;
  height: calc(100vh - 84px);
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
  box-sizing: border-box;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.javelin-king::-webkit-scrollbar {
  display: none;
}

.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  height: auto;
  min-height: calc(100vh - 104px);
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: -20px -20px 24px -20px;
  background: linear-gradient(135deg, #FF9966 0%, #FF5E62 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: white;
}

.title .el-icon {
  font-size: 24px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 8px;
  backdrop-filter: blur(4px);
}

.status-tag {
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  font-weight: 500;
}

.input-section {
  padding: 20px;
}

.action-section {
  padding: 0 20px 20px;
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-button {
  flex: 1;
  min-width: 120px;
  max-width: 200px;
}

.history-section {
  margin-top: 20px;
  padding: 20px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  max-height: 500px;
  /* 设置最大高度 */
  overflow-y: auto;
  /* 启用垂直滚动 */
}

.history-section::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  display: block;
  /* 显示滚动条 */
}

.history-section::-webkit-scrollbar-thumb {
  background: #909399;
  border-radius: 3px;
  cursor: pointer;
}

.history-section::-webkit-scrollbar-thumb:hover {
  background: #606266;
}

.history-section::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 3px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .javelin-king {
    padding: 12px;
    height: calc(100vh - 64px);
  }

  .header-section {
    padding: 16px;
  }

  .input-section,
  .action-section,
  .history-section {
    padding: 12px;
  }

  .action-button {
    flex: 1 1 calc(50% - 6px);
    min-width: 0;
    max-width: none;
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 480px) {
  .action-button {
    flex: 1 1 100%;
  }
}

/* 数据显示区域样式 */
.data-section {
  padding: 20px;
  margin-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.data-section::-webkit-scrollbar {
  display: none;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  display: none;
}

:deep(.el-table__body-wrapper) {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

:deep(.el-tabs__content) {
  padding: 20px;
  -ms-overflow-style: none;
  scrollbar-width: none;
}

:deep(.el-tabs__content::-webkit-scrollbar) {
  display: none;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .data-section {
    padding: 12px;
  }

  :deep(.el-tabs__content) {
    padding: 12px;
  }

  :deep(.el-col) {
    margin-bottom: 12px;
  }
}

/* 在 style 部分添加头像相关样式 */
.user-profile {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
}

.user-avatar {
  border: 4px solid white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
  transition: transform 0.3s ease;
}

.user-avatar:hover {
  transform: scale(1.05);
}

.user-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .user-profile {
    padding: 12px;
    margin-bottom: 16px;
  }

  .user-avatar {
    width: 60px;
    height: 60px;
  }

  .user-title {
    font-size: 16px;
  }
}

/* 更新操作记录式 */
.history-content {
  padding: 12px 16px;
  background: var(--el-bg-color);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--el-border-color-lighter);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.history-user {
  font-size: 13px;
  color: var(--el-text-color-secondary);
  font-family: 'Consolas', monospace;
}

.history-operation {
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 6px;
  font-family: 'Consolas', monospace;
}

.history-details {
  font-size: 13px;
  color: var(--el-text-color-regular);
  font-family: 'Consolas', monospace;
  padding: 8px 12px;
  background: var(--el-fill-color-light);
  border-radius: 6px;
  margin-top: 6px;
  white-space: pre-line;
  line-height: 1.5;
}

:deep(.el-timeline-item__content) {
  padding-bottom: 24px;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  font-family: 'Consolas', monospace;
  color: var(--el-text-color-secondary);
}

:deep(.el-timeline-item__node) {
  width: 12px;
  height: 12px;
}

:deep(.el-timeline-item__tail) {
  left: 5px;
  border-left: 2px solid var(--el-border-color-lighter);
}

/* 修改操作记录区域样式 */
.history-section {
  margin-top: 20px;
  padding: 20px;
  background: var(--el-fill-color-light);
  border-radius: 8px;
  border: 1px solid var(--el-border-color-lighter);
  max-height: 500px;
  /* 设置最大高度 */
  overflow-y: auto;
  /* 启用垂直滚动 */
}

/* 自定义滚动条样式 */
.history-section::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  display: block;
  /* 显示滚动条 */
}

.history-section::-webkit-scrollbar-thumb {
  background: #909399;
  border-radius: 3px;
  cursor: pointer;
}

.history-section::-webkit-scrollbar-thumb:hover {
  background: #606266;
}

.history-section::-webkit-scrollbar-track {
  background: #f0f2f5;
  border-radius: 3px;
}

/* 修改时间线样式以适应滚动 */
:deep(.el-timeline) {
  padding-right: 10px;
  /* 为滚动条出空间 */
}

:deep(.el-timeline-item__tail) {
  left: 5px;
  border-left: 2px solid var(--el-border-color-lighter);
}

:deep(.el-timeline-item__node) {
  width: 12px;
  height: 12px;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .history-section {
    max-height: 400px;
    /* 移动端稍微降低最大高度 */
    padding: 12px;
  }
}

/* 添加提示信息样式 */
.amount-tips {
  margin-top: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.tip-item {
  line-height: 1.5;
  display: flex;
  align-items: center;
  gap: 4px;
}

.extra-amount {
  color: var(--el-color-success);
}

:deep(.el-input-number__suffix) {
  color: var(--el-text-color-secondary);
  cursor: help;
}

/* 添加数据状态标签样式 */
.data-status {
  margin-left: 12px;
  font-size: 12px;
}

/* 确保数据区域的显示和隐藏有渡效果 */
.data-section {
  transition: opacity 0.3s ease;
  opacity: 1;
}

.data-section:not([style*="display: none"]) {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 添加编辑相关样式 */
.history-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.history-operation {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-tag--info) {
  border-style: dashed;
}

.dialog-footer {
  padding-top: 20px;
  text-align: right;
  display: block;
}
</style>