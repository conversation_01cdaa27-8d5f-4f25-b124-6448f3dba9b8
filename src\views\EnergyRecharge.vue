<template>
  <div class="energy-recharge">
    <h2>体力充值</h2>
    <RechargeForm @recharge="handleRecharge" :rechargeType="'energy'" />
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import RechargeForm from '@/components/RechargeForm.vue'

export default {
  name: 'EnergyRecharge',
  components: {
    RechargeForm
  },
  methods: {
    ...mapActions('recharge', ['recharge']),
    async handleRecharge(rechargeInfo) {
      try {
        await this.recharge(rechargeInfo)
        console.log('体力充值成功:', rechargeInfo)
      } catch (error) {
        console.error('体力充值失败:', error)
      }
    }
  }
}
</script>
