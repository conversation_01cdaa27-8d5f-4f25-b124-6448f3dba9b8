<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="slow-piggy-packages">
      <div class="page-header">
        <h2>慢豚豚的生活套餐</h2>
        <div class="subtitle">微信小程序 | 快速到账 | 稳定防封</div>
      </div>

      <div class="package-list">
        <div v-for="(pkg, index) in packages" :key="index"
          :class="['package-card', pkg.recommended ? 'highlight' : '']">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon" :class="pkg.iconClass">
                <component :is="pkg.icon" />
              </el-icon>
              <span class="package-name">{{ pkg.name }}</span>
            </div>
            <el-tag :type="pkg.recommended ? 'danger' : 'success'" effect="dark">{{ pkg.recommended ? '热销推荐' : '实惠'
            }}</el-tag>
          </div>

          <div class="package-content">
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="amount">{{ pkg.price }}</span>
              <div v-if="pkg.discount" class="discount-badge">限时</div>
            </div>
            <div class="benefits-section">
              <div class="benefit-item">
                <template v-for="(item, itemIndex) in pkg.contents" :key="itemIndex">
                  <span v-if="itemIndex === 0" class="highlight-amount">{{ item.name }}</span>
                  <template v-else>
                    <span class="separator">|</span>
                    <span class="other-benefits">{{ item.name }}</span>
                  </template>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="footer-notes">
        <div class="note-item">
          <el-icon>
            <Warning />
          </el-icon>
          <p>目前0封号，如封号可换号另补或换店里游戏免费补一次，介意勿拍！！！</p>
        </div>
        <div class="note-item">
          <el-icon>
            <InfoFilled />
          </el-icon>
          <p>不支持仅退款</p>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref } from 'vue'
import { Food, Warning, InfoFilled, Lightning, Coin, Money, Star } from '@element-plus/icons-vue'

const packages = ref([
  {
    name: '套餐1号',
    icon: Lightning,
    iconClass: 'icon-energy',
    contents: [
      { name: '无限体力' },
      { name: '无限金币' }
    ],
    price: 6,
    recommended: false,
    discount: false
  },
  {
    name: '套餐2号',
    icon: Coin,
    iconClass: 'icon-coin',
    contents: [
      { name: '无限体力' },
      { name: '无限金币' },
      { name: '无限红钞' }
    ],
    price: 15,
    recommended: false,
    discount: false
  },
  {
    name: '全打包3号',
    icon: Star,
    iconClass: 'icon-vip',
    contents: [
      { name: '无限体力' },
      { name: '无限金币' },
      { name: '无限红钞' },
      { name: '无限精力' }
    ],
    price: 20,
    recommended: true,
    discount: true
  }
]);

const tableRowClassName = ({ row, rowIndex }) => {
  if (rowIndex === 2) {
    return 'highlight-row';
  }
  return '';
};
</script>

<style scoped>
.slow-piggy-packages {
  padding: 12px;
  background: #f5f7fa;
  min-height: 100%;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  color: #303133;
}

.page-header h2 {
  font-size: 24px;
  margin-bottom: 8px;
  background: linear-gradient(45deg, #FF6B6B, #FF9A9E);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  color: #909399;
  font-size: 14px;
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 800px;
  margin: 0 auto;
}

.package-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.package-card.highlight {
  border: 2px solid #FF6B6B;
  background: linear-gradient(to right, #ffffff, #fff0f0);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.package-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.package-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.price-section {
  display: flex;
  align-items: baseline;
  min-width: 80px;
  position: relative;
}

.currency {
  font-size: 14px;
  color: #FF6B6B;
  margin-right: 2px;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  color: #FF6B6B;
}

.discount-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #E6A23C;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  transform: scale(0.8);
}

.benefits-section {
  flex: 1;
}

.benefit-item {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
}

.highlight-amount {
  color: #67c23a;
  font-weight: bold;
  font-size: 16px;
}

.separator {
  margin: 0 8px;
  color: #dcdfe6;
}

.other-benefits {
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
}

.package-card.highlight .highlight-amount {
  background: linear-gradient(45deg, #67c23a, #95d475);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 17px;
}

.footer-notes {
  margin-top: 20px;
  padding: 15px;
  background-color: #F8F9FA;
  border-radius: 8px;
  color: #909399;
  font-size: 13px;
  line-height: 1.6;
}

.note-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
}

.note-item:last-child {
  margin-bottom: 0;
}

.note-item .el-icon {
  color: #F56C6C;
  font-size: 16px;
  flex-shrink: 0;
  margin-top: 2px;
}

.note-item p {
  margin: 0;
}

/* 添加滚动条样式 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
}

.name-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.package-icon {
  font-size: 20px;
  padding: 6px;
  border-radius: 8px;
  transition: all 0.3s;
}

/* 图标样式 */
.icon-energy {
  background: #ecf5ff;
  color: #409EFF;
}

.icon-coin {
  background: #f0f9eb;
  color: #67c23a;
}

.icon-vip {
  background: linear-gradient(135deg, #FF9A9E, #FF6B6B);
  color: white;
}

/* 悬浮效果 */
.package-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.package-card:hover .package-icon {
  transform: scale(1.1) rotate(5deg);
}

@media (max-width: 768px) {
  .slow-piggy-packages {
    padding: 8px;
  }

  .page-header h2 {
    font-size: 20px;
  }

  .package-card {
    padding: 12px;
  }

  .package-name {
    font-size: 15px;
  }

  .amount {
    font-size: 20px;
  }

  .benefit-item {
    font-size: 13px;
  }

  /* 确保内容在一行显示 */
  .package-content {
    flex-wrap: nowrap;
  }

  /* 价格部分固定宽度 */
  .price-section {
    min-width: 60px;
  }

  /* 好处描述部分自适应 */
  .benefits-section {
    flex: 1;
    min-width: 0;
    /* 确保文本可以正确截断 */
  }

  .highlight-amount {
    font-size: 15px;
  }

  .package-card.highlight .highlight-amount {
    font-size: 16px;
  }

  .separator {
    margin: 0 4px;
  }

  .other-benefits {
    font-size: 13px;
  }

  .package-icon {
    font-size: 18px;
    padding: 4px;
  }

  .name-with-icon {
    gap: 6px;
  }
}
</style>