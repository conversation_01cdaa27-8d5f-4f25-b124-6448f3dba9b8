<template>
  <div class="small-shelter">
    <el-scrollbar height="calc(100vh - 120px)">
      <div class="content-wrapper">
        <el-row :gutter="20">
          <!-- 修改左侧列宽度为16 -->
          <el-col :span="16">
            <el-card class="box-card">
              <template #header>
                <div class="card-header">
                  <el-icon><House /></el-icon>
                  <span>小小庇护所</span>
                </div>
              </template>
              <GameIdForm ref="gameIdFormRef" 
                @update:operationHistory="updateOperationHistory" 
                @loading="setLoading" />
            </el-card>
          </el-col>

          <!-- 修改右侧列宽度为8 -->
          <el-col :span="8">
            <!-- 快捷操作卡片 -->
            <el-card class="box-card quick-actions-card">
              <template #header>
                <div class="card-header">
                  <el-icon><MagicStick /></el-icon>
                  <span>快捷操作</span>
                </div>
              </template>
              <div class="quick-actions">
                <div v-for="action in quickActions" 
                  :key="action.name"
                  class="quick-action-card"
                  :class="action.type"
                  @click="handleQuickAction(action)">
                  <el-tooltip 
                    :content="action.description" 
                    placement="top" 
                    effect="light">
                    <div class="action-content">
                      <el-icon class="action-icon">
                        <component :is="action.icon" />
                      </el-icon>
                      <div class="action-info">
                        <span class="action-name">{{ action.name }}</span>
                        <span v-if="action.action === 'shelter1'" class="action-desc">
                          💰无限金币 + 💎无限钻石
                        </span>
                      </div>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </el-card>

            <!-- 操作历史卡片 -->
            <el-card class="box-card history-card">
              <template #header>
                <div class="card-header">
                  <el-icon><Star /></el-icon>
                  <span>操作历史</span>
                </div>
              </template>
              <RechargeHistory :initialData="operationHistory" />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref, reactive, markRaw } from 'vue'
import GameIdForm from '@/components/ShelterGameIdForm.vue'
import RechargeHistory from '@/components/RechargeHistory.vue'
import { 
  House, 
  Star, 
  MagicStick,
  Timer,
  Trophy,
  Medal,
  Stamp
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import md5 from 'md5'

const operationHistory = ref([])
const isLoading = ref(false)
const gameIdFormRef = ref(null)

// 快捷操作列表
const quickActions = reactive([
  { 
    name: '庇护套餐1', 
    action: 'shelter1',
    icon: markRaw(Timer),
    type: 'blue',
    description: '💰无限金币 + 💎无限钻石'
  },
  { 
    name: '庇护套餐2', 
    action: 'shelter2',
    icon: markRaw(Trophy),
    type: 'green',
    description: '💰无限金币 + 💎无限钻石 + 🎁无限免广卷礼盒'
  },
  { 
    name: '庇护套餐3', 
    action: 'shelter3',
    icon: markRaw(Medal),
    type: 'orange', 
    description: '💰无限金币 + 💎无限钻石 + 🎁无限免广卷礼盒 + 🎭无限英雄碎片'
  },
  { 
    name: '赠送体力', 
    action: 'energy',
    icon: markRaw(Stamp),
    type: 'purple',
    description: '⚡体力值设置为200'
  }
])

// 修改处理快捷操作的方法
const handleQuickAction = async (action) => {
  // 所有操作都需要检查游戏ID
  if (!gameIdFormRef.value?.gameData?.gameId) {
    ElMessage.warning('请先输入游戏ID')
    return
  }

  try {
    isLoading.value = true

    switch(action.action) {
      case 'shelter1':
        // 切换到经济数据标签页
        gameIdFormRef.value.switchToEconomicTab()
        
        // 设置无限金币和钻石
        const success = gameIdFormRef.value.setEconomicData(99999999, 99999999)
        if (!success) {
          throw new Error('设置数据失败，请先解析游戏ID')
        }
        break
      case 'shelter2':
        gameIdFormRef.value.switchToEconomicTab()
        // 设置无限金币、钻石和免广卷礼盒
        const success2 = gameIdFormRef.value.setEconomicData(99999999, 99999999, 99999999)
        if (!success2) {
          throw new Error('设置数据失败，请先解析游戏ID')
        }
        break
      case 'shelter3':
        gameIdFormRef.value.switchToEconomicTab()
        // 设置无限金币、钻石、免广卷礼盒和英雄碎片
        const success3 = gameIdFormRef.value.setEconomicData(99999999, 99999999, 99999999, true)
        if (!success3) {
          throw new Error('设置数据失败，请先解析游戏ID')
        }
        break
      case 'energy':
        // 切换到能量管理标签页
        gameIdFormRef.value.switchToEnergyTab()
        // 设置体力值为200
        const success4 = gameIdFormRef.value.setEnergyData(200)
        if (!success4) {
          throw new Error('设置数据失败，请先解析游戏ID')
        }
        break
    }

    // 更新游戏数据
    await gameIdFormRef.value.updateGame()
    
    // 添加操作历史
    const newOperation = {
      time: new Date().toLocaleString(),
      function: action.name,
      status: '成功'
    }
    operationHistory.value.unshift(newOperation)
    
    ElMessage.success(`${action.name}成功`)
  } catch (error) {
    console.error('快捷操作失败:', error)
    ElMessage.error(`${action.name}失败: ${error.message}`)
  } finally {
    isLoading.value = false
  }
}

// 更新操作历史
const updateOperationHistory = (newHistory) => {
  operationHistory.value = [...newHistory, ...operationHistory.value].slice(0, 50)
}

const setLoading = (loading) => {
  isLoading.value = loading
}
</script>

<style scoped>
.content-wrapper {
  padding: 20px;
  min-width: 1200px; /* 设置最小宽度 */
}

.box-card {
  margin-bottom: 20px;
}

:deep(.el-card__body) {
  padding: 15px;
  min-width: fit-content; /* 确保内容不会被挤压 */
}

.quick-actions-card {
  margin-bottom: 20px;
}

.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 10px;
}

.quick-action-card {
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 12px 15px;
  height: 60px;
  display: flex;
  align-items: center;
}

.action-content {
  display: flex;
  align-items: center;
  color: white;
  gap: 12px;
  width: 100%;
}

.action-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.action-icon {
  font-size: 24px;
}

.action-name {
  font-size: 16px;
  font-weight: 500;
  line-height: 1.2;
}

.action-desc {
  font-size: 13px;
  opacity: 0.9;
  white-space: nowrap;
}

/* 套餐卡片样式 */
.quick-action-card.blue {
  background: linear-gradient(135deg, #36D1DC, #5B86E5);
}

.quick-action-card.green {
  background: linear-gradient(135deg, #11998e, #38ef7d);
}

.quick-action-card.orange {
  background: linear-gradient(135deg, #FF8008, #FFC837);
}

.quick-action-card.purple {
  background: linear-gradient(135deg, #834d9b, #d04ed6);
}

.quick-action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* 响应式布局 */
@media (max-width: 768px) {
  .quick-actions {
    grid-template-columns: 1fr;
  }
  
  .quick-action-card {
    height: 50px;
  }
}
</style>
