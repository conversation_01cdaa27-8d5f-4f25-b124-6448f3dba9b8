<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="oracle-war-packages">
      <div class="page-header">
        <h2>甲骨文战争套餐</h2>
        <div class="subtitle">V小程序可充 | 充你的号上 |</div>
      </div>

      <div class="package-list">
        <!-- 套餐1 -->
        <div class="package-card">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon icon-starter">
                <Collection />
              </el-icon>
              <span class="package-name">套餐一</span>
            </div>
            <el-tag type="info" effect="dark">入门</el-tag>
          </div>

          <div class="package-content">
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="amount">10</span>
            </div>
            <div class="benefits-section">
              <div class="benefit-item">
                <span class="highlight-amount">元宝20亿</span>
                <span class="separator">+</span>
                <span class="highlight-amount">神珠999</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 套餐2 -->
        <div class="package-card">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon icon-advanced">
                <Suitcase />
              </el-icon>
              <span class="package-name">套餐二</span>
            </div>
            <el-tag type="success" effect="dark">实惠</el-tag>
          </div>

          <div class="package-content">
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="amount">20</span>
            </div>
            <div class="benefits-section">
              <div class="benefit-item">
                <span class="highlight-amount">元宝20亿</span>
                <span class="separator">+</span>
                <span class="highlight-amount">神珠999</span>
              </div>
              <div class="benefit-item detail">
                <span class="detail-benefits">练兵符99,999 | 广告卷9,999</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 套餐3 -->
        <div class="package-card highlight">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon icon-supreme">
                <Trophy />
              </el-icon>
              <span class="package-name">套餐三</span>
            </div>
            <el-tag type="danger" effect="dark">推荐</el-tag>
          </div>

          <div class="package-content">
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="amount">30</span>
            </div>
            <div class="benefits-section">
              <div class="benefit-item">
                <span class="highlight-amount">元宝20亿</span>
                <span class="separator">+</span>
                <span class="highlight-amount">神珠999</span>
              </div>
              <div class="benefit-item detail">
                <span class="detail-benefits">练兵符99,999 | 广告卷9,999</span>
              </div>
              <div class="benefit-item detail">
                <span class="detail-benefits">妖王挑战卷9,999 | 每日挑战卷9,999</span>
              </div>
              <div class="benefit-item detail">
                <span class="detail-benefits">换一批9,999 | VIP等级满级15</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-notice">
        套餐目前可刷，如后续查查可免费换号补或换店内其他游戏，下单默认同意，不退款
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref } from 'vue'
import {
  Trophy, Collection, Suitcase
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
</script>

<style scoped>
.oracle-war-packages {
  padding: 12px;
  background: #f5f7fa;
  min-height: 100%;
}

.page-header {
  text-align: center;
  margin-bottom: 24px;
  color: #303133;
}

.page-header h2 {
  font-size: 36px;
  margin-bottom: 12px;
  background: linear-gradient(45deg, #2B5DED, #22D3EE);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 900;
  letter-spacing: 2px;
  text-shadow: 0 0 1px rgba(43, 93, 237, 0.1);
}

.subtitle {
  color: #4B5563;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  padding: 0 12px;
}

.package-card {
  background: white;
  border-radius: 20px;
  padding: 28px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.package-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.15);
}

.package-card.highlight {
  border: 3px solid #2B5DED;
  background: linear-gradient(to right, #ffffff, #EEF2FF);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.name-with-icon {
  display: flex;
  align-items: center;
  gap: 12px;
}

.package-icon {
  font-size: 28px;
  padding: 12px;
  border-radius: 14px;
}

.icon-starter {
  background: #EEF2FF;
  color: #2B5DED;
}

.icon-advanced {
  background: #ECFDF5;
  color: #059669;
}

.icon-supreme {
  background: linear-gradient(45deg, #2B5DED, #22D3EE);
  color: white;
}

.package-name {
  font-size: 24px;
  font-weight: 800;
  color: #1F2937;
  letter-spacing: 0.5px;
}

.package-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
}

.price-section {
  display: flex;
  align-items: baseline;
  min-width: 100px;
}

.currency {
  font-size: 20px;
  color: #2B5DED;
  margin-right: 4px;
  font-weight: 800;
}

.amount {
  font-size: 36px;
  font-weight: 900;
  background: linear-gradient(45deg, #2B5DED, #22D3EE);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.benefits-section {
  flex: 1;
}

.benefit-item {
  display: flex;
  align-items: center;
  color: #4B5563;
  font-size: 18px;
  white-space: nowrap;
  overflow: hidden;
  margin-bottom: 10px;
  font-weight: 600;
}

.benefit-item.detail {
  padding-left: 16px;
  margin-bottom: 6px;
  font-size: 16px;
  color: #6B7280;
  font-weight: 500;
}

.highlight-amount {
  color: #DC2626;
  font-weight: 800;
  font-size: 20px;
  background: linear-gradient(45deg, #DC2626, #FB7185);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: 0.5px;
}

.separator {
  margin: 0 14px;
  color: #9CA3AF;
  font-size: 20px;
  font-weight: 600;
}

.detail-benefits {
  color: #4B5563;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.3px;
}

.package-card.highlight .highlight-amount {
  background: linear-gradient(45deg, #DC2626, #FB7185);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 22px;
  font-weight: 900;
}

.bottom-notice {
  text-align: center;
  font-size: 16px;
  color: #4B5563;
  padding: 16px;
  font-weight: 600;
  letter-spacing: 0.3px;
  background: #F3F4F6;
  border-radius: 12px;
  margin: 0 12px;
}

@media (max-width: 768px) {
  .oracle-war-packages {
    padding: 12px;
  }

  .page-header h2 {
    font-size: 32px;
  }

  .subtitle {
    font-size: 16px;
  }

  .package-card {
    padding: 20px;
  }

  .package-name {
    font-size: 20px;
  }

  .amount {
    font-size: 32px;
  }

  .highlight-amount {
    font-size: 18px;
  }

  .benefit-item {
    font-size: 16px;
  }

  .benefit-item.detail {
    font-size: 14px;
  }

  .detail-benefits {
    font-size: 14px;
  }
}
</style>
