<template>
  <div class="promotion-container">
    <div class="promotion-content">
      <div class="header">
        <h1 class="title">三国争霸自走棋</h1>
        <div class="subtitle">小程序</div>
      </div>

      <div class="packages">
        <div class="package standard-package">
          <div class="package-name">套餐1</div>
          <div class="package-content">21亿元宝</div>
          <div class="package-price">18元</div>
        </div>

        <div class="package standard-package">
          <div class="package-name">套餐2</div>
          <div class="package-content">21亿功勋</div>
          <div class="package-price">18元</div>
        </div>

        <div class="package standard-package">
          <div class="package-name">套餐3</div>
          <div class="package-content">21亿换一批</div>
          <div class="package-price">18元</div>
        </div>

        <div class="package ultimate-package">
          <div class="package-name">终极套餐</div>
          <div class="package-content">
            <ul class="content-list">
              <li>✦ 21亿元宝</li>
              <li>✦ 21亿功勋</li>
              <li>✦ 21亿换一批</li>
              <li>✦ 1亿功勋点</li>
              <li>✦ 4款技能书各1亿</li>
              <li>✦ VIP12级</li>
              <li>✦ 解锁8个英雄</li>
            </ul>
          </div>
          <div class="package-price">50元</div>
        </div>
      </div>

      <div class="contact-info">
        <div class="contact-text">联系客服购买</div>
        <div class="wechat">微信: Game_Support</div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 没有特定的脚本逻辑
</script>

<style scoped>
.promotion-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1c1f2b 0%, #2c3e50 100%);
  padding: 20px;
  box-sizing: border-box;
  color: white;
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

.promotion-content {
  width: 100%;
  max-width: 500px;
  /* 控制最大宽度，适应手机屏幕 */
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.header {
  text-align: center;
  margin-bottom: 10px;
}

.title {
  font-size: 36px;
  font-weight: bold;
  margin: 0;
  color: #f8bb86;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 22px;
  margin-top: 5px;
  color: #FFD700;
}

.packages {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.package {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(5px);
  transition: transform 0.3s, box-shadow 0.3s;
}

.package:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.standard-package {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  align-items: center;
}

.ultimate-package {
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, rgba(218, 112, 214, 0.2) 0%, rgba(255, 69, 0, 0.2) 100%);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

.package-name {
  font-size: 24px;
  font-weight: bold;
  color: #f8bb86;
}

.package-content {
  font-size: 20px;
  color: #ffffff;
}

.package-price {
  font-size: 24px;
  font-weight: bold;
  color: #00e676;
  text-align: right;
}

.content-list {
  list-style-type: none;
  padding: 0;
  margin: 10px 0;
}

.content-list li {
  font-size: 20px;
  margin-bottom: 5px;
  color: #ffffff;
  display: flex;
  align-items: center;
}

.contact-info {
  text-align: center;
  margin-top: 15px;
  font-size: 20px;
  color: #f8bb86;
}

.contact-text {
  margin-bottom: 5px;
}

.wechat {
  font-weight: bold;
  color: white;
}

/* 响应式调整 */
@media (max-width: 400px) {
  .promotion-content {
    gap: 15px;
  }

  .title {
    font-size: 32px;
  }

  .subtitle {
    font-size: 20px;
  }

  .package-name,
  .package-price {
    font-size: 22px;
  }

  .package-content,
  .content-list li {
    font-size: 18px;
  }

  .contact-info {
    font-size: 18px;
  }
}
</style>