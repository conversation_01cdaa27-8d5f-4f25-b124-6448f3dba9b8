<template>
  <div class="game-id-form" ref="formRef">
    <el-form :model="form" label-position="top" class="game-info-form">
      <el-form-item label="游戏 ID">
        <div class="input-container">
          <el-input v-model="form.gameId" placeholder="请输入游戏ID" class="game-id-input">
            <template #prefix>
              <el-icon>
                <User />
              </el-icon>
            </template>
          </el-input>
        </div>
      </el-form-item>
      <div class="button-container">
        <el-button type="primary" @click="downloadData" icon="Download" :loading="isLoading.download">
          {{ isLoading.download ? '下载中...' : '下载数据' }}
        </el-button>
        <el-button type="success" @click="updateGame" icon="Refresh" :loading="isLoading.update"
          :disabled="!isDataLoaded">
          {{ isLoading.update ? '更新中...' : '更新游戏' }}
        </el-button>
        <el-button type="warning" @click="restoreInitialData" :loading="isLoading.restore">
          <el-icon>
            <RefreshLeft />
          </el-icon>
          {{ isLoading.restore ? '恢复中...' : '恢复初始值' }}
        </el-button>
      </div>
    </el-form>

    <div class="content-wrapper">
      <el-scrollbar v-if="gameData" class="game-data-editor">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="基本信息" name="basic">
            <template #label>
              <el-icon>
                <InfoFilled />
              </el-icon>
              <span>基本信息</span>
            </template>
            <el-scrollbar height="500px">
              <el-form :model="gameData" label-width="140px" class="basic-info-form">
                <el-form-item label="玩家ID">
                  <tooltip-wrapper content="玩家的唯一标识符">
                    <el-input v-model="gameData.playerID" disabled></el-input>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="等级">
                  <tooltip-wrapper content="玩家当前的等级">
                    <el-input-number v-model="gameData.level" :min="-1" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="巡逻联盟币">
                  <tooltip-wrapper content="游戏中的主要货币">
                    <el-input-number v-model="gameData.money" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="金条">
                  <tooltip-wrapper content="高级货币,用于购买稀有物品">
                    <el-input-number v-model="gameData.gem" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="体力">
                  <tooltip-wrapper content="用于进行游戏活动的能量值">
                    <el-input-number v-model="gameData.tiLiNum" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="经验值">
                  <tooltip-wrapper content="累积的经验,用于提升等级">
                    <el-input-number v-model="gameData.expNum" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="当前等级">
                  <tooltip-wrapper content="玩家目前的等级">
                    <el-input-number v-model="gameData.nowGrade" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="天赋红级别">
                  <tooltip-wrapper content="红色天赋的等级">
                    <el-input-number v-model="gameData.tianFuHongLevel" :min="0"
                      controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="天赋蓝级别">
                  <tooltip-wrapper content="蓝色天赋的等级">
                    <el-input-number v-model="gameData.tianFuLanLevel" :min="0"
                      controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="天赋石">
                  <tooltip-wrapper content="用于提升天赋的特殊道具">
                    <el-input-number v-model="gameData.tianFuShiNum" :min="0"
                      controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="复活币">
                  <tooltip-wrapper content="用于复活角色的特殊道具">
                    <el-input-number v-model="gameData.fuHuoBiNum" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="积分">
                  <tooltip-wrapper content="玩家获得的游戏积分">
                    <el-input-number v-model="gameData.jiFen" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
                <el-form-item label="勋章数">
                  <tooltip-wrapper content="玩家获得的勋章数量">
                    <el-input-number v-model="gameData.xunZhangNum" :min="0"
                      controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </el-tab-pane>

          <el-tab-pane label="角色信息" name="characters">
            <template #label>
              <el-icon>
                <User />
              </el-icon>
              <span>角色信息</span>
            </template>
            <div v-if="activeTab === 'characters'" class="table-container">
              <el-table :data="gameData.roleData" height="400" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="uid" label="UID" width="80">
                  <template #default="scope">
                    <tooltip-wrapper content="角色的唯一标识符">
                      <el-input-number v-model="scope.row.uid" :min="0" :max="999999"
                        controls-position="right"></el-input-number>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <el-table-column prop="grade" label="等级" width="120">
                  <template #default="scope">
                    <tooltip-wrapper content="角色的等级">
                      <el-input-number v-model="scope.row.grade" :min="1" :max="999"
                        controls-position="right"></el-input-number>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <el-table-column prop="star" label="星级" width="120">
                  <template #default="scope">
                    <tooltip-wrapper content="角色的星级">
                      <el-input-number v-model="scope.row.star" :min="1" :max="999"
                        controls-position="right"></el-input-number>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <el-table-column prop="isUse" label="是否使用" width="100">
                  <template #default="scope">
                    <tooltip-wrapper content="是否正在使用该角色">
                      <el-switch v-model="scope.row.isUse" :active-value="1" :inactive-value="0"></el-switch>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <el-table-column prop="isHelp" label="是否助战" width="100">
                  <template #default="scope">
                    <tooltip-wrapper content="是否设置为助战角色">
                      <el-switch v-model="scope.row.isHelp" :active-value="1" :inactive-value="0"></el-switch>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="技能信息" name="skills">
            <template #label>
              <el-icon>
                <MagicStick />
              </el-icon>
              <span>技能信息</span>
            </template>
            <div v-if="activeTab === 'skills'" class="table-container">
              <el-table :data="gameData.skillData" height="400" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="lv" label="等级" width="120">
                  <template #default="scope">
                    <el-input-number v-model="scope.row.lv" :min="1" :max="999"
                      controls-position="right"></el-input-number>
                  </template>
                </el-table-column>
                <el-table-column prop="unlock" label="是否解锁" width="100">
                  <template #default="scope">
                    <el-switch v-model="scope.row.unlock" :active-value="1" :inactive-value="0"></el-switch>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="商城信息" name="shop">
            <template #label>
              <el-icon>
                <ShoppingBag />
              </el-icon>
              <span>商城信息</span>
            </template>
            <el-scrollbar height="500px">
              <el-form :model="gameData" label-width="140px" class="shop-info-form">
                <!-- 商城货币 -->
                <el-form-item label="商城货币">
                  <el-table :data="transformedShopMoneys" style="width: 100%">
                    <el-table-column prop="index" label="序号" width="80"></el-table-column>
                    <el-table-column prop="value" label="值">
                      <template #default="scope">
                        <el-input-number v-model="gameData.shopMoneys[scope.row.index - 1]" :min="0"
                          controls-position="right"></el-input-number>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>

                <!-- 商城购买信息 -->
                <el-form-item label="商城购买记录">
                  <el-table :data="transformedShopBuyInfo" style="width: 100%">
                    <el-table-column prop="index" label="序号" width="80"></el-table-column>
                    <el-table-column prop="value" label="购买状态">
                      <template #default="scope">
                        <el-input-number v-model="gameData.shopBuyInfo[scope.row.index - 1]" :min="0"
                          controls-position="right"></el-input-number>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>

                <!-- 体力购买信息 -->
                <el-form-item label="体力购买次数">
                  <el-table :data="transformedTiLiBuyArr" style="width: 100%">
                    <el-table-column prop="index" label="序号" width="80"></el-table-column>
                    <el-table-column prop="value" label="购买次数">
                      <template #default="scope">
                        <el-input-number v-model="gameData.tiLiBuyArr[scope.row.index - 1]" :min="0"
                          controls-position="right"></el-input-number>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>

                <!-- 其他商城相关数据 -->
                <el-form-item label="装备购买次数">
                  <tooltip-wrapper content="玩家购买装备的总次数">
                    <el-input-number v-model="gameData.buyZBNum" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>

                <el-form-item label="角色购买次数">
                  <tooltip-wrapper content="玩家购买角色的总次数">
                    <el-input-number v-model="gameData.buyRoleNum" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>

                <el-form-item label="扫荡卡数量">
                  <tooltip-wrapper content="玩家拥有的扫荡卡数量">
                    <el-input-number v-model="gameData.saoDangKa" :min="0" controls-position="right"></el-input-number>
                  </tooltip-wrapper>
                </el-form-item>
              </el-form>
            </el-scrollbar>
          </el-tab-pane>

          <el-tab-pane label="装备信息" name="equipment">
            <template #label>
              <el-icon>
                <Suitcase />
              </el-icon>
              <span>装备信息</span>
            </template>
            <div v-if="activeTab === 'equipment'" class="table-container">
              <div class="equipment-actions">
                <el-button type="primary" size="small" @click="showAddEquipmentDialog" class="add-equipment-button">
                  <el-icon>
                    <Plus />
                  </el-icon>
                  批量添加装备
                </el-button>
              </div>
              <el-table :data="gameData.zbData" height="400" style="width: 100%">
                <el-table-column prop="id" label="ID" width="80"></el-table-column>
                <el-table-column prop="grade" label="等级" width="120">
                  <template #default="scope">
                    <tooltip-wrapper content="装备的等级">
                      <el-input-number v-model="scope.row.grade" :min="0" :max="999"
                        controls-position="right"></el-input-number>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <el-table-column prop="quality" label="品质" width="120">
                  <template #default="scope">
                    <tooltip-wrapper content="装备的品质">
                      <el-input-number v-model="scope.row.quality" :min="0" :max="999"
                        controls-position="right"></el-input-number>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <el-table-column prop="isWear" label="是否穿戴" width="100">
                  <template #default="scope">
                    <tooltip-wrapper content="是否已经穿戴该装备">
                      <el-switch v-model="scope.row.isWear" :active-value="1" :inactive-value="0"></el-switch>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <!-- 新增删除列 -->
                <el-table-column label="操作" width="100">
                  <template #default="scope">
                    <el-button type="danger" size="small" @click="deleteEquipment(scope.$index)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="任务信息" name="tasks">
            <template #label>
              <el-icon>
                <List />
              </el-icon>
              <span>任务信息</span>
            </template>
            <div v-if="activeTab === 'tasks'" class="table-container">
              <el-table :data="gameData.qiRiTaskInfos" height="400" style="width: 100%">
                <el-table-column prop="ID" label="任务ID" width="80"></el-table-column>
                <el-table-column prop="num" label="完成次数" width="120">
                  <template #default="scope">
                    <tooltip-wrapper content="任务完成次数">
                      <el-input-number v-model="scope.row.num" :min="0" :max="999999"
                        controls-position="right"></el-input-number>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="scope">
                    <tooltip-wrapper content="任务状态">
                      <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0"></el-switch>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>

          <el-tab-pane label="图纸信息" name="blueprints">
            <template #label>
              <el-icon>
                <Document />
              </el-icon>
              <span>图纸信息</span>
            </template>
            <div v-if="activeTab === 'blueprints'" class="table-container">
              <el-table :data="transformedBlueprintData" height="400" style="width: 100%">
                <el-table-column prop="index" label="序号" width="80"></el-table-column>
                <el-table-column prop="value" label="数量" width="120">
                  <template #default="scope">
                    <tooltip-wrapper content="图纸数量">
                      <el-input-number v-model="gameData.zbTuZhiData[scope.$index]" :min="0" :max="999999"
                        controls-position="right"></el-input-number>
                    </tooltip-wrapper>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-scrollbar>

      <div v-else class="no-data-placeholder">
        <el-empty description="暂无数据" :image-size="200">
          <template #description>
            <p>请先下载数据</p>
          </template>
        </el-empty>
      </div>
    </div>

    <!-- 添加装备对话框 -->
    <el-dialog v-model="addEquipmentDialogVisible" title="批量添加装备" width="30%" center>
      <el-form :model="newEquipment" label-width="80px" size="small">
        <el-form-item label="数量">
          <el-input-number v-model="newEquipment.count" :min="1" :max="100" style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="装备ID">
          <el-input-number v-model="newEquipment.id" :min="0" style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="等级">
          <el-input-number v-model="newEquipment.grade" :min="0" :max="999" style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="品质">
          <el-input-number v-model="newEquipment.quality" :min="0" :max="999" style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="是否穿戴">
          <el-switch v-model="newEquipment.isWear" :active-value="1" :inactive-value="0"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addEquipmentDialogVisible = false" size="small">取消</el-button>
          <el-button type="primary" @click="addEquipment" size="small">确认添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, computed, nextTick, onMounted, onUnmounted, getCurrentInstance } from 'vue'
import { User, Download, Refresh, RefreshLeft, Plus, InfoFilled, MagicStick, Suitcase, List, Document, ShoppingBag } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import md5 from 'md5'
import { vResize } from 'vue-resize-directive'
import TooltipWrapper from './TooltipWrapper.vue'

const debounce = (fn, delay) => {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};

export default {
  name: 'GameIdForm',
  components: {
    User,
    Download,
    Refresh,
    RefreshLeft,
    Plus,
    InfoFilled,
    MagicStick,
    Suitcase,
    List,
    TooltipWrapper,
    Document,
    ShoppingBag
  },
  props: {
    initialData: {
      type: Object,
      default: () => ({})
    },
    style: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:operationHistory', 'loading', 'data-loaded'],
  directives: {
    resize: vResize
  },
  setup(props, { emit }) {
    const { proxy } = getCurrentInstance();
    const form = reactive({
      gameId: props.initialData.gameId || 'oymKy624JRs78uPNxAnOBHrqJhEg' //oymKy6xyoQ62-pcfrbBkBCfpxKMg
    })

    const gameData = ref(null)
    const activeTab = ref('basic')

    const token = ref('')
    const auth = ref('')

    const encryptionData = (data) => {
      try {
        const n = Math.floor(Date.now() / 1e3).toString();
        const md5Str = md5("NewRpggameV2" + n);

        // 将数据转换为 UTF-8 编码的字符串
        let base64Str = '';
        if (data) {
          // 将字符串转换为 UTF-8 编码的字节数组
          const utf8Str = unescape(encodeURIComponent(data));
          // 进行 Base64 编码
          base64Str = btoa(utf8Str);
        } else {
          base64Str = btoa('null');
        }

        return md5Str + base64Str + n;
      } catch (error) {
        console.error('加密数据失败:', error);
        ElMessage.error('加密数据失败');
        return null;
      }
    }

    const getToken = async () => {
      const requestData = {
        ActionId: 3,
        UserId: form.gameId,
        Params: "vx",
        ServerId: "1",
        Channel: 120,
        Version: "1.0.0",
        Package: "com.dtty.qhjj"
      };

      const encryptedData = encryptionData(JSON.stringify(requestData));

      try {
        const response = await fetch('/api/logic/newrpg_actionsV2', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
            'xweb_xhr': '1',
            'Accept': '*/*',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wx73375fce167e413f/56/page-frame.html',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9'
          },
          body: encryptedData
        });

        if (response.ok) {
          const data = await response.json();
          if (data.Status === 0 && data.Token && data.Auth) {
            token.value = data.Token;
            auth.value = data.Auth;
            return { token: data.Token, auth: data.Auth };
          } else if (data.Status === 8) {
            const banEndDate = new Date(data.Time);
            const banEndDateString = banEndDate.toLocaleString();
            throw new Error(`账号已被禁封，解封时间: ${banEndDateString}`);
          } else {
            throw new Error('获取TokenAuth失败');
          }
        } else {
          throw new Error('获取Token和Auth请求失败');
        }
      } catch (error) {
        console.error('获取Token和Auth出错:', error);
        ElMessage.error('获取Token和Auth失败: ' + error.message);
        throw error;
      }
    }

    const isLoading = reactive({
      download: false,
      update: false,
      restore: false,
      save: false  // 添加保存操作的加载状态
    })

    const isDataLoaded = ref(false)  // 添加这一行

    const downloadData = async () => {
      isLoading.download = true
      isDataLoaded.value = false  // 开始下载时设置为 false
      try {
        const { token: newToken, auth: newAuth } = await getToken();

        const userId = newAuth;
        const clientTimes = Date.now();
        const encryptedData = encryptionData(null);

        const url = `/sdk/newrpg/v1/getdata?userId=${userId}&Key=data&cliemnttimes=${clientTimes}&deviceInfo=unknown`;
        console.log('url:', url);

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
            'xweb_xhr': '1',
            'Accept': '*/*',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wx73375fce167e413f/56/page-frame.html',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9'
          },
          body: encryptedData
        });

        if (response.ok) {
          const responseData = await response.json();
          console.log('下的数据:', responseData);
          if (responseData && responseData.Status === 0 && responseData.UserData) {
            try {
              const userData = JSON.parse(responseData.UserData);
              if (userData && userData.data) {
                gameData.value = userData.data;
                updateOperationHistory(userData.data);
                ElMessage.success('数据下载成功');
                isDataLoaded.value = true  // 下载成功时设置为 true
                emit('data-loaded', true)
              } else {
                throw new Error('UserData 中的 data 字段不存在');
              }
            } catch (error) {
              throw new Error('解析 UserData 失败: ' + error.message);
            }
          } else {
            throw new Error('返回的数据格式不正确');
          }
        } else {
          throw new Error('下载数据失败: ' + response.statusText);
        }
      } catch (error) {
        console.error('下载数据出错:', error);
        ElMessage.error('下载数失败: ' + error.message);
        updateOperationHistory({ playerID: '未知', level: '未知', money: '未知', gem: '未知', tiLiNum: '未知', roleData: [], skillData: [] });
        isDataLoaded.value = false  // 下载失败时设置为 false
        emit('data-loaded', false)
      } finally {
        isLoading.download = false
      }
    }

    const updateOperationHistory = (data) => {
      const currentTime = new Date().toLocaleString();
      const operationHistory = [
        { time: currentTime, function: '下载数据', value: '成功' },
        { time: currentTime, function: '玩家ID', value: data.playerID },
        { time: currentTime, function: '等级', value: data.level },
        { time: currentTime, function: '巡逻联盟币', value: data.money },
        { time: currentTime, function: '金条', value: data.gem },
        { time: currentTime, function: '体力', value: data.tiLiNum },
        { time: currentTime, function: '角色数量', value: data.roleData ? data.roleData.length : '未知' },
        { time: currentTime, function: '技能数量', value: data.skillData ? data.skillData.length : '未知' },
      ];
      emit('update:operationHistory', operationHistory);
    }

    const updateGame = async () => {
      isLoading.update = true
      try {
        const { token: newToken, auth: newAuth } = await getToken();

        const userId = newAuth;
        const clientTimes = Date.now();

        const dataToSend = {
          data: gameData.value
        };

        console.log('dataToSend:', JSON.stringify(dataToSend, null, 2));
        debugger;

        const encryptedData = encryptionData(JSON.stringify(dataToSend));

        const url = `/sdk/newrpg/v1/postdata?userId=${userId}&Key=data&Token=${newToken}&Version=1.0.0&Package=com.dtty.qhjj&cliemnttimes=${clientTimes}&deviceInfo=microsoft`;

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
            'xweb_xhr': '1',
            'Accept': '*/*',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wx73375fce167e413f/56/page-frame.html',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9'
          },
          body: encryptedData
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`更新游戏失败: ${response.status} ${response.statusText}`);
        }

        // 发送额外的请求
        const additionalRequestData = {
          ActionId: 7,
          UserId: newAuth,
          Params: newToken,
          ServerId: "1",
          Channel: 120,
          Version: "1.0.0",
          Package: "com.dtty.qhjj"
        };

        const encryptedAdditionalData = encryptionData(JSON.stringify(additionalRequestData));

        const additionalResponse = await fetch('/api/logic/newrpg_actionsV2', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
            'xweb_xhr': '1',
            'Accept': '*/*',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wx73375fce167e413f/56/page-frame.html',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9'
          },
          body: encryptedAdditionalData
        });

        if (!additionalResponse.ok) {
          throw new Error('额外请求失败');
        }

        const data = await response.json();
        console.log('更新游戏成功:', data);
        emit('update:operationHistory', [{ time: new Date().toLocaleString(), function: '更新游戏', value: '成功' }]);
        ElMessage.success('游戏更新成功');

        // 添加这行来触发重新计算高度
        nextTick(() => {
          window.dispatchEvent(new Event('resize'));
          if (formRef.value) {
            formRef.value.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        });

        // 返回 token 和 auth
        return { token: newToken, auth: newAuth };
      } catch (error) {
        console.error('更新游戏出错:', error);
        emit('update:operationHistory', [{ time: new Date().toLocaleString(), function: '更新游戏', value: '错误' }]);
        ElMessage.error('��新游戏失败: ' + error.message);
        throw error;
      } finally {
        isLoading.update = false
      }
    }

    const saveData = async () => {
      isLoading.save = true
      try {
        await updateGame();
        ElMessage.success('数据保存成功');

        // 添加这行来触发重新计算高度
        nextTick(() => {
          window.dispatchEvent(new Event('resize'));
          if (formRef.value) {
            formRef.value.scrollIntoView({ behavior: 'smooth', block: 'start' });
          }
        });
      } catch (error) {
        ElMessage.error('数据保存失败: ' + error.message);
      } finally {
        isLoading.save = false
      }
    }

    const handleTabChange = () => {
      nextTick(() => {
        // 在下一个 tick 中触发一个空的 resize 事件
        window.dispatchEvent(new Event('resize'))
      })
    }

    const componentStyle = computed(() => ({
      ...props.style,
      overflow: 'auto'  // 添加滚动条，以防内容溢出
    }))

    const restoreInitialData = async () => {
      isLoading.restore = true
      isDataLoaded.value = false  // 重置时设置为 false
      
      try {
        const { token: newToken, auth: newAuth } = await getToken();
        console.log(token,auth);
        debugger;
        const userId = newAuth;
        const clientTimes = Date.now();

        const initialData = { "data": { "roleData": [{ "grade": 1, "id": 0, "isHelp": 0, "isUse": 1, "star": 1, "uid": 0 }], "roleMaterialData": [], "roleUid": 0, "money": 1900, "gem": 0, "zbData": [{ "grade": 0, "id": 2, "isTuZhi": 0, "isWear": 0, "place": 0, "quality": 0, "qualityGrade": 0, "sid": 1 }, { "grade": 0, "id": 6, "isTuZhi": 0, "isWear": 0, "place": 1, "quality": 0, "qualityGrade": 0, "sid": 2 }], "zbTuZhiData": [0, 0, 0, 0, 0, 0, 0], "shopMoneys": [1, 1], "xunLuoIndex": 0, "zbSuiPianData": [], "ZBUid": 2, "tianFuHongLevel": 1, "tianFuLanLevel": 0, "tianFuMidLevel": -1, "tianFuBigLevel": 0, "levelPro": [0, 0], "zbSpTongYongData": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "unlockLevelArr": [0], "level": -1, "levelBoxReward": [[0, 0, 0], [0, 1, 0], [0, 2, 0], [0, 3, 0], [0, 4, 0], [0, 5, 0], [0, 6, 0], [0, 7, 0], [0, 8, 0], [0, 9, 0], [0, 10, 0], [0, 11, 0], [0, 12, 0], [0, 13, 0], [0, 14, 0], [0, 15, 0], [0, 16, 0], [0, 17, 0], [0, 18, 0], [0, 19, 0], [0, 20, 0], [0, 21, 0], [0, 22, 0], [0, 23, 0], [0, 24, 0], [0, 25, 0], [0, 26, 0], [0, 27, 0], [0, 28, 0], [0, 29, 0], [0, 30, 0], [0, 31, 0], [0, 32, 0], [0, 33, 0], [0, 34, 0], [0, 35, 0], [0, 36, 0], [0, 37, 0], [0, 38, 0], [0, 39, 0], [0, 40, 0], [0, 41, 0], [0, 42, 0], [0, 43, 0], [0, 44, 0], [0, 45, 0], [0, 46, 0], [0, 47, 0], [0, 48, 0], [0, 49, 0], [0, 50, 0], [0, 51, 0], [0, 52, 0], [0, 53, 0], [0, 54, 0], [0, 55, 0], [0, 56, 0], [0, 57, 0], [0, 58, 0], [0, 59, 0], [0, 60, 0], [0, 61, 0], [0, 62, 0], [0, 63, 0], [0, 64, 0], [0, 65, 0], [0, 66, 0], [0, 67, 0], [0, 68, 0], [0, 69, 0], [0, 70, 0], [0, 71, 0], [0, 72, 0], [0, 73, 0], [0, 74, 0], [0, 75, 0], [0, 76, 0], [0, 77, 0], [0, 78, 0], [0, 79, 0], [0, 80, 0], [0, 81, 0], [0, 82, 0], [0, 83, 0], [0, 84, 0], [0, 85, 0], [0, 86, 0], [0, 87, 0], [0, 88, 0], [0, 89, 0], [0, 90, 0], [0, 91, 0], [0, 92, 0], [0, 93, 0], [0, 94, 0], [0, 95, 0], [0, 96, 0], [0, 97, 0], [0, 98, 0], [0, 99, 0], [0, 100, 0], [0, 101, 0], [0, 102, 0], [0, 103, 0], [0, 104, 0], [0, 105, 0], [0, 106, 0]], "soundType": 0, "musicType": 0, "shakeType": 0, "hxVideoTimerArr": [0, 0, 0, 0], "moveType": 0, "huazhiType": 2, "enterLevelIDArr": [0], "taskIndex": 0, "zxTaskNowArr": [0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0], "zbUpNum": 0, "xunLuoNum": 0, "roleUpNum": 0, "killBossNum": 0, "tiLiNum": 30, "tiLiBuyArr": [3, 3], "tiLiTimer": 1729512273000, "lastOnLineTimer": 1729520198158, "shopBuyInfo": [0, 0, 0, 0, 0], "shopGiftInfo": [], "huoYueDuDay": 0, "huoYueDuWeek": 0, "dayTaskArr": [], "dayTaskPro": [1, 19, 19, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "weekTaskArr": [], "weekTaskPro": [1, 0, 5, 0, 19, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "huoYueDayArr": [], "huoYueWeekArr": [], "dayOnLineTime": 1150, "weekOnLineTime": 1150, "offLineQuickNum": 1, "lastOffLineTimer": 1729500254914, "offLineRewardArr": null, "buyZBNum": 1, "buyRoleNum": 0, "expNum": 0, "nowGrade": 0, "oldGrade": 0, "guideIndex": 8, "isFirstStart": true, "skillLimits": [5, 8, 10, 23, 28, 34, 29, 30, 36, 37, 33, 10000, 35], "qiRiTaskInfos": [{ "ID": 0, "num": 0, "status": 0 }, { "ID": 1, "num": 0, "status": 0 }, { "ID": 2, "num": 1, "status": 0 }, { "ID": 3, "num": 1, "status": 0 }, { "ID": 4, "num": 0, "status": 0 }, { "ID": 5, "num": 2, "status": 1 }, { "ID": 6, "num": 1, "status": 0 }, { "ID": 7, "num": 1168, "status": 1 }, { "ID": 8, "num": 0, "status": 0 }, { "ID": 9, "num": 0, "status": 0 }, { "ID": 10, "num": 0, "status": 0 }, { "ID": 11, "num": 0, "status": 0 }, { "ID": 12, "num": 0, "status": 0 }, { "ID": 13, "num": 1, "status": 0 }, { "ID": 14, "num": 1, "status": 0 }, { "ID": 15, "num": 0, "status": 0 }, { "ID": 16, "num": 0, "status": 0 }, { "ID": 17, "num": 0, "status": 0 }, { "ID": 18, "num": 1, "status": 0 }, { "ID": 19, "num": 0, "status": 0 }, { "ID": 20, "num": 1168, "status": 0 }, { "ID": 21, "num": 0, "status": 0 }, { "ID": 22, "num": 0, "status": 0 }, { "ID": 23, "num": 0, "status": 0 }, { "ID": 24, "num": 0, "status": 0 }, { "ID": 25, "num": 0, "status": 0 }, { "ID": 26, "num": 1, "status": 0 }, { "ID": 27, "num": 1, "status": 0 }, { "ID": 28, "num": 0, "status": 0 }, { "ID": 29, "num": 0, "status": 0 }, { "ID": 30, "num": 0, "status": 0 }, { "ID": 31, "num": 1, "status": 0 }, { "ID": 32, "num": 0, "status": 0 }, { "ID": 33, "num": 1168, "status": 0 }, { "ID": 34, "num": 0, "status": 0 }, { "ID": 35, "num": 0, "status": 0 }, { "ID": 36, "num": 0, "status": 0 }, { "ID": 37, "num": 0, "status": 0 }, { "ID": 38, "num": 0, "status": 0 }, { "ID": 39, "num": 1, "status": 0 }, { "ID": 40, "num": 1, "status": 0 }, { "ID": 41, "num": 0, "status": 0 }, { "ID": 42, "num": 0, "status": 0 }, { "ID": 43, "num": 0, "status": 0 }, { "ID": 44, "num": 1, "status": 0 }, { "ID": 45, "num": 0, "status": 0 }, { "ID": 46, "num": 1168, "status": 0 }, { "ID": 47, "num": 0, "status": 0 }, { "ID": 48, "num": 0, "status": 0 }, { "ID": 49, "num": 0, "status": 0 }, { "ID": 50, "num": 0, "status": 0 }, { "ID": 51, "num": 0, "status": 0 }, { "ID": 52, "num": 1, "status": 0 }, { "ID": 53, "num": 1, "status": 0 }, { "ID": 54, "num": 0, "status": 0 }, { "ID": 55, "num": 0, "status": 0 }, { "ID": 56, "num": 1, "status": 0 }, { "ID": 57, "num": 0, "status": 0 }, { "ID": 58, "num": 0, "status": 0 }, { "ID": 59, "num": 1168, "status": 0 }, { "ID": 60, "num": 0, "status": 0 }, { "ID": 61, "num": 0, "status": 0 }, { "ID": 62, "num": 0, "status": 0 }, { "ID": 63, "num": 0, "status": 0 }, { "ID": 64, "num": 0, "status": 0 }, { "ID": 65, "num": 1, "status": 0 }, { "ID": 66, "num": 1, "status": 0 }, { "ID": 67, "num": 0, "status": 0 }, { "ID": 68, "num": 0, "status": 0 }, { "ID": 69, "num": 1, "status": 0 }, { "ID": 70, "num": 0, "status": 0 }, { "ID": 71, "num": 1168, "status": 0 }, { "ID": 72, "num": 0, "status": 0 }, { "ID": 73, "num": 0, "status": 0 }, { "ID": 74, "num": 0, "status": 0 }, { "ID": 75, "num": 0, "status": 0 }, { "ID": 76, "num": 0, "status": 0 }, { "ID": 77, "num": 0, "status": 0 }, { "ID": 78, "num": 0, "status": 0 }, { "ID": 79, "num": 1, "status": 0 }, { "ID": 80, "num": 1, "status": 0 }, { "ID": 81, "num": 0, "status": 0 }, { "ID": 82, "num": 1, "status": 0 }, { "ID": 83, "num": 0, "status": 0 }, { "ID": 84, "num": 1168, "status": 0 }, { "ID": 85, "num": 1168, "status": 0 }, { "ID": 86, "num": 0, "status": 0 }, { "ID": 87, "num": 0, "status": 0 }, { "ID": 88, "num": 0, "status": 0 }, { "ID": 89, "num": 0, "status": 0 }, { "ID": 90, "num": 0, "status": 0 }, { "ID": 91, "num": 0, "status": 0 }, { "ID": 92, "num": 0 }], "qiRiNum": 0, "qiRiDay": 1729440000000, "zbVideoTimer": 0, "zbVideoTimerArr": [0, 0, 0, 0], "roleVideoTimer": 0, "zbBaoDiNum": 1, "roleBaoDiNum": 0, "superZbBaoDiNum": 0, "superZbSBaoDiNum": 0, "kongTouGetNum": 0, "addMoneyNum": 0, "addGemNum": 0, "saoDangOpen": 1, "payMoneyNum": -100, "payGemNum": 0, "date": 21, "loadCaCheArr": [1, 0, 0, 0, 0, 0, 0, 0, 0], "tianFuShiNum": 0, "mainLevelPro": [0, 0], "mainRewardArr": [], "gameGuideIndex": 1, "xunZhangNum": 0, "tiaoZhanShu": 2, "wuJinNanDuMax": 0, "tiaoZhanShuTime": 1729468800000, "tianZhanShuBuyInfo": [1, 1], "jiFen": 0, "saoDangKa": 1, "wjGiftFreeNum": 1, "zbCardArr": [0, 0, 0, 0, 0, 0], "roleJiYinNum": 0, "wjWeekIndex": 1, "wjWeekIndexFUWU": 1, "qiRiGiftInfos": [], "goldPassGet": [{ "data": [0, 0], "id": 1 }, { "data": [0, 0], "id": 2 }, { "data": [0, 0], "id": 3 }, { "data": [0, 0], "id": 4 }, { "data": [0, 0], "id": 5 }, { "data": [0, 0], "id": 6 }, { "data": [0, 0], "id": 7 }, { "data": [0, 0], "id": 8 }, { "data": [0, 0], "id": 9 }, { "data": [0, 0], "id": 10 }, { "data": [0, 0], "id": 11 }, { "data": [0, 0], "id": 12 }, { "data": [0, 0], "id": 13 }, { "data": [0, 0], "id": 14 }, { "data": [0, 0], "id": 15 }, { "data": [0, 0], "id": 16 }, { "data": [0, 0], "id": 17 }, { "data": [0, 0], "id": 18 }, { "data": [0, 0], "id": 19 }, { "data": [0, 0], "id": 20 }, { "data": [0, 0], "id": 21 }, { "data": [0, 0], "id": 22 }, { "data": [0, 0], "id": 23 }, { "data": [0, 0], "id": 24 }, { "data": [0, 0], "id": 25 }, { "data": [0, 0], "id": 26 }, { "data": [0, 0], "id": 27 }, { "data": [0, 0], "id": 28 }, { "data": [0, 0], "id": 29 }, { "data": [0, 0], "id": 30 }, { "data": [0, 0], "id": 31 }, { "data": [0, 0], "id": 32 }, { "data": [0, 0], "id": 33 }, { "data": [0, 0], "id": 34 }, { "data": [0, 0], "id": 35 }, { "data": [0, 0], "id": 36 }, { "data": [0, 0], "id": 37 }, { "data": [0, 0], "id": 38 }, { "data": [0, 0], "id": 39 }, { "data": [0, 0], "id": 40 }, { "data": [0, 0], "id": 41 }, { "data": [0, 0], "id": 42 }, { "data": [0, 0], "id": 43 }, { "data": [0, 0], "id": 44 }, { "data": [0, 0], "id": 45 }, { "data": [0, 0], "id": 46 }, { "data": [0, 0], "id": 47 }, { "data": [0, 0], "id": 48 }, { "data": [0, 0], "id": 49 }], "TTCeBianLan": 0, "TTAddZhuoMian": 0, "offLineQuickNumTiLi": 3, "levelProcess": 0.66, "levelProcessMax": 0, "levelInItArr": [], "isSign": 0, "signNum": 0, "signGetArr": [], "nowQiRiTaskIndex": 0, "cunQianGuanTime": 0, "cunQiaGuanLevel": 0, "hxBaoDiArr": [0, 0], "time": 1729520225611, "playerID": "1000140244744516", "saoDangNum": 0, "quickLevel": [[3], [], []], "weekTime": 1729500252000, "xinPianSid": 0, "xinPianArr": [], "xpNewSidArr": null, "xiLianNum": 0, "xinPianIndex": 0, "LongZuOpenTime": 0, "yuXiNum": 0, "chouNum": 0, "raffleTaskInfo": [], "LongYinHuoYueDu": 0, "LongYinGet": [], "LongZuOpenTime1": 0, "yuXiNum1": 0, "chouNum1": 0, "raffleTaskInfo1": [], "LongYinHuoYueDu1": 0, "LongYinGet1": [], "saoDangTime": 0, "saoDangFenMax": 0, "saoDangInfo": null, "PassPortGet": [{ "data": [0, 0, 0], "id": 0 }, { "data": [0, 0, 0], "id": 1 }, { "data": [0, 0, 0], "id": 2 }, { "data": [0, 0, 0], "id": 3 }, { "data": [0, 0, 0], "id": 4 }, { "data": [0, 0, 0], "id": 5 }, { "data": [0, 0, 0], "id": 6 }, { "data": [0, 0, 0], "id": 7 }, { "data": [0, 0, 0], "id": 8 }, { "data": [0, 0, 0], "id": 9 }, { "data": [0, 0, 0], "id": 10 }, { "data": [0, 0, 0], "id": 11 }, { "data": [0, 0, 0], "id": 12 }, { "data": [0, 0, 0], "id": 13 }, { "data": [0, 0, 0], "id": 14 }, { "data": [0, 0, 0], "id": 15 }, { "data": [0, 0, 0], "id": 16 }, { "data": [0, 0, 0], "id": 17 }, { "data": [0, 0, 0], "id": 18 }, { "data": [0, 0, 0], "id": 19 }, { "data": [0, 0, 0], "id": 20 }, { "data": [0, 0, 0], "id": 21 }, { "data": [0, 0, 0], "id": 22 }, { "data": [0, 0, 0], "id": 23 }, { "data": [0, 0, 0], "id": 24 }, { "data": [0, 0, 0], "id": 25 }, { "data": [0, 0, 0], "id": 26 }, { "data": [0, 0, 0], "id": 27 }, { "data": [0, 0, 0], "id": 28 }, { "data": [0, 0, 0], "id": 29 }, { "data": [0, 0, 0], "id": 30 }, { "data": [0, 0, 0], "id": 31 }, { "data": [0, 0, 0], "id": 32 }, { "data": [0, 0, 0], "id": 33 }, { "data": [0, 0, 0], "id": 34 }, { "data": [0, 0, 0], "id": 35 }, { "data": [0, 0, 0], "id": 36 }, { "data": [0, 0, 0], "id": 37 }, { "data": [0, 0, 0], "id": 38 }, { "data": [0, 0, 0], "id": 39 }, { "data": [0, 0, 0], "id": 40 }, { "data": [0, 0, 0], "id": 41 }, { "data": [0, 0, 0], "id": 42 }, { "data": [0, 0, 0], "id": 43 }, { "data": [0, 0, 0], "id": 44 }, { "data": [0, 0, 0], "id": 45 }, { "data": [0, 0, 0], "id": 46 }, { "data": [0, 0, 0], "id": 47 }, { "data": [0, 0, 0], "id": 48 }, { "data": [0, 0, 0], "id": 49 }], "PassPortTiLi": 5, "PassPortLun": 0, "kongTouGetNumArr": [], "dogUseNum": 0, "dogForever": 0, "newGiftVideoNum": 0, "newGiftGetArr": [], "newGiftOpenTime": 0, "signTimer": 0, "skillData": [{ "id": 0, "lv": 1, "unlock": 1 }, { "id": 1, "lv": 1, "unlock": 1 }, { "id": 2, "lv": 1, "unlock": 1 }, { "id": 3, "lv": 1, "unlock": 1 }, { "id": 4, "lv": 1, "unlock": 0 }, { "id": 5, "lv": 1, "unlock": 0 }, { "id": 6, "lv": 1, "unlock": 0 }, { "id": 7, "lv": 1, "unlock": 0 }, { "id": 8, "lv": 1, "unlock": 0 }, { "id": 9, "lv": 1, "unlock": 0 }, { "id": 10, "lv": 1, "unlock": 0 }, { "id": 11, "lv": 1, "unlock": 1 }, { "id": 12, "lv": 1, "unlock": 0 }, { "id": 13, "lv": 1, "unlock": 0 }, { "id": 14, "lv": 1, "unlock": 0 }, { "id": 15, "lv": 1, "unlock": 0 }, { "id": 16, "lv": 1, "unlock": 0 }], "wjGiftBuyNum": 0, "wjShopBuyArr": [], "wjStartTime": 0, "wjJiFenMax": 0, "os": "Windows", "version": "1.7.1", "newZBTongYongSuiPian": [], "ZheKouChoose": [{ "getTime": 0, "reward": [] }, { "getTime": 0, "reward": [] }, { "getTime": 0, "reward": [] }], "ZheKouBuyNum": [0, 0, 0], "ChouJiangNum": 0, "ChouJiangXingYunNum": 0, "ChouJiangFree": 0, "ChouJiangGetArr": [], "DuiHuanNum": [], "ChouJiangLun": 0, "MoFangNum": 0, "RoleJinJieChoose": [], "IsfirstGongGaoDay": null, "newGameGuideIndex": null, "fuHuoBiNum": 0, "ZhuanPanJuanNum": 0, "petGuideIndex": 0, "canGetFuHuo": 0, "newShouChongGet": [0, 0, 0], "newShowChongDay": 0, "weaponGuideIndex": 0, "zhanShuGuideIndex": 1, "newGuideIndex": 0, "RoleChoose": [], "xunLuoIndex4": null, "xunLuoIndex5": null, "xunLuoIndex6": null, "xunLuoIndex7": null, "xunLuoIndex8": null, "xunLuoIndex9": null, "beginLevel": [1], "roomId": "0", "bossOpenNum": 0, "bossGetArr": [], "bossBi": 0, "bossShopBuyArr": [], "LongZuId": 0, "LongZuId1": 0, "skillSuiPianData": [{ "id": 0, "num": 0 }, { "id": 1, "num": 0 }, { "id": 2, "num": 0 }, { "id": 3, "num": 0 }, { "id": 4, "num": 0 }, { "id": 5, "num": 0 }, { "id": 6, "num": 0 }, { "id": 7, "num": 0 }, { "id": 8, "num": 0 }, { "id": 9, "num": 0 }, { "id": 10, "num": 0 }, { "id": 11, "num": 0 }, { "id": 12, "num": 0 }, { "id": 13, "num": 0 }, { "id": 14, "num": 0 }, { "id": 15, "num": 0 }, { "id": 16, "num": 0 }], "dayGiftInfo": [], "chooseBoxInfo": [], "dailyStoreInfo": [{ "buyNeedMoney": 160, "buyNum": 0, "id": 23, "zheko": 8 }, { "buyNeedMoney": 9000, "buyNum": 0, "id": 63, "zheko": 9 }, { "buyNeedMoney": 16000, "buyNum": 0, "id": 13, "zheko": 8 }, { "buyNeedMoney": 480, "buyNum": 0, "id": 46, "zheko": 8 }, { "buyNeedMoney": 3000, "buyNum": 0, "id": 63, "zheko": 3 }], "chengZhangChoose": [{ "getTime": 0, "reward": [] }, { "getTime": 0, "reward": [] }, { "getTime": 0, "reward": [] }], "ZBJinJieChoose": [], "isOpenWj": 0 } };

        console.log('初始数据:', JSON.stringify(initialData, null, 2));
        debugger;

        const encryptedData = encryptionData(JSON.stringify(initialData));

        const url = `/sdk/newrpg/v1/postdata?userId=${userId}&Key=data&Token=${newToken}&Version=1.0.0&Package=com.dtty.qhjj&cliemnttimes=${clientTimes}&deviceInfo=microsoft`;

        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json;charset=UTF-8',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
            'xweb_xhr': '1',
            'Accept': '*/*',
            'Sec-Fetch-Site': 'cross-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://servicewechat.com/wx73375fce167e413f/56/page-frame.html',
            'Accept-Encoding': 'gzip, deflate, br',
            'Accept-Language': 'zh-CN,zh;q=0.9'
          },
          body: encryptedData
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('Error response:', errorText);
          throw new Error(`恢复初始值失败: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('恢复初始值成功:', data);
        emit('update:operationHistory', [{ time: new Date().toLocaleString(), function: '恢复初始值', value: '成功' }]);
        ElMessage.success('恢复初始值成功');

        // 重新加载游戏数据
        await downloadData();
        emit('data-loaded', false) // 重置后设置数据加载状态为 false
      } catch (error) {
        console.error('恢复初始值出错:', error);
        emit('update:operationHistory', [{ time: new Date().toLocaleString(), function: '恢复初始值', value: '错误' }]);
        ElMessage.error('恢复初始值失败: ' + error.message);
        isDataLoaded.value = false
      } finally {
        isLoading.restore = false
      }
    }

    // 添加这些变量
    let resizeObserver;
    const formRef = ref(null);

    onMounted(() => {
      // 使用全局定义的 createResizeObserverPolyfill
      resizeObserver = proxy.$createResizeObserverPolyfill();
      if (formRef.value) {
        resizeObserver.observe(formRef.value, () => {
          // 触发新渲染
          nextTick(() => {
            window.dispatchEvent(new Event('resize'));
          });
        });
      }
    });

    onUnmounted(() => {
      // 清理 ResizeObserver
      if (resizeObserver && formRef.value) {
        resizeObserver.unobserve(formRef.value);
      }
    });

    const handleResize = debounce(() => {
      // 在这里处理 resize 事件
      nextTick(() => {
        window.dispatchEvent(new Event('resize'));
      });
    }, 100);  // 100ms 的防抖

    // 添加这个响应式引用
    const addEquipmentDialogVisible = ref(false)

    // 添加这个方法
    const showAddEquipmentDialog = () => {
      addEquipmentDialogVisible.value = true
    }

    // 添加这个响应式引用
    const newEquipment = ref({
      count: 1,
      id: 0,
      grade: 0,
      quality: 0,
      isWear: 0
    });

    // 添加这个方法
    const addEquipment = () => {
      if (!gameData.value || !gameData.value.zbData) {
        ElMessage.error('游戏数据不存在或装备数据不完整');
        return;
      }

      const newEquipments = [];
      let addedCount = 0;
      const existingIds = new Set(gameData.value.zbData.map(item => item.id));

      for (let i = 0; i < newEquipment.value.count; i++) {
        if (!existingIds.has(newEquipment.value.id)) {
          newEquipments.push({
            id: newEquipment.value.id,
            grade: newEquipment.value.grade,
            quality: newEquipment.value.quality,
            isWear: newEquipment.value.isWear,
            sid: gameData.value.zbData.length + addedCount + 1,
            place: 0,
            isTuZhi: 0,
            qualityGrade: 0
          });
          existingIds.add(newEquipment.value.id);
          addedCount++;
        }
      }

      if (addedCount > 0) {
        gameData.value.zbData = [...gameData.value.zbData, ...newEquipments];
        ElMessage.success(`成功添加 ${addedCount} 件装备`);
      } else {
        ElMessage.warning('所有装备ID已存在，未添加新装备');
      }

      addEquipmentDialogVisible.value = false;

      // 重置表单
      newEquipment.value = {
        count: 1,
        id: 0,
        grade: 0,
        quality: 0,
        isWear: 0
      };

      // 使用 nextTick 确保 DOM 已更新
      nextTick(() => {
        // 触发窗口 resize 事件
        window.dispatchEvent(new Event('resize'));

        // 滚动到装备信息标签页
        const equipmentTab = document.querySelector('.el-tabs__item[aria-controls="pane-equipment"]');
        if (equipmentTab) {
          equipmentTab.click();
        }

        // 滚动到表格底部
        const tableBody = document.querySelector('.el-table__body-wrapper');
        if (tableBody) {
          tableBody.scrollTop = tableBody.scrollHeight;
        }

        // 如果有 formRef，滚动到表单顶部
        if (formRef.value) {
          formRef.value.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }

        // 强制更新 el-scrollbar
        const scrollbar = document.querySelector('.game-data-editor');
        if (scrollbar) {
          const scrollbarComponent = scrollbar.__vueParentComponent.ctx;
          if (scrollbarComponent && scrollbarComponent.update) {
            scrollbarComponent.update();
          }
        }

        // 调整滚动位置，确保最后一行数据可见
        const lastRow = document.querySelector('.el-table__body tr:last-child');
        if (lastRow) {
          lastRow.scrollIntoView({ behavior: 'smooth', block: 'end' });
        }

        // 确保 "保存修改" 按钮不遮挡内容
        const saveButton = document.querySelector('.save-button-container');
        if (saveButton) {
          const saveButtonHeight = saveButton.offsetHeight;
          document.querySelector('.game-data-editor').style.paddingBottom = `${saveButtonHeight + 20}px`;
        }
      });

      // 延迟一段时间后再次触发 resize 事件和更新滚动条
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
        const scrollbar = document.querySelector('.game-data-editor');
        if (scrollbar) {
          const scrollbarComponent = scrollbar.__vueParentComponent.ctx;
          if (scrollbarComponent && scrollbarComponent.update) {
            scrollbarComponent.update();
          }
        }
      }, 300);
    };

    // 添加删除装备的方法
    const deleteEquipment = (index) => {
      ElMessageBox.confirm('确定要删除这件装备吗？', '警告', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        gameData.value.zbData.splice(index, 1);
        ElMessage.success('装备删除成功');
        // 触发重新计算高度
        nextTick(() => {
          window.dispatchEvent(new Event('resize'));
        });
      }).catch(() => {
        // 取消删除操作
      });
    };

    // 在 setup 函数中添加计算属性
    const transformedBlueprintData = computed(() => {
      if (!gameData.value || !gameData.value.zbTuZhiData) return [];
      return gameData.value.zbTuZhiData.map((value, index) => ({
        index: index + 1,
        value: value
      }));
    });

    // 添加计算属性
    const transformedShopMoneys = computed(() => {
      if (!gameData.value || !gameData.value.shopMoneys) return []
      return gameData.value.shopMoneys.map((value, index) => ({
        index: index + 1,
        value: value
      }))
    })

    const transformedShopBuyInfo = computed(() => {
      if (!gameData.value || !gameData.value.shopBuyInfo) return []
      return gameData.value.shopBuyInfo.map((value, index) => ({
        index: index + 1,
        value: value
      }))
    })

    const transformedTiLiBuyArr = computed(() => {
      if (!gameData.value || !gameData.value.tiLiBuyArr) return []
      return gameData.value.tiLiBuyArr.map((value, index) => ({
        index: index + 1,
        value: value
      }))
    })

    return {
      form,
      gameData,
      activeTab,
      downloadData,
      updateGame,
      saveData,
      componentStyle,
      token,
      auth,
      handleTabChange,
      restoreInitialData,
      formRef,
      handleResize,
      showAddEquipmentDialog,
      newEquipment,
      addEquipment,
      addEquipmentDialogVisible,
      deleteEquipment,
      isLoading,
      isDataLoaded,  // 添加这一行
      transformedBlueprintData,
      transformedShopMoneys,
      transformedShopBuyInfo,
      transformedTiLiBuyArr
    }
  }
}
</script>

<style scoped>
.game-id-form {
  height: 100%;
  display: flex;
  flex-direction: column;
  user-select: text;
  margin-left: 20px;
  padding-right: 20px;
}

.game-info-form {
  flex-shrink: 0;
}

.game-data-editor {
  flex-grow: 1;
  overflow-y: auto;
  min-height: 500px;
  max-height: calc(100vh - 200px);
  padding-bottom: 20px;
  /* 减少底部内边距 */
}

.input-container {
  width: 100%;
  display: flex;
  justify-content: center;
}

.game-id-input {
  width: 400px;
  max-width: 100%;
}

.button-container {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 20px;
  margin-bottom: 20px;
}

.el-input,
.el-input-number,
.el-select,
.el-switch,
.el-table {
  user-select: text;
  -webkit-user-drag: none;
}

.el-table__body-wrapper {
  user-select: text;
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

.el-table {
  width: 100%;
  min-width: 420px;
}

:deep(.el-tabs__content) {
  overflow: visible;
  height: 100%;
}

:deep(.el-tab-pane) {
  height: 100%;
}

:deep(.el-form-item__label) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

:deep(.el-input),
:deep(.el-input-number) {
  width: calc(100% - 10px);
}

:deep(.el-form-item__content) {
  display: flex;
  width: 100%;
}

:deep(.el-input),
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__inner) {
  width: 100%;
}

.el-table :deep(.el-input),
.el-table :deep(.el-input-number) {
  width: 100%;
}

@media (max-width: 768px) {
  .game-id-input {
    width: 100%;
  }

  .button-container {
    flex-direction: column;
    align-items: center;
  }

  .el-button {
    width: 80%;
    margin-bottom: 10px;
  }

  .el-table {
    font-size: 12px;
  }

  .el-input-number {
    width: 80px;
  }

  .game-id-form {
    margin-left: 10px;
    padding-right: 10px;
  }

  :deep(.el-form-item__label) {
    width: 100% !important;
    text-align: left;
    margin-bottom: 5px;
  }

  .basic-info-form {
    padding: 10px;
  }
}

.equipment-actions {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.add-equipment-button {
  margin-left: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 可以添加些样式来调整删除按钮的外观 */
.el-button--danger.el-button--small {
  padding: 4px 8px;
  font-size: 12px;
}

:deep(.el-tooltip__trigger) {
  display: inline-block;
  width: 100%;
}

:deep(.el-input-number) {
  width: 100%;
}

.el-tooltip__popper {
  max-width: 200px;
  font-size: 14px;
}

/* 添加以下新样式 */
.basic-info-form :deep(.el-form-item__content) {
  display: flex;
  justify-content: flex-start;
  /* 将内容左对齐 */
}

.basic-info-form :deep(.el-input),
.basic-info-form :deep(.el-input-number) {
  width: 300px;
  /* 将宽度从 200px 增加到 300px */
}

/* 为了确保 TooltipWrapper 不会影响输入框的宽度 */
.basic-info-form :deep(.tooltip-wrapper) {
  width: auto;
}

.shop-info-form {
  padding: 20px;
}

.shop-info-form :deep(.el-form-item) {
  margin-bottom: 24px;
}

.shop-info-form :deep(.el-table) {
  margin-bottom: 16px;
}

/* 如果需要，可以调整标签宽度 */
.basic-info-form :deep(.el-form-item__label) {
  width: 140px !important;
  /* 稍微增加标签宽度 */
}

/* 对于移动设备，我们可能需要调整一下样式 */
@media (max-width: 768px) {

  .basic-info-form :deep(.el-input),
  .basic-info-form :deep(.el-input-number) {
    width: 100%;
    /* 在小屏幕上保持全宽 */
  }

  .basic-info-form :deep(.el-form-item__label) {
    width: 100% !important;
    /* 在小屏幕上标签占满宽度 */
  }
}
</style>
