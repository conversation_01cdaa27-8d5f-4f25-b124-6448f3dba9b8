<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="dashboard">
      <!-- 数据卡片区 -->
      <el-row :gutter="20" class="data-cards">
        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card">
            <div class="card-content">
              <div class="card-left">
                <div class="card-title">系统公告</div>
                <div class="card-value">新增二手车模拟器充值功能</div>
              </div>
              <div class="card-icon notice">
                <el-icon><Bell /></el-icon>
              </div>
            </div>
            <div class="card-footer">
              <el-tag size="small" type="danger">最新</el-tag>
              <span class="time">{{ currentDate }}</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card">
            <div class="card-content">
              <div class="card-left">
                <div class="card-title">系统状态</div>
                <div class="card-value">
                  <el-tag type="success">运行正常</el-tag>
                </div>
              </div>
              <div class="card-icon status">
                <el-icon><Monitor /></el-icon>
              </div>
            </div>
            <div class="card-footer">
              <span>在线时长: 99.9%</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card">
            <div class="card-content">
              <div class="card-left">
                <div class="card-title">API 状态</div>
                <div class="card-value">
                  <el-tag type="success">正常</el-tag>
                </div>
              </div>
              <div class="card-icon api">
                <el-icon><Connection /></el-icon>
              </div>
            </div>
            <div class="card-footer">
              <span>响应时间: 50ms</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="6">
          <el-card class="data-card">
            <div class="card-content">
              <div class="card-left">
                <div class="card-title">客服支</div>
                <div class="card-value">在线服务</div>
              </div>
              <div class="card-icon support">
                <el-icon><Service /></el-icon>
              </div>
            </div>
            <div class="card-footer">
              <span>服务时间: 9:00-22:00</span>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 游戏列表区域 -->
      <div class="section-title">
        <el-icon><Platform /></el-icon>
        <span>支持游戏</span>
      </div>

      <el-row :gutter="20" class="game-list">
        <!-- 枪魂狙击 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('gunsoulsniping')">
            <div class="game-icon gun">
              <el-icon><Aim /></el-icon>
            </div>
            <div class="game-info">
              <h3>枪魂狙击</h3>
              <p>快节奏射击游戏</p>
              <el-tag size="small" type="danger">热门</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 小小庇护所 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('smallshelter')">
            <div class="game-icon shelter">
              <el-icon><House /></el-icon>
            </div>
            <div class="game-info">
              <h3>小小庇护所</h3>
              <p>生存建造游戏</p>
              <el-tag size="small" type="success">推荐</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 一起泡泡龙 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('bubble-game')">
            <div class="game-icon bubble">
              <el-icon><Lollipop /></el-icon>
            </div>
            <div class="game-info">
              <h3>一起泡泡龙</h3>
              <p>休闲益智游戏</p>
              <el-tag size="small" type="warning">新游</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 二手车模拟器 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('used-car-game')">
            <div class="game-icon car">
              <el-icon><Van /></el-icon>
            </div>
            <div class="game-info">
              <h3>二手车模拟器</h3>
              <p>经营模拟游戏</p>
              <el-tag size="small" type="info">稳定</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 塔塔守卫战 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('tower-defense')">
            <div class="game-icon tower">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="game-info">
              <h3>塔塔守卫战</h3>
              <p>策略塔防游戏</p>
              <el-tag size="small" type="primary">新增</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 喵桑活下去/欢乐勇士 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('cat-survival')">
            <div class="game-icon cat">
              <el-icon><MagicStick /></el-icon>
            </div>
            <div class="game-info">
              <h3>喵桑活下去/欢乐勇士</h3>
              <p>生存冒险游戏</p>
              <el-tag size="small" type="success">新增</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 标枪王者 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('javelin-king')">
            <div class="game-icon javelin">
              <el-icon><Position /></el-icon>
            </div>
            <div class="game-info">
              <h3>标枪王者</h3>
              <p>竞技投掷游戏</p>
              <el-tag size="small" type="warning">新游</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 开心点点消 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card" shadow="hover" @click="navigateToGame('happy-match')">
            <div class="game-icon happy-match">
              <el-icon><Sugar /></el-icon>
            </div>
            <div class="game-info">
              <h3>开心点点消</h3>
              <p>休闲消除游戏</p>
              <el-tag size="small" type="danger">新增</el-tag>
            </div>
          </el-card>
        </el-col>

        <!-- 即将上线的游戏 -->
        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card coming-soon" shadow="hover">
            <div class="game-icon coming">
              <el-icon><VideoCameraFilled /></el-icon>
            </div>
            <div class="game-info">
              <h3>动作冒险</h3>
              <p>3D动作冒险游戏</p>
              <el-tag size="small" type="info">即将上线</el-tag>
            </div>
            <div class="coming-soon-overlay">
              <span>敬请期待</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card coming-soon" shadow="hover">
            <div class="game-icon coming">
              <el-icon><Football /></el-icon>
            </div>
            <div class="game-info">
              <h3>体育竞技</h3>
              <p>多人在线竞技</p>
              <el-tag size="small" type="info">开发中</el-tag>
            </div>
            <div class="coming-soon-overlay">
              <span>开发中</span>
            </div>
          </el-card>
        </el-col>

        <el-col :xs="24" :sm="12" :md="8" :lg="6">
          <el-card class="game-card coming-soon" shadow="hover">
            <div class="game-icon coming">
              <el-icon><Box /></el-icon>
            </div>
            <div class="game-info">
              <h3>沙盒世</h3>
              <p>创造你的世界</p>
              <el-tag size="small" type="info">规划中</el-tag>
            </div>
            <div class="coming-soon-overlay">
              <span>规划中</span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  Bell,
  Monitor,
  Connection,
  Service,
  Platform,
  Aim,
  House,
  Lollipop,
  Van,
  Trophy,
  VideoCameraFilled,
  Football,
  Box,
  Star,
  MagicStick,
  Position,
  Sugar
} from '@element-plus/icons-vue'

const router = useRouter()
const currentDate = ref(new Date().toLocaleDateString())

const navigateToGame = (route) => {
  router.push(`/recharge/${route}`)
}
</script>

<style scoped>
.dashboard {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
  box-sizing: border-box;
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;     /* Firefox */
}

/* Chrome, Safari and Opera */
.dashboard::-webkit-scrollbar {
  display: none;
}

/* 隐藏 el-scrollbar 的滚动条 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
  margin-right: -17px !important; /* 补偿滚动条宽度 */
  margin-bottom: -17px !important;
}

:deep(.el-scrollbar__view) {
  padding-right: 17px; /* 补偿边距 */
  padding-bottom: 17px;
}

:deep(.el-scrollbar__bar.is-horizontal) {
  display: none !important;
}

:deep(.el-scrollbar__bar.is-vertical) {
  display: none !important;
}

/* 确保内容区域有足够的空间 */
.el-scrollbar {
  min-height: calc(100vh - 84px);
}

/* 防止水平滚动 */
.el-scrollbar__wrap {
  overflow-x: hidden !important;
}

/* 确保行间距正确 */
.el-row--flex {
  flex-wrap: wrap;
  margin: -10px !important;
}

.el-col {
  padding: 10px !important;
}

/* 数据卡片样式 */
.data-cards {
  margin-bottom: 30px;
  margin-right: 0;
  margin-left: 0;
}

.data-card {
  height: 100%;
  transition: transform 0.3s;
}

.data-card:hover {
  transform: translateY(-5px);
}

.card-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-left {
  flex: 1;
}

.card-title {
  font-size: 14px;
  color: #909399;
  margin-bottom: 8px;
}

.card-value {
  font-size: 16px;
  font-weight: bold;
}

.card-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.card-icon.notice { background-color: #fef0f0; color: #f56c6c; }
.card-icon.status { background-color: #f0f9eb; color: #67c23a; }
.card-icon.api { background-color: #ecf5ff; color: #409eff; }
.card-icon.support { background-color: #f5f7fa; color: #909399; }

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #909399;
}

/* 游戏列表样式 */
.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}

.section-title .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.game-list {
  margin-right: 0;
  margin-left: 0;
  padding-bottom: 20px;
}

.game-card {
  height: 100%;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.game-card:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.game-card:hover .game-icon {
  transform: scale(1.1) rotate(5deg);
}

.game-icon {
  width: 70px;
  height: 70px;
  border-radius: 20px;
  margin: 0 auto 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32px;
  transition: all 0.3s ease;
  position: relative;
}

.game-icon::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background: inherit;
  filter: blur(8px);
  opacity: 0.6;
  z-index: -1;
  transition: all 0.3s ease;
}

/* 不同游戏的图标样式 */
.game-icon.gun {
  background: linear-gradient(135deg, #ff4757 0%, #ff6b81 100%);
  color: white;
}

.game-icon.shelter {
  background: linear-gradient(135deg, #2ed573 0%, #7bed9f 100%);
  color: white;
}

.game-icon.bubble {
  background: linear-gradient(135deg, #ffa502 0%, #ffdb58 100%);
  color: white;
}

.game-icon.car {
  background: linear-gradient(135deg, #5352ed 0%, #70a1ff 100%);
  color: white;
}

.game-icon.tower {
  background: linear-gradient(135deg, #8E44AD 0%, #9B59B6 100%);
  color: white;
}

.game-icon.cat {
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  color: white;
}

.game-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.game-info h3 {
  margin: 10px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.game-info p {
  color: #7f8c8d;
  font-size: 14px;
  margin: 8px 0 12px;
}

/* 标签样式 */
.el-tag {
  border-radius: 12px;
  padding: 2px 12px;
  font-weight: 500;
  border: none;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .dashboard {
    padding: 10px;
  }
  
  .data-cards {
    margin-bottom: 20px;
  }
  
  .el-col {
    padding: 5px;
  }

  .game-card {
    padding: 15px;
    margin-bottom: 15px;
  }

  .game-icon {
    width: 60px;
    height: 60px;
    font-size: 28px;
  }

  .game-info h3 {
    font-size: 16px;
  }

  .game-info p {
    font-size: 13px;
  }
}

/* 添加动画效果 */
@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
  100% {
    transform: translateY(0px);
  }
}

.game-card:hover .game-icon::after {
  transform: scale(1.2);
  opacity: 0.4;
}

/* 添加鼠标悬停时的光晕效果 */
.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    transparent 0%,
    rgba(255, 255, 255, 0.1) 50%,
    transparent 100%
  );
  transform: translateX(-100%);
  transition: transform 0.6s;
}

.game-card:hover::before {
  transform: translateX(100%);
}

/* 在其他游戏图标样式后添加 */
.game-icon.javelin {
  background: linear-gradient(135deg, #FF9966 0%, #FF5E62 100%);
  color: white;
}

.game-icon.happy-match {
  background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%);
  color: white;
}
</style>
