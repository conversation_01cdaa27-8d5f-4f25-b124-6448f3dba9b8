<template>
  <div class="operation-history" v-if="history.length > 0">
    <div class="history-header">
      <el-icon><Timer /></el-icon>
      <span>操作历史</span>
    </div>
    
    <el-timeline>
      <el-timeline-item
        v-for="(item, index) in history"
        :key="index"
        :type="item.status === '成功' ? 'success' : 'danger'"
        :timestamp="item.time"
      >
        <div class="history-item">
          <span class="action">{{ item.action }}</span>
          <el-tag 
            :type="item.status === '成功' ? 'success' : 'danger'"
            size="small"
          >
            {{ item.status }}
          </el-tag>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup>
import { Timer } from '@element-plus/icons-vue'
import { ElTag, ElTimeline, ElTimelineItem } from 'element-plus'

defineProps({
  history: {
    type: Array,
    default: () => []
  }
})
</script>

<style scoped>
.operation-history {
  margin-top: 30px;
  padding: 20px;
  border-radius: 8px;
  background-color: var(--el-bg-color);
  box-shadow: var(--el-box-shadow-lighter);
}

.history-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: bold;
}

.history-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
}

.action {
  font-weight: 500;
}

:deep(.el-timeline-item__node) {
  width: 12px;
  height: 12px;
}

:deep(.el-timeline-item__tail) {
  left: 5px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 25px;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}
</style> 