<template>
  <el-card class="user-stats" shadow="hover">
    <template #header>
      <h2>用户数据</h2>
    </template>
    <el-row :gutter="20">
      <el-col :span="8" :xs="12" v-for="(value, key) in stats" :key="key">
        <el-card shadow="never" class="stat-card">
          <template #header>
            <div class="stat-header">
              <el-icon :size="24" :color="iconColors[key]">
                <component :is="iconMap[key]" />
              </el-icon>
              <span>{{ statNames[key] }}</span>
            </div>
          </template>
          <div class="stat-value">{{ value }}</div>
        </el-card>
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import { Coin, Lightning, Star, Trophy, MagicStick } from '@element-plus/icons-vue'

export default {
  name: 'UserStats',
  components: {
    Coin,
    Lightning,
    Star,
    Trophy,
    MagicStick
  },
  props: {
    stats: {
      type: Object,
      required: true
    }
  },
  setup() {
    const iconMap = {
      gold: Coin,
      energy: Lightning,
      talent: Star,
      level: Trophy,
      talentStone: MagicStick
    }
    const iconColors = {
      gold: '#FFD700',
      energy: '#00BFFF',
      talent: '#FF69B4',
      level: '#32CD32',
      talentStone: '#9370DB'
    }
    const statNames = {
      gold: '金币',
      energy: '体力',
      talent: '天赋',
      level: '等级',
      talentStone: '天赋石'
    }
    return { iconMap, iconColors, statNames }
  }
}
</script>

<style scoped>
.user-stats {
  margin-bottom: 20px;
}
.stat-card {
  margin-bottom: 20px;
}
.stat-header {
  display: flex;
  align-items: center;
}
.stat-header span {
  margin-left: 8px;
  font-size: 14px;
  color: #606266;
}
.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}
</style>
