server {
    listen 9090;
    server_name ehjxld.duckdns.org ***************;
    root D:/xp.cn/www/wwwroot/admin/game/;

    # 添加DNS解析器
    resolver ******* ******* valid=300s;
    resolver_timeout 10s;

    # 处理前端路由
    location / {
        try_files $uri $uri/ /index.html;
        index index.html;
    }

    # 代理 /jsfs 请求
    location /jsfs {
        proxy_pass https://mini.fattoy.cn:8087;
        proxy_set_header Host mini.fattoy.cn:8087;
        proxy_set_header Origin https://servicewechat.com;
        proxy_set_header Referer https://servicewechat.com/wx969813315c7ff3d0/8/page-frame.html;
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/json;charset=UTF-8';
        proxy_set_header Accept '*/*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header xweb_xhr '1';
        
        proxy_ssl_verify off;  # 忽略SSL验证
        proxy_ssl_server_name on;
    }

    # 代理 /pdzs 请求
    location /pdzs {
        proxy_pass https://mini.fattoy.cn:8087;
        proxy_set_header Host mini.fattoy.cn:8087;
        proxy_set_header Origin https://servicewechat.com;
        proxy_set_header Referer https://servicewechat.com/wx969813315c7ff3d0/8/page-frame.html;
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/json;charset=UTF-8';
        
        proxy_ssl_verify off;  # 忽略SSL验证
        proxy_ssl_server_name on;
    }

    # 添加标枪王者的代理配置
    location /javelin-api/ {
        proxy_pass https://javelin.mandrillvr.com/api/;
        proxy_set_header Host javelin.mandrillvr.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wxfd5e6758e91c29e6/97/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded';
        proxy_set_header Accept '*/*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header xweb_xhr '1';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 添加开心点点消的代理配置
    # 下载数据接口
    location /happy-match/getData {
        proxy_pass https://gohappy.qzzgame.com/v1.0/data/getData;
        proxy_set_header Host gohappy.qzzgame.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx4a93eb6df699acf5/276/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded;charset=UTF-8';
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header xweb_xhr '1';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
    }

    # 上传数据接口
    location /happy-match/saveData {
        proxy_pass https://gohappy.qzzgame.com/v1.0/data/saveData;
        proxy_set_header Host gohappy.qzzgame.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx4a93eb6df699acf5/276/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded;charset=UTF-8';
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header xweb_xhr '1';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 添加甜甜爱消除的代理配置
    # 下载数据接口
    location /sweet-match/getData {
        proxy_pass https://gosweet.qzzgame.com/v1.0/data/getData;
        proxy_set_header Host gosweet.qzzgame.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx402e4490eba51720/37/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded;charset=UTF-8';
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header Accept-Encoding 'gzip, deflate, br';
        proxy_set_header xweb_xhr '1';
        proxy_set_header sec-fetch-site 'cross-site';
        proxy_set_header sec-fetch-mode 'cors';
        proxy_set_header sec-fetch-dest 'empty';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
    }

    # 上传数据接口
    location /sweet-match/saveData {
        proxy_pass https://gosweet.qzzgame.com/v1.0/data/saveData;
        proxy_set_header Host gosweet.qzzgame.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx402e4490eba51720/37/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded;charset=UTF-8';
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header Accept-Encoding 'gzip, deflate, br';
        proxy_set_header xweb_xhr '1';
        proxy_set_header sec-fetch-site 'cross-site';
        proxy_set_header sec-fetch-mode 'cors';
        proxy_set_header sec-fetch-dest 'empty';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 砍不过我呀的代理配置
    location /xsapi {
        proxy_pass https://yiyouzan.cn/xsapi;
        proxy_set_header Host yiyouzan.cn;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx7065d302cdfa0f48/21/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/json;charset=UTF-8';
        proxy_set_header Accept '*/*';
        proxy_set_header xweb_xhr '1';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 疯狂铁匠游戏代理配置
    location /kod/ {
        proxy_pass https://zomzom.qimiaohaiyu.com/kod/;
        proxy_set_header Host zomzom.qimiaohaiyu.com;
        proxy_set_header Referer 'https://servicewechat.com/wx48667cfa12792a52/8/page-frame.html';
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Accept '*/*';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded';
        proxy_set_header xweb_xhr '1';
        
        # 允许跨域
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,xweb_xhr';
        
        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,xweb_xhr';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
        
        # 错误处理
        proxy_intercept_errors on;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        
        # 超时设置
        proxy_connect_timeout 60;
        proxy_send_timeout 60;
        proxy_read_timeout 60;
        
        # 缓冲区设置
        proxy_buffer_size 16k;
        proxy_buffers 4 32k;
        proxy_busy_buffers_size 64k;
    }

    # 添加恐怖躲猫猫的代理配置
    # 下载和上传数据接口
    location /api/ {
        proxy_pass https://gcloud.qmhd87.com/api/;
        proxy_set_header Host gcloud.qmhd87.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx2bdca0cd94206228/14/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/json';
        proxy_set_header Accept '*/*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header Accept-Encoding 'gzip, deflate, br';
        proxy_set_header xweb_xhr '1';
        proxy_set_header Connection 'keep-alive';
        proxy_set_header sec-fetch-site 'cross-site';
        proxy_set_header sec-fetch-mode 'cors';
        proxy_set_header sec-fetch-dest 'empty';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 添加谁打过我呀游戏的代理配置
    # 初始化SDK接口
    location /who-beat-me/init {
        proxy_pass https://sdk-dj.youpingame.com/sdk/init.php?noencrypt=1;
        proxy_set_header Host sdk-dj.youpingame.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx969813315c7ff3d0/8/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded';
        proxy_set_header Accept '*/*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header xweb_xhr '1';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr,X-SID';

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr,X-SID';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 获取授权信息接口
    location /who-beat-me/auth {
        proxy_pass https://sdk-dj.youpingame.com/api/game_save/game_saves/tencent_cos_info;
        proxy_set_header Host sdk-dj.youpingame.com;
        proxy_set_header Origin 'https://servicewechat.com';
        proxy_set_header Referer 'https://servicewechat.com/wx969813315c7ff3d0/8/page-frame.html';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555';
        proxy_set_header Content-Type 'application/x-www-form-urlencoded';
        proxy_set_header Accept '*/*';
        proxy_set_header Accept-Language 'zh-CN,zh;q=0.9';
        proxy_set_header xweb_xhr '1';

        # 传递会话ID
        proxy_set_header Cookie "HUOSHUID=$http_x_sid";

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*';
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr,X-SID';

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,xweb_xhr,X-SID';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # 下载游戏数据接口
    location ~ ^/who-beat-me/download/([^?]+) {
        # 移除查询参数，只保留文件名
        set $filename $1;
        set $backend "yp-storage-1318351391.cos.ap-shanghai.myqcloud.com";
        proxy_pass https://$backend/218/$filename;
        proxy_set_header Host $backend;

        # 移除一些可能导致问题的请求头
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

        # 不设置Origin和Referer，避免CORS问题
        proxy_set_header Origin '';
        proxy_set_header Referer '';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        # SSL和超时设置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 错误处理和重试
        proxy_intercept_errors on;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;

        # 添加调试头
        add_header X-Debug-Backend $backend always;
        add_header X-Debug-Filename $filename always;
        add_header X-Debug-Full-URL "https://$backend/218/$filename" always;

        error_page 404 = @fallback_download;
    }

    # 下载数据的fallback处理
    location @fallback_download {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Content-Type' 'application/json' always;
        return 404 '{"error": "数据文件不存在", "message": "请检查游戏ID是否正确"}';
    }

    # 添加根本打不过游戏的代理配置
    # 测试接口 - 验证nginx配置是否生效
    location = /cant-beat-me/test {
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Content-Type' 'application/json' always;
        return 200 '{"status": "success", "message": "根本打不过游戏nginx配置正常", "timestamp": "$time_iso8601"}';
    }

    # 下载游戏数据接口 - 使用更精确的匹配
    location ^~ /cant-beat-me/download/ {
        # 提取文件名，移除查询参数
        if ($request_uri ~ ^/cant-beat-me/download/([^?]+)) {
            set $filename $1;
        }
        set $backend "yp-storage-1318351391.cos.ap-shanghai.myqcloud.com";
        proxy_pass https://$backend/215/$filename;
        proxy_set_header Host $backend;

        # 移除一些可能导致问题的请求头
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

        # 不设置Origin和Referer，避免CORS问题
        proxy_set_header Origin '';
        proxy_set_header Referer '';

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        # SSL和超时设置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;

        # 错误处理和重试
        proxy_intercept_errors on;
        proxy_next_upstream error timeout invalid_header http_500 http_502 http_503 http_504;
        proxy_next_upstream_tries 2;
        proxy_next_upstream_timeout 30s;

        # 添加调试头
        add_header X-Debug-Backend $backend always;
        add_header X-Debug-Filename $filename always;
        add_header X-Debug-Full-URL "https://$backend/215/$filename" always;

        error_page 404 = @fallback_download;
    }

    # 上传游戏数据接口
    location ~ ^/who-beat-me/upload/([^?]+) {
        # 移除查询参数，只保留文件名
        set $filename $1;
        set $backend "yp-storage-1318351391.cos.ap-shanghai.myqcloud.com";
        proxy_pass https://$backend/218/$filename;
        proxy_method PUT;
        proxy_set_header Host $backend;

        # 简化请求头
        proxy_set_header Content-Type 'application/json';
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

        # 不设置Origin和Referer
        proxy_set_header Origin '';
        proxy_set_header Referer '';

        # 传递授权头
        proxy_set_header Authorization $http_authorization;

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        # SSL和超时设置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲区设置，适合大文件上传
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # 根本打不过游戏上传数据接口
    location ~ ^/cant-beat-me/upload/([^?]+) {
        # 移除查询参数，只保留文件名
        set $filename $1;
        set $backend "yp-storage-1318351391.cos.ap-shanghai.myqcloud.com";
        proxy_pass https://$backend/215/$filename;
        proxy_method PUT;
        proxy_set_header Host $backend;

        # 简化请求头
        proxy_set_header Content-Type 'application/json';
        proxy_set_header Accept 'application/json, text/plain, */*';
        proxy_set_header User-Agent 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36';

        # 不设置Origin和Referer
        proxy_set_header Origin '';
        proxy_set_header Referer '';

        # 传递授权头
        proxy_set_header Authorization $http_authorization;

        # 允许跨域设置
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, OPTIONS' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization' always;

        # 处理 OPTIONS 请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, PUT, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' 0;
            return 204;
        }

        # SSL和超时设置
        proxy_ssl_verify off;
        proxy_ssl_server_name on;
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;

        # 缓冲区设置，适合大文件上传
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires max;
        log_not_found off;
        access_log off;
        add_header Cache-Control "public, no-transform";
    }

    # 错误页面配置
    error_page 403 /error/403.html;
    error_page 404 /error/404.html;
    error_page 502 /error/502.html;
    error_page 503 /error/503.html;

    # 日志配置
    access_log D:/xp.cn/www/wwwroot/admin/ehjxld.duckdns.org_80/log/nginx_access_$logdate.log main;
}