<template>
  <div class="cultivator-simulator">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <VideoPlay />
              </el-icon>
              <span>散修生活模拟器</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 数据输入区域 -->
          <div class="data-input-section card-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon>
                  <Document />
                </el-icon>
                <span>游戏数据</span>
              </div>
              <el-button type="danger" size="small" class="clear-button" @click="handleClearData" :icon="Delete">
                清空数据
              </el-button>
            </div>

            <!-- 玩家ID输入框 -->
            <el-form :model="formData" label-position="top">
              <el-form-item label="修仙者ID">
                <el-input v-model="formData.userId" placeholder="请输入修仙者ID" clearable
                  :disabled="loading.download || loading.upload" />
              </el-form-item>
            </el-form>

            <!-- 操作按钮 -->
            <div class="action-section">
              <el-button type="primary" :loading="loading.download" @click="handleDownload" :icon="Download"
                class="action-button">
                下载数据
              </el-button>
              <el-button type="success" :loading="loading.upload" @click="handleUpload" :icon="Upload"
                :disabled="!hasData" class="action-button">
                上传数据
              </el-button>
            </div>

            <!-- 套餐按钮 -->
            <div class="package-section">
              <el-button type="danger" @click="handlePackageOne" :disabled="!hasData" class="package-button"
                :icon="Star">
                套餐一 (钻石/金币/锻造石/宠物蛋/VIP/自动打怪/辅助系统)
              </el-button>
              <el-button type="warning" @click="handlePackageTwo" :disabled="!hasData" class="package-button"
                :icon="MagicStick">
                套餐二 (装备管理/宠物碎片)
              </el-button>
              <el-button type="success" @click="handlePackageThree" :disabled="!hasData" class="package-button"
                :icon="IceCream">
                套餐三 (冰火符文/宠物装备/邀请满级)
              </el-button>
            </div>
          </div>

          <!-- 数据展示区域 -->
          <div v-if="hasData" class="data-display-section">
            <el-tabs type="border-card">
              <!-- 基础信息标签页 -->
              <el-tab-pane label="基础信息">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="OpenID">{{ gameData.openId }}</el-descriptions-item>
                  <el-descriptions-item label="版本号">{{ gameData.version }}</el-descriptions-item>
                  <el-descriptions-item label="用户ID">{{ gameData.userId }}</el-descriptions-item>
                  <el-descriptions-item label="压缩状态">
                    {{ gameData.multiCompressed ? '多重压缩' : (gameData.isContentCompressed ? '单重压缩' : '未压缩') }}
                  </el-descriptions-item>
                  <el-descriptions-item label="压缩次数">{{ gameData.compressionCount || 0 }}</el-descriptions-item>
                  <el-descriptions-item label="数据状态">
                    <el-tag :type="gameData.code === 1 ? 'success' : 'warning'">
                      {{ getStatusText(gameData.code) }}
                    </el-tag>
                  </el-descriptions-item>

                  <!-- 游戏系统信息 -->
                  <template v-if="gameData.contentJson?.MAIN?.mgrGame">
                    <el-descriptions-item label="游戏版本">
                      {{ gameData.contentJson.MAIN.mgrGame.gameVersion }}
                    </el-descriptions-item>
                    <el-descriptions-item label="公告版本">
                      {{ gameData.contentJson.MAIN.mgrGame.announcementVersionId }}
                    </el-descriptions-item>
                    <el-descriptions-item label="自动攻击">
                      <el-tag :type="gameData.contentJson.MAIN.mgrGame.bAutoAttack ? 'success' : 'info'">
                        {{ gameData.contentJson.MAIN.mgrGame.bAutoAttack ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="自动终结技">
                      <el-tag :type="gameData.contentJson.MAIN.mgrGame.bAutoFinalSkill ? 'success' : 'info'">
                        {{ gameData.contentJson.MAIN.mgrGame.bAutoFinalSkill ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                  </template>

                  <!-- 签到系统信息 -->
                  <template v-if="gameData.contentJson?.MAIN?.mgrSign">
                    <el-descriptions-item label="当前签到索引">
                      <el-input-number v-model="gameData.contentJson.MAIN.mgrSign.curSignIndex" :min="0"
                        @change="handleSignDataChange" size="small" />
                    </el-descriptions-item>
                    <el-descriptions-item label="签到状态">
                      <el-select v-model="gameData.contentJson.MAIN.mgrSign.state" @change="handleSignDataChange"
                        size="small" style="width: 120px;">
                        <el-option :value="1" label="未签到" />
                        <el-option :value="2" label="已签到" />
                      </el-select>
                    </el-descriptions-item>
                  </template>
                </el-descriptions>
              </el-tab-pane>

              <!-- 物品系统标签页 -->
              <el-tab-pane label="物品系统" v-if="gameData.contentJson?.MAIN?.mgrItems">
                <div class="data-section">
                  <el-table :data="computedItemList" border height="400" :header-cell-style="{ background: '#f5f7fa' }"
                    v-loading="loading.tableData">
                    <el-table-column prop="itemId" label="物品ID" width="120" />
                    <el-table-column prop="name" label="物品名称" width="150" />
                    <el-table-column prop="amount" label="数量">
                      <template #default="scope">
                        <el-input-number v-model="scope.row.amount" :min="0"
                          :precision="scope.row.itemId === '2' ? 8 : 0" @change="handleItemAmountChange(scope.row)" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 角色系统标签页 -->
              <el-tab-pane label="角色系统" v-if="gameData.contentJson?.MAIN?.mgrRole">
                <div class="data-section">
                  <!-- 基础信息区域 - 两列布局 -->
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form :model="gameData.contentJson.MAIN.mgrRole" label-width="100px">
                        <el-form-item label="角色等级">
                          <el-input-number v-model="gameData.contentJson.MAIN.mgrRole.level" :min="1"
                            @change="handleRoleDataChange" style="width: 200px;" />
                        </el-form-item>
                        <el-form-item label="当前角色">
                          <el-select v-model="gameData.contentJson.MAIN.mgrRole.usedRoleId"
                            @change="handleRoleDataChange" style="width: 200px;">
                            <el-option v-for="role in ['roleYasuo', 'roleMonkey', 'roleJiansheng', 'roleTongren']"
                              :key="role" :label="getItemName(role)" :value="role" />
                          </el-select>
                        </el-form-item>
                      </el-form>
                    </el-col>
                    <el-col :span="12">
                      <el-form :model="gameData.contentJson.MAIN.mgrRole" label-width="100px">
                        <el-form-item label="击败怪物">
                          <el-input-number v-model="gameData.contentJson.MAIN.mgrRole.defeatMonsterAmount" :min="0"
                            @change="handleRoleDataChange" style="width: 200px;" />
                        </el-form-item>
                        <el-form-item label="能量进度">
                          <el-input-number v-model="gameData.contentJson.MAIN.mgrRole.skillPowerProgress" :min="0"
                            @change="handleRoleDataChange" style="width: 200px;" />
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>

                  <el-divider content-position="left">角色属性配置</el-divider>

                  <!-- 一键添加区域 -->
                  <div class="add-buttons-section">
                    <el-form :inline="true">
                      <el-form-item label="觉醒等级" class="custom-form-item">
                        <el-input-number v-model="newAwaken.level" :min="1" style="width: 150px;" />
                      </el-form-item>
                      <el-form-item class="button-form-item">
                        <el-button type="primary" @click="handleAddRoleAwaken">一键添加其他角色</el-button>
                      </el-form-item>
                      <el-form-item label="属性等级" class="custom-form-item">
                        <el-input-number v-model="newAttribute.level" :min="1" :max="99999999" style="width: 150px;" />
                      </el-form-item>
                      <el-form-item class="button-form-item">
                        <el-button type="success" @click="handleAddAttributes">一键添加属性等级</el-button>
                      </el-form-item>
                    </el-form>
                  </div>

                  <!-- 角色觉醒和属性等级并排显示 -->
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <h4>角色觉醒等级</h4>
                      <!-- 角色觉醒表格 -->
                      <el-table :data="computedRoleAwakenList" border height="350"
                        :header-cell-style="{ background: '#f5f7fa' }" v-loading="loading.tableData">
                        <el-table-column prop="roleId" label="角色" width="150">
                          <template #default="scope">
                            {{ getItemName(scope.row.roleId) }}
                          </template>
                        </el-table-column>
                        <el-table-column prop="level" label="觉醒等级">
                          <template #default="scope">
                            <el-input-number
                              v-model="gameData.contentJson.MAIN.mgrRole.roleAwakenLevel[scope.row.roleId]" :min="1"
                              @change="handleRoleDataChange" />
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>

                    <el-col :span="12">
                      <h4>属性等级</h4>
                      <!-- 属性等级表格 -->
                      <el-table :data="computedAttributeList" border height="350"
                        :header-cell-style="{ background: '#f5f7fa' }" v-loading="loading.tableData">
                        <el-table-column prop="name" label="属性" width="150" />
                        <el-table-column prop="level" label="等级">
                          <template #default="scope">
                            <el-input-number v-model="gameData.contentJson.MAIN.mgrRole.attributeLevel[scope.row.key]"
                              :min="1" @change="handleRoleDataChange" />
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-col>
                  </el-row>
                </div>
              </el-tab-pane>

              <!-- 自动打怪标签页 -->
              <el-tab-pane label="自动打怪" v-if="gameData.contentJson?.MAIN?.mgrAutoClick">
                <div class="data-section">
                  <el-form :model="gameData.contentJson.MAIN.mgrAutoClick" label-width="120px">
                    <el-form-item label="剩余时间(秒)">
                      <el-input-number v-model="gameData.contentJson.MAIN.mgrAutoClick.leftDuration" :min="0"
                        :precision="2" :step="60" @change="handleAutoClickDataChange" />
                    </el-form-item>
                    <el-form-item label="等级">
                      <el-tooltip content="等级最大值为30" placement="top"
                        :popper-style="{ color: '#f56c6c', backgroundColor: 'transparent', border: 'none' }">
                        <el-input-number v-model="gameData.contentJson.MAIN.mgrAutoClick.level" :min="1" :max="30"
                          @change="handleAutoClickDataChange" />
                      </el-tooltip>
                    </el-form-item>
                    <el-form-item label="广告次数">
                      <el-input-number v-model="gameData.contentJson.MAIN.mgrAutoClick.adCount" :min="0"
                        @change="handleAutoClickDataChange" />
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>

              <!-- VIP系统标签页 -->
              <el-tab-pane label="VIP系统" v-if="gameData.contentJson?.MAIN?.mgrVip">
                <div class="data-section">
                  <el-form :model="gameData.contentJson.MAIN.mgrVip" label-width="120px">
                    <el-form-item label="VIP状态">
                      <el-switch v-model="gameData.contentJson.MAIN.mgrVip.bUnlocked" @change="handleVipDataChange" />
                    </el-form-item>
                    <el-form-item label="广告次数">
                      <el-tooltip content="广告次数最高为800" placement="top"
                        :popper-style="{ color: '#f56c6c', backgroundColor: 'transparent', border: 'none' }">
                        <el-input-number v-model="gameData.contentJson.MAIN.mgrVip.adCount" :min="0" :max="800"
                          @change="handleVipDataChange" />
                      </el-tooltip>
                    </el-form-item>
                    <el-form-item label="上次领取等级">
                      <el-input-number v-model="gameData.contentJson.MAIN.mgrVip.lastObtainLevel" :min="1"
                        @change="handleVipDataChange" />
                    </el-form-item>
                    <el-form-item label="上次领取时间">
                      <el-date-picker v-model="gameData.contentJson.MAIN.mgrVip.lastObtainRewardTime" type="datetime"
                        placeholder="选择日期时间" format="YYYY-MM-DD HH:mm:ss" value-format="x"
                        :default-time="new Date(2000, 1, 1, 0, 0, 0)" @change="handleVipDataChange" />
                      <span style="margin-left: 10px; color: #666; font-size: 12px">
                        {{ formatUnixTimestamp(gameData.contentJson.MAIN.mgrVip.lastObtainRewardTime) }}
                      </span>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>

              <!-- 辅助系统标签页 -->
              <el-tab-pane label="辅助系统" v-if="gameData.contentJson?.MAIN?.mgrUZI">
                <div class="data-section">
                  <el-form :model="gameData.contentJson.MAIN.mgrUZI" label-width="120px">
                    <el-form-item label="辅助状态">
                      <el-switch v-model="gameData.contentJson.MAIN.mgrUZI.bUnlocked" @change="handleUZIDataChange" />
                    </el-form-item>
                    <el-form-item label="辅助等级">
                      <el-tooltip content="辅助等级最高为90级" placement="top"
                        :popper-style="{ color: '#f56c6c', backgroundColor: 'transparent', border: 'none' }">
                        <el-input-number v-model="gameData.contentJson.MAIN.mgrUZI.level" :min="1" :max="90"
                          @change="handleUZIDataChange" />
                      </el-tooltip>
                    </el-form-item>
                  </el-form>
                </div>
              </el-tab-pane>

              <!-- 装备管理标签页 -->
              <el-tab-pane label="装备管理" v-if="gameData.contentJson?.MAIN?.mgrEquipment || hasData.value">
                <div class="data-section">
                  <el-form label-width="140px">
                    <el-form-item label="当前掉落装备ID">
                      <el-input v-model="equipmentData.curDropingEquipmentId" @change="handleEquipmentDataChange"
                        placeholder="例如：3_1" style="width: 200px;" />
                    </el-form-item>
                  </el-form>

                  <div class="equipment-header">
                    <h4>装备强化与锻造</h4>
                    <el-button type="primary" @click="handleAddMissingEquipment" size="small">
                      一键添加6个装备
                    </el-button>
                  </div>

                  <el-table :data="computedEquipmentList" border height="400"
                    :header-cell-style="{ background: '#f5f7fa' }" v-loading="loading.tableData">
                    <el-table-column prop="position" label="装备位置" width="100">
                      <template #default="scope">
                        {{ getEquipmentPositionName(scope.row.position) }}
                      </template>
                    </el-table-column>
                    <el-table-column prop="level" label="强化等级" width="200">
                      <template #default="scope">
                        <el-input-number v-model="scope.row.level" :min="1" :max="99"
                          @change="handleEquipmentLevelChange(scope.row)" />
                      </template>
                    </el-table-column>
                    <el-table-column prop="forgeLevel" label="锻造等级" width="200">
                      <template #default="scope">
                        <el-input-number v-model="scope.row.forgeLevel" :min="0" :max="20"
                          @change="handleEquipmentForgeLevelChange(scope.row)" />
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 任务系统标签页 -->
              <el-tab-pane label="任务系统" v-if="gameData.contentJson?.MAIN?.mgrBaseTask">
                <div class="data-section">
                  <el-table :data="computedTaskList" border height="400" :header-cell-style="{ background: '#f5f7fa' }"
                    v-loading="loading.tableData">
                    <el-table-column prop="id" label="任务ID" width="120" />
                    <el-table-column prop="name" label="任务名称" />
                    <el-table-column prop="state" label="状态" width="120">
                      <template #default="scope">
                        <el-select v-model="scope.row.state" @change="handleTaskStateChange(scope.row)">
                          <el-option label="进行中" value="进行中" />
                          <el-option label="已完成" value="已完成" />
                          <el-option label="已领取" value="已领取" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="进度" width="200">
                      <template #default="scope">
                        <template v-if="scope.row.progress !== undefined">
                          <el-input-number v-model="scope.row.progress" :min="0" :max="scope.row.target"
                            @change="handleTaskProgressChange(scope.row)" />
                          /{{ scope.row.target }}
                          ({{ scope.row.percentage }})
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 邀请任务标签页 -->
              <el-tab-pane label="邀请任务" v-if="gameData.contentJson?.MAIN?.mgrBaseTask">
                <div class="data-section">
                  <el-table :data="computedInviteTaskList" border height="400"
                    :header-cell-style="{ background: '#f5f7fa' }" v-loading="loading.tableData">
                    <el-table-column prop="id" label="任务ID" width="120" />
                    <el-table-column prop="name" label="任务名称" />
                    <el-table-column prop="state" label="状态" width="120">
                      <template #default="scope">
                        <el-select v-model="scope.row.state" @change="handleTaskStateChange(scope.row)">
                          <el-option label="进行中" value="进行中" />
                          <el-option label="已完成" value="已完成" />
                          <el-option label="已领取" value="已领取" />
                        </el-select>
                      </template>
                    </el-table-column>
                    <el-table-column label="进度" width="200">
                      <template #default="scope">
                        <template v-if="scope.row.progress !== undefined">
                          <el-input-number v-model="scope.row.progress" :min="0" :max="scope.row.target"
                            @change="handleTaskProgressChange(scope.row)" />
                          /{{ scope.row.target }}
                          ({{ scope.row.percentage }})
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </el-tab-pane>

              <!-- 原始数据标签页 -->
              <el-tab-pane label="原始数据">
                <div class="raw-data-container">
                  <div class="raw-data-toolbar">
                    <el-button type="primary" size="small" @click="copyRawData" :icon="CopyDocument">
                      复制原始数据
                    </el-button>
                    <el-button type="success" size="small" @click="formatRawData" :icon="MagicStick">
                      格式化
                    </el-button>
                  </div>
                  <div class="raw-data-wrapper">
                    <pre class="raw-data-code"><code>{{ rawDataStr }}</code></pre>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, Download, Upload, Delete, VideoPlay, CopyDocument, MagicStick, Star, IceCream } from '@element-plus/icons-vue'
import axios from 'axios'
import pako from 'pako'

// 表单数据
const formData = reactive({
  userId: 'opKsB5j0hI_2IKcH3kcX05gkk3TA'
})

// 游戏数据
const gameData = reactive({})

// 加载状态
const loading = reactive({
  download: false,
  upload: false,
  tableData: false
})

// 是否有数据
const hasData = ref(false)

// 原始数据字符串
const rawDataStr = ref('')

// 新增角色觉醒等级
const newAwaken = reactive({
  level: 1
})

// 新增属性等级
const newAttribute = reactive({
  level: 99999999
})

// 全部角色ID列表
const allRoleIds = ['roleYasuo', 'roleMonkey', 'roleJiansheng', 'roleTongren']

// 获取状态文本
const getStatusText = (code) => {
  switch (code) {
    case 0: return '无数据或请求失败'
    case 1: return '成功'
    case 2: return '数据未变更'
    default: return '未知状态'
  }
}

// 获取物品数量
const getItemAmount = (itemDatas, itemId) => {
  if (!itemDatas || !itemDatas[itemId] || !itemDatas[itemId][0]) return '0'
  return itemDatas[itemId][0].amount.toString()
}

// 获取任务状态类型
const getTaskStateType = (state) => {
  switch (state) {
    case '已完成': return 'success'
    case '进行中': return 'primary'
    case '已领取': return 'info'
    default: return ''
  }
}

// 获取任务列表
const getTaskList = (taskSystem) => {
  const tasks = []

  // 添加完成的任务
  if (taskSystem.finishedBaseTaskIds) {
    tasks.push(...taskSystem.finishedBaseTaskIds
      .filter(id => !id.startsWith('BaseInvite_')) // 排除邀请任务
      .map(id => ({
        id,
        state: '已完成',
        name: `任务${id}`
      })))
  }

  // 添加已领取奖励的任务
  if (taskSystem.rewardedBaseTaskIds) {
    tasks.push(...taskSystem.rewardedBaseTaskIds
      .filter(id => !id.startsWith('BaseInvite_')) // 排除邀请任务
      .map(id => ({
        id,
        state: '已领取',
        name: `任务${id}`
      })))
  }

  // 添加进行中的任务
  if (taskSystem.baseTaskInfos) {
    tasks.push(...Object.entries(taskSystem.baseTaskInfos)
      .filter(([id, task]) => (task.state === 1 || task.state === 2) && !id.startsWith('BaseInvite_')) // 排除邀请任务
      .map(([id, task]) => ({
        id,
        state: '进行中',
        name: `任务${id}`,
        progress: task.curProgress,
        target: task.targetProgress,
        percentage: Math.floor((task.curProgress / task.targetProgress) * 100) + '%'
      })))
  }

  return tasks
}

// 获取邀请任务列表
const getInviteTaskList = (taskSystem) => {
  const tasks = []

  // 添加完成的邀请任务
  if (taskSystem.finishedBaseTaskIds) {
    tasks.push(...taskSystem.finishedBaseTaskIds
      .filter(id => id.startsWith('BaseInvite_')) // 只包含邀请任务
      .map(id => ({
        id,
        state: '已完成',
        name: getInviteTaskName(id)
      })))
  }

  // 添加已领取奖励的邀请任务
  if (taskSystem.rewardedBaseTaskIds) {
    tasks.push(...taskSystem.rewardedBaseTaskIds
      .filter(id => id.startsWith('BaseInvite_')) // 只包含邀请任务
      .map(id => ({
        id,
        state: '已领取',
        name: getInviteTaskName(id)
      })))
  }

  // 添加进行中的邀请任务
  if (taskSystem.baseTaskInfos) {
    tasks.push(...Object.entries(taskSystem.baseTaskInfos)
      .filter(([id, task]) => (task.state === 1 || task.state === 2) && id.startsWith('BaseInvite_')) // 只包含邀请任务
      .map(([id, task]) => ({
        id,
        state: '进行中',
        name: getInviteTaskName(id),
        progress: task.curProgress,
        target: task.targetProgress,
        percentage: Math.floor((task.curProgress / task.targetProgress) * 100) + '%'
      })))
  }

  // 确保所有邀请任务都在列表中
  const inviteTaskIds = ['BaseInvite_01', 'BaseInvite_02', 'BaseInvite_03', 'BaseInvite_04', 'BaseInvite_05']
  const existingIds = tasks.map(task => task.id)

  // 添加不存在的邀请任务
  inviteTaskIds.forEach(id => {
    if (!existingIds.includes(id)) {
      tasks.push({
        id,
        state: '进行中',
        name: getInviteTaskName(id),
        progress: 0,
        target: 1,
        percentage: '0%'
      })
    }
  })

  return tasks
}

// 获取邀请任务名称
const getInviteTaskName = (taskId) => {
  const inviteTaskNames = {
    'BaseInvite_01': '邀请1位好友',
    'BaseInvite_02': '邀请2位好友',
    'BaseInvite_03': '邀请3位好友',
    'BaseInvite_04': '邀请4位好友',
    'BaseInvite_05': '邀请5位好友'
  }
  return inviteTaskNames[taskId] || `邀请任务${taskId}`
}

// 清空数据
const handleClearData = () => {
  formData.userId = ''
  Object.keys(gameData).forEach(key => {
    delete gameData[key]
  })
  hasData.value = false
  rawDataStr.value = ''
  ElMessage.success('数据已清空')
}

// 解析响应数据
const parseResponse = (responseData) => {
  const dataView = new DataView(responseData)

  // 提取元数据
  const code = dataView.getUint8(0)
  const version = dataView.getInt32(1, true)
  const token = dataView.getInt32(5, true)

  // 提取压缩数据
  const compressedData = new Uint8Array(responseData.slice(9))

  return {
    code,
    version,
    token: token.toString(),
    compressedData
  }
}

// base64 解码函数
const base64ToUint8Array = (base64) => {
  const binaryString = atob(base64)
  const length = binaryString.length
  const bytes = new Uint8Array(length)
  for (let i = 0; i < length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }
  return bytes
}

// 递归解压内容
const recursiveDecompress = (content, compressionCount = 0, maxIterations = 10) => {
  if (compressionCount >= maxIterations) {
    return {
      result: content,
      compressionCount,
      success: false
    }
  }

  if (typeof content === "string" && content.startsWith("H4sI")) {
    try {
      const compressedData = base64ToUint8Array(content)
      const decompressedStr = pako.inflate(compressedData, { to: 'string' })
      return recursiveDecompress(decompressedStr, compressionCount + 1, maxIterations)
    } catch (error) {
      return {
        result: content,
        compressionCount,
        success: false,
        error
      }
    }
  }

  return {
    result: content,
    compressionCount,
    success: true
  }
}

// 下载数据
const handleDownload = async () => {
  if (!formData.userId) {
    ElMessage.warning('请输入修仙者ID')
    return
  }

  loading.download = true
  try {
    const params = {
      gameId: '1358',
      channelId: '138',
      secret: 'ada56d2sd',
      version: '0',
      userId: formData.userId,
      platform: '1'
    }

    const url = `https://gamedata.ultralisk.cn/getuserdatav8?${new URLSearchParams(params)}`

    const response = await axios.get(url, {
      responseType: 'arraybuffer',
      headers: {
        'Accept': '*/*',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'bCheck': '1',
        'Connection': 'keep-alive',
        'Content-Type': 'application/x-www-form-urlencoded',
        'Host': 'gamedata.ultralisk.cn',
        'Referer': 'https://servicewechat.com/wx9be03b8b0f4d5b16/23/page-frame.html',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555'
      }
    })

    const result = parseResponse(response.data)
    Object.assign(gameData, result)

    if (result.code === 1) {
      // 解压数据
      let decodedData
      const firstChar = String.fromCharCode(result.compressedData[0])
      if (firstChar === '{') {
        decodedData = new TextDecoder().decode(result.compressedData)
      } else {
        decodedData = pako.inflate(result.compressedData, { to: 'string' })
      }

      const jsonData = JSON.parse(decodedData)

      // 保存外层属性
      gameData.openId = jsonData.openId
      gameData.version = jsonData.version
      gameData.userId = jsonData.userId

      // 处理content字段
      if (jsonData.content) {
        const contentResult = recursiveDecompress(jsonData.content)
        if (contentResult.success) {
          try {
            const contentJson = JSON.parse(contentResult.result)
            gameData.contentJson = contentJson
            gameData.isContentCompressed = contentResult.compressionCount > 0
            gameData.multiCompressed = contentResult.compressionCount > 1
            gameData.compressionCount = contentResult.compressionCount
          } catch (error) {
            console.error('解析content JSON失败:', error)
          }
        }
      }

      rawDataStr.value = JSON.stringify(jsonData, null, 2)
      hasData.value = true
      ElMessage.success('数据下载成功')
    } else if (result.code === 2) {
      ElMessage.info('数据未变更，使用客户端缓存数据')
    } else {
      ElMessage.warning('无数据或请求失败')
    }
  } catch (error) {
    console.error('下载数据失败:', error)
    ElMessage.error('下载数据失败: ' + error.message)
  } finally {
    loading.download = false
  }
}

// 上传数据
const handleUpload = async () => {
  if (!formData.userId) {
    ElMessage.warning('请输入修仙者ID')
    return
  }

  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  if (!gameData.token) {
    ElMessage.warning('无法获取token，请重新下载数据')
    return
  }

  loading.upload = true
  try {
    // 将version值加1
    const newVersion = parseInt(gameData.version) + 1

    // 构建请求参数
    const params = {
      gameId: '1358',
      channelId: '138',
      secret: 'ada56d2sd',
      version: newVersion.toString(), // 使用新的version值
      userId: formData.userId,
      platform: '1',
      token: gameData.token
    }

    const url = `https://gamedata.ultralisk.cn/saveuserdatav8?${new URLSearchParams(params)}`

    // 从原始JSON中获取数据结构
    const originalData = JSON.parse(rawDataStr.value)
    const jsonData = {
      openId: gameData.openId,
      version: newVersion.toString(), // 使用新的version值
      userId: gameData.userId
    }

    // 处理content字段
    if (gameData.contentJson) {
      // 将修改后的contentJson转为字符串
      const modifiedContentStr = JSON.stringify(gameData.contentJson)

      // 根据原数据的压缩状态决定是否需要压缩
      if (gameData.isContentCompressed) {
        // 执行压缩
        if (gameData.multiCompressed && gameData.compressionCount > 1) {
          // 多重压缩
          let compressed = modifiedContentStr
          for (let i = 0; i < gameData.compressionCount; i++) {
            const compressedData = pako.gzip(compressed)
            compressed = btoa(
              String.fromCharCode.apply(null, compressedData)
            )
          }
          jsonData.content = compressed
        } else {
          // 单层压缩
          const compressedData = pako.gzip(modifiedContentStr)
          jsonData.content = btoa(
            String.fromCharCode.apply(null, compressedData)
          )
        }
      } else {
        // 不压缩
        jsonData.content = modifiedContentStr
      }
    }

    // 将整个数据结构转为JSON字符串
    const jsonStr = JSON.stringify(jsonData)

    // 方式2: 发送压缩后的二进制数据
    try {
      // 对整个JSON进行压缩
      const compressedFullData = pako.gzip(jsonStr)
      const blob = new Blob([compressedFullData], { type: 'application/octet-stream' })

      ElMessage.info('正在使用压缩二进制方式上传数据...')

      const response = await axios.post(url, blob, {
        headers: {
          'Content-Type': 'application/octet-stream',
          'bCheck': '1',
          'Accept': '*/*',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Connection': 'keep-alive',
          'Host': 'gamedata.ultralisk.cn',
          'Referer': 'https://servicewechat.com/wx9be03b8b0f4d5b16/23/page-frame.html',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555'
        }
      })

      if (response.status === 200) {
        // 更新本地保存的版本号
        gameData.version = newVersion.toString()

        // 更新原始数据中的版本号
        const updatedJsonData = JSON.parse(rawDataStr.value)
        updatedJsonData.version = newVersion.toString()
        rawDataStr.value = JSON.stringify(updatedJsonData, null, 2)

        ElMessage.success(`数据上传成功! 版本号已更新为${newVersion}`)
      } else {
        throw new Error(`请求状态异常: ${response.status}`)
      }
    } catch (error) {
      console.error('压缩二进制上传失败:', error)
      ElMessage.error(`上传失败: ${error.message}`)
    }
  } catch (error) {
    console.error('上传数据失败:', error)
    ElMessage.error('上传数据失败: ' + error.message)
  } finally {
    loading.upload = false
  }
}

// 获取物品列表
const getItemList = (itemDatas) => {
  if (!itemDatas) return []
  return Object.entries(itemDatas).map(([itemId, data]) => ({
    itemId,
    name: getItemName(itemId),
    amount: data[0].amount
  }))
}

// 获取物品名称
const getItemName = (itemId) => {
  const itemNames = {
    '1': '钻石',
    '2': '金币',
    'PET_EGG': '宠物蛋',
    'roleYasuo': '亚索',
    'roleMonkey': '悟空',
    'roleJiansheng': '剑圣',
    'roleTongren': '桐仁',
    'skin01': '默认皮肤',
    'skin301': '特殊皮肤',
    'FORGED_STONE': '锻造石',
    'SOUL_STONE': '灵魂石',
    'ENERGY_STONE': '能量石',
    'MAGIC_STONE': '魔法石',
    'SPIRIT_STONE': '精神石',
    'DRAGON_SCALE': '龙鳞',
    'PHOENIX_FEATHER': '凤凰羽',
    'IMMORTAL_ESSENCE': '仙灵精华',
    'DEMON_CORE': '魔核',
    'VOID_CRYSTAL': '虚空晶体',
    'ETERNAL_FLAME': '永恒之火',
    'CELESTIAL_SILK': '天衣无缝',
    'DIVINE_WOOD': '神木',
    'CHAOS_ORB': '混沌之球',
    'THUNDER_JADE': '雷霆玉',
    'FROST_GEM': '霜冻宝石',
    'BLOOD_PEARL': '血珠',
    'STARLIGHT_DUST': '星辰尘',
    'ANCIENT_SCROLL': '古卷',
    'MYSTIC_HERB': '仙草',
    'BEAST_FANG': '兽牙',
    'GALAXY_FRAGMENT': '星河碎片',
    'WISDOM_PAGE': '智慧之页',
    'HERO_MEDAL': '英雄勋章',
    'SKY_TEAR': '天之泪',
    'EARTH_CORE': '地心核',
    'FORTUNE_TOKEN': '幸运令牌',
    'DREAM_THREAD': '梦幻之线',
    // 新增物品
    'DETERRENCE_FRUIT': '威慑果实',
    'ATTACK_FRUIT': '攻击果实',
    'DOUBLEHIT_FRUIT': '连击果实',
    'CRITICAL_DAMAGE_FRUIT': '暴击伤害果实',
    'skin101': '皮肤101',
    'skin102': '皮肤102',
    'skin103': '皮肤103',
    'skin104': '皮肤104',
    'skin108': '皮肤108',
    'skin107': '皮肤107',
    'skin106': '皮肤106',
    'skin03': '皮肤03',
    'skin02': '皮肤02',
    'skin04': '皮肤04',
    '1_1': '金币碎片',
    '3_1': '灵石',
    '5_1': '修为石',
    '6_1': '突破丹',
    'FIRE_DR_BUFF_CD_LV1': '龙族CD增益Lv1',
    'FIRE_DR_BUFF_CD_LV2': '龙族CD增益Lv2',
    'FIRE_DR_BOOM_REAL_HURT_LV1': '龙族爆发伤害Lv1',
    'FIRE_DR_BOOM_REAL_HURT_LV2': '龙族爆发伤害Lv2',
    'FIRE_CY_BOOM_ENERGY_BACK_LV2': '青云爆发能量回复Lv2',
    'FIRE_YS_BOOM_DURATION_LV1': '亚索爆发持续时间Lv1',
    'FIRE_YS_BOOM_DURATION_LV2': '亚索爆发持续时间Lv2',
    'FIRE_ZS_BOOM_HURT_LV1': '剑圣爆发伤害Lv1',
    'FIRE_ZS_BOOM_HURT_LV2': '剑圣爆发伤害Lv2',
    'FIRE_ZS_IM_HURT_LV1': '剑圣瞬伤Lv1',
    'FIRE_ZS_IM_HURT_LV2': '剑圣瞬伤Lv2',
    'FIRE_YS_ACTIVE_VERTIGO_LV1': '亚索主动眩晕Lv1',
    'FIRE_YS_ACTIVE_VERTIGO_LV2': '亚索主动眩晕Lv2',
    'FIRE_ZS_HURT_RATIO_LV2': '剑圣伤害比例Lv2',
    'FIRE_ZS_HURT_RATIO_LV3': '剑圣伤害比例Lv3',
    'FIRE_CY_IM_ENERGY_LV2': '青云瞬时能量Lv2',
    'FIRE_DR_HURT_RATIO_LV1': '龙族伤害比例Lv1',
    'FIRE_DR_HURT_RATIO_LV2': '龙族伤害比例Lv2',
    'FIRE_DR_IM_HURT_LV2': '龙族瞬伤Lv2',
    'FIRE_ZS_BUFF_CD_LV2': '剑圣CD增益Lv2',
    'ICE_ARENA_ENERGY_RATIO_LV1': '冰系能量比例Lv1',
    'ICE_ARENA_ENERGY_RATIO_LV2': '冰系能量比例Lv2',
    'ICE_ARENA_DURATION_RATIO_LV1': '冰系持续比例Lv1',
    'ICE_ARENA_BREAK_HURT_RATIO_LV1': '冰系破冰伤害比例Lv1',
    'ICE_BOOM_RATIO_LV1': '冰系爆发比例Lv1',
    'ICE_BREAK_AMOUNT_LV1': '冰系破冰数量Lv1',
    'ICE_BREAK_HURT_RATIO_LV1': '冰系破冰伤害比例Lv1',
    'ICE_BREAK_HURT_COUNT_LV1': '冰系破冰伤害次数Lv1',
    'ICE_CIGU_HURT_CD_LV1': '冰系刺骨伤害CDLv1',
    'ICE_CIGU_HURT_CD_LV2': '冰系刺骨伤害CDLv2'
  }
  return itemNames[itemId] || itemId
}

// 获取角色觉醒列表
const getRoleAwakenList = (awakenData) => {
  if (!awakenData) return []
  return Object.entries(awakenData).map(([roleId, level]) => ({
    roleId,
    level
  }))
}

// 获取属性列表
const getAttributeList = (attributeData) => {
  if (!attributeData) return []
  const attributeNames = {
    'ATTACK': '攻击',
    'DOUBLEHIT': '连击',
    'CRITICAL_DAMAGE': '暴击伤害',
    'CRITICAL_RATIO': '暴击率'
  }
  return Object.entries(attributeData).map(([attr, level]) => ({
    key: attr,
    name: attributeNames[attr] || attr,
    level
  }))
}

// 获取签到状态文本
const getSignState = (state) => {
  switch (state) {
    case 2: return '已签到'
    case 1: return '未签到'
    default: return '未知状态'
  }
}

// 格式化时间戳
const formatTime = (timestamp) => {
  if (!timestamp) return '无'
  const date = new Date(timestamp * 1000)
  return date.toLocaleString()
}

// 处理物品数量变更
const handleItemAmountChange = (item) => {
  if (!gameData.contentJson?.MAIN?.mgrItems?.itemDatas[item.itemId]) {
    gameData.contentJson.MAIN.mgrItems.itemDatas[item.itemId] = [{
      itemId: item.itemId,
      amount: item.amount
    }]
  } else {
    gameData.contentJson.MAIN.mgrItems.itemDatas[item.itemId][0].amount = item.amount
  }
  updateRawData()
}

// 处理角色数据变更
const handleRoleDataChange = () => {
  updateRawData()
}

// 处理VIP数据变更
const handleVipDataChange = () => {
  updateRawData()
}

// 处理签到数据变更
const handleSignDataChange = () => {
  updateRawData()
}

// 处理任务状态变更
const handleTaskStateChange = (task) => {
  const taskSystem = gameData.contentJson.MAIN.mgrBaseTask

  // 从所有列表中移除该任务
  if (taskSystem.finishedBaseTaskIds) {
    taskSystem.finishedBaseTaskIds = taskSystem.finishedBaseTaskIds.filter(id => id !== task.id)
  }
  if (taskSystem.rewardedBaseTaskIds) {
    taskSystem.rewardedBaseTaskIds = taskSystem.rewardedBaseTaskIds.filter(id => id !== task.id)
  }

  // 根据新状态添加到对应列表
  switch (task.state) {
    case '已完成':
      if (!taskSystem.finishedBaseTaskIds) taskSystem.finishedBaseTaskIds = []
      taskSystem.finishedBaseTaskIds.push(task.id)
      break
    case '已领取':
      if (!taskSystem.rewardedBaseTaskIds) taskSystem.rewardedBaseTaskIds = []
      taskSystem.rewardedBaseTaskIds.push(task.id)
      break
  }

  updateRawData()
}

// 处理任务进度变更
const handleTaskProgressChange = (task) => {
  if (gameData.contentJson?.MAIN?.mgrBaseTask?.baseTaskInfos?.[task.id]) {
    gameData.contentJson.MAIN.mgrBaseTask.baseTaskInfos[task.id].curProgress = task.progress
    task.percentage = Math.floor((task.progress / task.target) * 100) + '%'
  }
  updateRawData()
}

// 处理自动打怪数据变更
const handleAutoClickDataChange = () => {
  updateRawData()
}

// 处理辅助系统数据变更
const handleUZIDataChange = () => {
  updateRawData()
}

// 防抖函数
const debounce = (fn, delay) => {
  let timer = null
  return function (...args) {
    if (timer) clearTimeout(timer)
    timer = setTimeout(() => {
      fn.apply(this, args)
    }, delay)
  }
}

// 处理 ResizeObserver 错误
const handleResizeError = () => {
  const resizeHandler = debounce(() => {
    const messages = [
      'ResizeObserver loop limit exceeded',
      'ResizeObserver loop completed with undelivered notifications'
    ]

    const originalError = window.console.error
    window.console.error = (...args) => {
      if (args.length > 0 && typeof args[0] === 'string') {
        const shouldIgnore = messages.some(message => args[0].includes(message))
        if (shouldIgnore) return
      }
      originalError.apply(window.console, args)
    }
  }, 100)

  window.addEventListener('resize', resizeHandler)
  return () => {
    window.removeEventListener('resize', resizeHandler)
  }
}

// 生命周期钩子
onMounted(() => {
  const cleanup = handleResizeError()

  // 添加全局 ResizeObserver 错误处理
  const messages = [
    'ResizeObserver loop limit exceeded',
    'ResizeObserver loop completed with undelivered notifications'
  ]

  const originalError = window.console.error
  window.console.error = (...args) => {
    if (args.length > 0 && typeof args[0] === 'string') {
      const shouldIgnore = messages.some(message => args[0].includes(message))
      if (shouldIgnore) return
    }
    originalError.apply(window.console, args)
  }

  onUnmounted(() => {
    cleanup()
    window.console.error = originalError
  })
})

// 表格数据计算属性
const computedItemList = computed(() => {
  return getItemList(gameData.contentJson?.MAIN?.mgrItems?.itemDatas || {})
})

const computedRoleAwakenList = computed(() => {
  return getRoleAwakenList(gameData.contentJson?.MAIN?.mgrRole?.roleAwakenLevel || {})
})

const computedAttributeList = computed(() => {
  return getAttributeList(gameData.contentJson?.MAIN?.mgrRole?.attributeLevel || {})
})

const computedTaskList = computed(() => {
  return getTaskList(gameData.contentJson?.MAIN?.mgrBaseTask || {})
})

const computedInviteTaskList = computed(() => {
  return getInviteTaskList(gameData.contentJson?.MAIN?.mgrBaseTask || {})
})

// 获取装备位置名称
const getEquipmentPositionName = (position) => {
  const positionNames = {
    '1': '武器',
    '2': '头盔',
    '3': '护甲',
    '4': '项链',
    '5': '手套',
    '6': '鞋子'
  }
  return positionNames[position] || `位置${position}`
}

// 获取装备列表
const getEquipmentList = (equipmentData) => {
  if (!equipmentData) return []

  const result = []

  // 处理装备强化等级
  if (equipmentData.pos2Level) {
    Object.entries(equipmentData.pos2Level).forEach(([position, level]) => {
      const existingItem = result.find(item => item.position === position)
      if (existingItem) {
        existingItem.level = level
      } else {
        result.push({
          position,
          level,
          forgeLevel: equipmentData.pos2ForgeLevel?.[position] || 0
        })
      }
    })
  }

  // 处理装备锻造等级
  if (equipmentData.pos2ForgeLevel) {
    Object.entries(equipmentData.pos2ForgeLevel).forEach(([position, forgeLevel]) => {
      const existingItem = result.find(item => item.position === position)
      if (existingItem) {
        existingItem.forgeLevel = forgeLevel
      } else {
        result.push({
          position,
          level: 1,
          forgeLevel
        })
      }
    })
  }

  // 确保装备按位置排序
  result.sort((a, b) => parseInt(a.position) - parseInt(b.position))

  return result
}

// 处理装备数据变更
const handleEquipmentDataChange = () => {
  // 确保mgrEquipment对象存在
  if (!gameData.contentJson.MAIN.mgrEquipment) {
    gameData.contentJson.MAIN.mgrEquipment = {
      pos2Level: {},
      pos2ForgeLevel: {}
    }
  }

  // 更新当前掉落装备ID
  if (equipmentData.curDropingEquipmentId) {
    gameData.contentJson.MAIN.mgrEquipment.curDropingEquipmentId = equipmentData.curDropingEquipmentId
  }

  updateRawData()
}

// 处理装备强化等级变更
const handleEquipmentLevelChange = (equipment) => {
  // 确保mgrEquipment对象存在
  if (!gameData.contentJson.MAIN.mgrEquipment) {
    gameData.contentJson.MAIN.mgrEquipment = {
      pos2Level: {},
      pos2ForgeLevel: {}
    }
  }

  // 确保pos2Level对象存在
  if (!gameData.contentJson.MAIN.mgrEquipment.pos2Level) {
    gameData.contentJson.MAIN.mgrEquipment.pos2Level = {}
  }

  // 更新强化等级
  gameData.contentJson.MAIN.mgrEquipment.pos2Level[equipment.position] = equipment.level

  updateRawData()
}

// 处理装备锻造等级变更
const handleEquipmentForgeLevelChange = (equipment) => {
  // 确保mgrEquipment对象存在
  if (!gameData.contentJson.MAIN.mgrEquipment) {
    gameData.contentJson.MAIN.mgrEquipment = {
      pos2Level: {},
      pos2ForgeLevel: {}
    }
  }

  // 确保pos2ForgeLevel对象存在
  if (!gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel) {
    gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel = {}
  }

  // 更新锻造等级
  gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel[equipment.position] = equipment.forgeLevel

  updateRawData()
}

// 一键添加缺失的装备
const handleAddMissingEquipment = () => {
  // 确保mgrEquipment对象存在
  if (!gameData.contentJson.MAIN) {
    gameData.contentJson.MAIN = {}
  }

  if (!gameData.contentJson.MAIN.mgrEquipment) {
    gameData.contentJson.MAIN.mgrEquipment = {
      pos2Level: {},
      pos2ForgeLevel: {}
    }
  }

  // 确保pos2Level和pos2ForgeLevel对象存在
  if (!gameData.contentJson.MAIN.mgrEquipment.pos2Level) {
    gameData.contentJson.MAIN.mgrEquipment.pos2Level = {}
  }

  if (!gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel) {
    gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel = {}
  }

  // 所有装备位置
  const positions = ['1', '2', '3', '4', '5', '6']

  // 添加缺失的装备
  let addedCount = 0
  positions.forEach(position => {
    // 检查强化等级是否已存在
    if (!gameData.contentJson.MAIN.mgrEquipment.pos2Level[position]) {
      gameData.contentJson.MAIN.mgrEquipment.pos2Level[position] = 25
      addedCount++
    }

    // 检查锻造等级是否已存在
    if (!gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel[position]) {
      gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel[position] = 15
    }
  })

  // 设置当前掉落装备ID
  if (!gameData.contentJson.MAIN.mgrEquipment.curDropingEquipmentId) {
    gameData.contentJson.MAIN.mgrEquipment.curDropingEquipmentId = "3_1"
  }

  updateRawData()
  ElMessage.success(`成功添加${addedCount}个装备`)
}

// 装备数据对象（响应式）
const equipmentData = reactive({
  curDropingEquipmentId: ''
})

// 监听gameData变化，更新装备数据
const updateEquipmentData = () => {
  if (gameData.contentJson?.MAIN?.mgrEquipment) {
    equipmentData.curDropingEquipmentId = gameData.contentJson.MAIN.mgrEquipment.curDropingEquipmentId || ''
  }
}

// 表格数据计算属性
const computedEquipmentList = computed(() => {
  return getEquipmentList(gameData.contentJson?.MAIN?.mgrEquipment || {})
})

// 更新原始数据（防抖处理）
const updateRawData = debounce(() => {
  if (gameData.contentJson) {
    rawDataStr.value = JSON.stringify(gameData.contentJson, null, 2)
    updateEquipmentData() // 更新装备数据
  }
}, 300)

// 处理标签页切换
const handleTabChange = (tab) => {
  loading.tableData = true
  nextTick(() => {
    loading.tableData = false
  })
}

// 格式化Unix时间戳
const formatUnixTimestamp = (timestamp) => {
  if (!timestamp) return '无时间数据'

  // 检查时间戳的长度，如果是13位（毫秒），直接使用，如果是10位（秒），转换为毫秒
  const timestampMs = String(timestamp).length > 10 ? Number(timestamp) : Number(timestamp) * 1000

  try {
    const date = new Date(timestampMs)
    if (isNaN(date.getTime())) return '无效时间'

    return date.toLocaleString()
  } catch (error) {
    console.error('格式化时间戳失败:', error, timestamp)
    return '格式化错误'
  }
}

// 复制原始数据
const copyRawData = () => {
  if (!rawDataStr.value) {
    ElMessage.warning('没有数据可复制')
    return
  }

  navigator.clipboard.writeText(rawDataStr.value)
    .then(() => {
      ElMessage.success('数据已复制到剪贴板')
    })
    .catch(err => {
      console.error('复制失败:', err)
      ElMessage.error('复制失败，请手动选择并复制')
    })
}

// 格式化原始数据
const formatRawData = () => {
  if (!rawDataStr.value) {
    ElMessage.warning('没有数据可格式化')
    return
  }

  try {
    const jsonData = JSON.parse(rawDataStr.value)
    rawDataStr.value = JSON.stringify(jsonData, null, 2)
    ElMessage.success('数据格式化成功')
  } catch (error) {
    console.error('格式化失败:', error)
    ElMessage.error('数据格式化失败: ' + error.message)
  }
}

// 处理添加角色觉醒等级
const handleAddRoleAwaken = () => {
  if (!gameData.contentJson?.MAIN?.mgrRole) {
    ElMessage.warning('角色数据不存在')
    return
  }

  if (!gameData.contentJson.MAIN.mgrRole.roleAwakenLevel) {
    gameData.contentJson.MAIN.mgrRole.roleAwakenLevel = {}
  }

  // 检查现有的角色觉醒等级
  const existingRoles = Object.keys(gameData.contentJson.MAIN.mgrRole.roleAwakenLevel)

  // 找出缺失的角色
  const missingRoles = allRoleIds.filter(roleId => !existingRoles.includes(roleId))

  if (missingRoles.length === 0) {
    ElMessage.info('已拥有所有角色的觉醒等级')
    return
  }

  // 添加所有缺失的角色觉醒等级
  let addedCount = 0
  missingRoles.forEach(roleId => {
    gameData.contentJson.MAIN.mgrRole.roleAwakenLevel[roleId] = newAwaken.level
    addedCount++
  })

  // 更新数据
  updateRawData()
  ElMessage.success(`成功添加${addedCount}个角色的觉醒等级`)
}

// 处理添加属性等级
const handleAddAttributes = () => {
  if (!gameData.contentJson?.MAIN?.mgrRole) {
    ElMessage.warning('角色数据不存在')
    return
  }

  if (!gameData.contentJson.MAIN.mgrRole.attributeLevel) {
    gameData.contentJson.MAIN.mgrRole.attributeLevel = {}
  }

  // 所有属性
  const attributes = ['ATTACK', 'DOUBLEHIT', 'CRITICAL_DAMAGE', 'CRITICAL_RATIO']
  let addedCount = 0

  // 添加所有属性等级
  attributes.forEach(attr => {
    if (!gameData.contentJson.MAIN.mgrRole.attributeLevel[attr]) {
      gameData.contentJson.MAIN.mgrRole.attributeLevel[attr] = newAttribute.level
      addedCount++
    } else {
      gameData.contentJson.MAIN.mgrRole.attributeLevel[attr] = newAttribute.level
    }
  })

  // 更新数据
  updateRawData()
  ElMessage.success(`成功设置${addedCount}个属性等级`)
}

// 处理套餐一
const handlePackageOne = () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  // 设置物品数量
  if (!gameData.contentJson?.MAIN?.mgrItems) {
    if (!gameData.contentJson.MAIN) {
      gameData.contentJson.MAIN = {}
    }
    gameData.contentJson.MAIN.mgrItems = {
      itemDatas: {}
    }
  }

  if (!gameData.contentJson.MAIN.mgrItems.itemDatas) {
    gameData.contentJson.MAIN.mgrItems.itemDatas = {}
  }

  // 设置钻石 '1'
  if (!gameData.contentJson.MAIN.mgrItems.itemDatas['1']) {
    gameData.contentJson.MAIN.mgrItems.itemDatas['1'] = [{ amount: 9999999999 }]
  } else {
    gameData.contentJson.MAIN.mgrItems.itemDatas['1'][0].amount = 9999999999
  }

  // 设置金币 '2'
  if (!gameData.contentJson.MAIN.mgrItems.itemDatas['2']) {
    gameData.contentJson.MAIN.mgrItems.itemDatas['2'] = [{ amount: 9999999999 }]
  } else {
    gameData.contentJson.MAIN.mgrItems.itemDatas['2'][0].amount = 9999999999
  }

  // 设置锻造石 'FORGED_STONE'
  if (!gameData.contentJson.MAIN.mgrItems.itemDatas['FORGED_STONE']) {
    gameData.contentJson.MAIN.mgrItems.itemDatas['FORGED_STONE'] = [{ amount: 99999999 }]
  } else {
    gameData.contentJson.MAIN.mgrItems.itemDatas['FORGED_STONE'][0].amount = 99999999
  }

  // 设置宠物蛋 'PET_EGG'
  if (!gameData.contentJson.MAIN.mgrItems.itemDatas['PET_EGG']) {
    gameData.contentJson.MAIN.mgrItems.itemDatas['PET_EGG'] = [{ amount: 99999 }]
  } else {
    gameData.contentJson.MAIN.mgrItems.itemDatas['PET_EGG'][0].amount = 99999
  }

  // 设置VIP系统
  if (!gameData.contentJson.MAIN.mgrVip) {
    gameData.contentJson.MAIN.mgrVip = {
      bUnlocked: true,
      adCount: 800,
      lastObtainLevel: 1,
      lastObtainRewardTime: Date.now()
    }
  } else {
    gameData.contentJson.MAIN.mgrVip.bUnlocked = true
    gameData.contentJson.MAIN.mgrVip.adCount = 800
  }

  // 设置自动打怪
  if (!gameData.contentJson.MAIN.mgrAutoClick) {
    gameData.contentJson.MAIN.mgrAutoClick = {
      leftDuration: 999999,
      level: 13,
      adCount: 0
    }
  } else {
    gameData.contentJson.MAIN.mgrAutoClick.level = 13
    gameData.contentJson.MAIN.mgrAutoClick.leftDuration = 999999
  }

  // 设置辅助系统
  if (!gameData.contentJson.MAIN.mgrUZI) {
    gameData.contentJson.MAIN.mgrUZI = {
      level: 90,
      bUnlocked: true
    }
  } else {
    gameData.contentJson.MAIN.mgrUZI.level = 90
    gameData.contentJson.MAIN.mgrUZI.bUnlocked = true
  }

  // 更新原始数据
  updateRawData()
  ElMessage.success('套餐一设置成功！已设置无限钻石/金币/锻造石/宠物蛋/VIP/自动打怪/辅助系统')
}

// 处理套餐二
const handlePackageTwo = () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  // 确保mgrEquipment对象存在
  if (!gameData.contentJson.MAIN) {
    gameData.contentJson.MAIN = {}
  }

  // 添加装备
  if (!gameData.contentJson.MAIN.mgrEquipment) {
    gameData.contentJson.MAIN.mgrEquipment = {
      pos2Level: {},
      pos2ForgeLevel: {}
    }
  }

  // 确保pos2Level和pos2ForgeLevel对象存在
  if (!gameData.contentJson.MAIN.mgrEquipment.pos2Level) {
    gameData.contentJson.MAIN.mgrEquipment.pos2Level = {}
  }

  if (!gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel) {
    gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel = {}
  }

  // 所有装备位置
  const positions = ['1', '2', '3', '4', '5', '6']

  // 添加缺失的装备
  let addedCount = 0
  positions.forEach(position => {
    // 检查强化等级是否已存在
    if (!gameData.contentJson.MAIN.mgrEquipment.pos2Level[position]) {
      gameData.contentJson.MAIN.mgrEquipment.pos2Level[position] = 25
      addedCount++
    }

    // 检查锻造等级是否已存在
    if (!gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel[position]) {
      gameData.contentJson.MAIN.mgrEquipment.pos2ForgeLevel[position] = 15
    }
  })

  // 设置当前掉落装备ID
  if (!gameData.contentJson.MAIN.mgrEquipment.curDropingEquipmentId) {
    gameData.contentJson.MAIN.mgrEquipment.curDropingEquipmentId = "3_1"
  }

  // 确保mgrItems对象存在
  if (!gameData.contentJson.MAIN.mgrItems) {
    gameData.contentJson.MAIN.mgrItems = {
      itemDatas: {}
    }
  }

  // 添加宠物碎片
  for (let i = 1; i <= 4; i++) {
    const petId = `pet01_${i}`
    if (!gameData.contentJson.MAIN.mgrItems.itemDatas[petId]) {
      gameData.contentJson.MAIN.mgrItems.itemDatas[petId] = [{ itemId: petId, amount: 99999 }]
    } else {
      gameData.contentJson.MAIN.mgrItems.itemDatas[petId][0].amount = 99999
    }
  }

  // 更新原始数据
  updateRawData()
  ElMessage.success(`套餐二设置成功！已添加${addedCount}个装备和宠物碎片`)
}

// 处理套餐三
const handlePackageThree = () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  // 确保mgrItems对象存在
  if (!gameData.contentJson.MAIN) {
    gameData.contentJson.MAIN = {}
  }

  if (!gameData.contentJson.MAIN.mgrItems) {
    gameData.contentJson.MAIN.mgrItems = {
      itemDatas: {}
    }
  }

  // 设置冰系符文
  const iceRunes = [
    'ICE_ARENA_ENERGY_RATIO_LV1',
    'ICE_ARENA_ENERGY_RATIO_LV2',
    'ICE_ARENA_DURATION_RATIO_LV1',
    'ICE_ARENA_BREAK_HURT_RATIO_LV1',
    'ICE_BOOM_RATIO_LV1',
    'ICE_BREAK_AMOUNT_LV1',
    'ICE_BREAK_HURT_RATIO_LV1',
    'ICE_BREAK_HURT_COUNT_LV1',
    'ICE_CIGU_HURT_CD_LV1',
    'ICE_CIGU_HURT_CD_LV2'
  ]

  // 设置火系符文
  const fireRunes = [
    'FIRE_DR_BUFF_CD_LV1',
    'FIRE_DR_BUFF_CD_LV2',
    'FIRE_DR_BOOM_REAL_HURT_LV1',
    'FIRE_DR_BOOM_REAL_HURT_LV2',
    'FIRE_CY_BOOM_ENERGY_BACK_LV1',
    'FIRE_CY_BOOM_ENERGY_BACK_LV2',
    'FIRE_YS_BOOM_DURATION_LV1',
    'FIRE_YS_BOOM_DURATION_LV2',
    'FIRE_ZS_BOOM_HURT_LV1',
    'FIRE_ZS_BOOM_HURT_LV2',
    'FIRE_ZS_IM_HURT_LV1',
    'FIRE_ZS_IM_HURT_LV2',
    'FIRE_YS_ACTIVE_VERTIGO_LV1',
    'FIRE_YS_ACTIVE_VERTIGO_LV2',
    'FIRE_ZS_HURT_RATIO_LV1',
    'FIRE_ZS_HURT_RATIO_LV2',
    'FIRE_ZS_HURT_RATIO_LV3',
    'FIRE_CY_IM_ENERGY_LV1',
    'FIRE_CY_IM_ENERGY_LV2',
    'FIRE_DR_HURT_RATIO_LV1',
    'FIRE_DR_HURT_RATIO_LV2',
    'FIRE_DR_IM_HURT_LV1',
    'FIRE_DR_IM_HURT_LV2',
    'FIRE_ZS_BUFF_CD_LV1',
    'FIRE_ZS_BUFF_CD_LV2'
  ]

  // 设置冰火符文数量
  const setRuneAmount = (runeId) => {
    if (!gameData.contentJson.MAIN.mgrItems.itemDatas[runeId]) {
      gameData.contentJson.MAIN.mgrItems.itemDatas[runeId] = [{ itemId: runeId, amount: 9999 }]
    } else {
      gameData.contentJson.MAIN.mgrItems.itemDatas[runeId][0].amount = 9999
    }
  }

  // 添加所有冰火符文
  iceRunes.forEach(setRuneAmount)
  fireRunes.forEach(setRuneAmount)

  // 添加宠物装备
  for (let part = 1; part <= 4; part++) {
    for (let level = 1; level <= 4; level++) {
      const petEquipId = `pet_part${part}_001_lv${level}`
      if (!gameData.contentJson.MAIN.mgrItems.itemDatas[petEquipId]) {
        gameData.contentJson.MAIN.mgrItems.itemDatas[petEquipId] = [{ itemId: petEquipId, amount: 999999 }]
      } else {
        gameData.contentJson.MAIN.mgrItems.itemDatas[petEquipId][0].amount = 999999
      }
    }
  }

  // 设置邀请任务为已完成
  if (!gameData.contentJson.MAIN.mgrBaseTask) {
    gameData.contentJson.MAIN.mgrBaseTask = {
      finishedBaseTaskIds: [],
      rewardedBaseTaskIds: [],
      baseTaskInfos: {}
    }
  }

  const inviteTaskIds = ['BaseInvite_01', 'BaseInvite_02', 'BaseInvite_03', 'BaseInvite_04', 'BaseInvite_05']

  // 将所有邀请任务设置为已完成
  inviteTaskIds.forEach(taskId => {
    if (!gameData.contentJson.MAIN.mgrBaseTask.finishedBaseTaskIds) {
      gameData.contentJson.MAIN.mgrBaseTask.finishedBaseTaskIds = []
    }
    if (!gameData.contentJson.MAIN.mgrBaseTask.finishedBaseTaskIds.includes(taskId)) {
      gameData.contentJson.MAIN.mgrBaseTask.finishedBaseTaskIds.push(taskId)
    }
  })

  // 更新原始数据
  updateRawData()
  ElMessage.success('套餐三设置成功！已添加无限冰火符文、宠物装备和邀请满级')
}
</script>

<style scoped>
.cultivator-simulator {
  padding: 20px;
}

.main-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.title {
  display: flex;
  align-items: center;
  font-size: 22px;
  font-weight: bold;
}

.title .el-icon {
  margin-right: 10px;
  font-size: 24px;
  color: #409EFF;
}

.status-tag {
  margin-left: 10px;
}

.data-input-section {
  margin-bottom: 20px;
}

.card-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(235, 238, 245, 0.5);
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.section-title .el-icon {
  margin-right: 10px;
  font-size: 20px;
  color: #42b983;
}

.action-section {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
}

.action-button {
  flex: 1;
  height: 44px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.package-section {
  margin-top: 16px;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  width: 100%;
}

.package-button {
  height: 44px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  border: none;
  white-space: normal;
  line-height: 1.2;
  padding: 8px 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.package-button :deep(.el-icon) {
  margin-right: 5px;
  flex-shrink: 0;
}

.package-button:first-child {
  background: linear-gradient(135deg, #f56c6c, #e83e8c);
}

.package-button:nth-child(2) {
  background: linear-gradient(135deg, #e6a23c, #f56c6c);
}

.package-button:nth-child(3) {
  background: linear-gradient(135deg, #67c23a, #42b983);
}

.package-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.package-button:first-child:hover {
  background: linear-gradient(135deg, #e83e8c, #f56c6c);
}

.package-button:nth-child(2):hover {
  background: linear-gradient(135deg, #f56c6c, #e6a23c);
}

.package-button:nth-child(3):hover {
  background: linear-gradient(135deg, #42b983, #67c23a);
}

.data-display-section {
  margin-top: 20px;
}

.data-section {
  margin-bottom: 24px;
}

.data-section h3 {
  margin-bottom: 16px;
  color: #409EFF;
  font-size: 18px;
  font-weight: 600;
}

.raw-data-container {
  display: flex;
  flex-direction: column;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.raw-data-toolbar {
  display: flex;
  justify-content: flex-end;
  padding: 8px 12px;
  background-color: #e9ecef;
  border-bottom: 1px solid #dee2e6;
  gap: 8px;
}

.raw-data-wrapper {
  padding: 0;
  overflow: auto;
  max-height: 600px;
  position: relative;
}

.raw-data-code {
  margin: 0;
  padding: 16px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
  background-color: #282c34;
  color: #f8f8f2;
  border-radius: 0 0 6px 6px;
  overflow-x: auto;
  tab-size: 2;
  white-space: pre;
}

/* JSON 语法高亮 */
.raw-data-code :deep(.string) {
  color: #a8ff60;
}

.raw-data-code :deep(.number) {
  color: #ff9d00;
}

.raw-data-code :deep(.boolean) {
  color: #569cd6;
}

.raw-data-code :deep(.null) {
  color: #ff628c;
}

.raw-data-code :deep(.key) {
  color: #9cdcfe;
}

:deep(.el-descriptions) {
  margin-bottom: 20px;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-table) {
  margin-top: 12px;
}

.el-table {
  transition: all 0.3s ease-in-out;
}

.el-table__body-wrapper {
  overflow-y: auto;
  overflow-x: hidden;
}

.el-table--scrollable-y .el-table__body-wrapper {
  overflow-y: auto;
}

.el-table--enable-row-hover .el-table__body tr:hover>td {
  background-color: #f5f7fa !important;
}

.el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #fafafa;
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 20px;
}

.equipment-header h4 {
  margin: 0;
  color: #409EFF;
  font-size: 16px;
}

.add-attribute-form {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.add-awaken-form {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.add-buttons-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.add-buttons-section :deep(.el-form--inline .el-form-item) {
  margin-right: 0;
  margin-bottom: 0;
}

.add-buttons-section :deep(.el-form--inline) {
  display: flex;
  flex-wrap: nowrap;
  align-items: center;
  justify-content: flex-start;
  gap: 16px;
}

.add-buttons-section :deep(.custom-form-item) {
  margin-right: 8px;
  margin-bottom: 0;
}

.add-buttons-section :deep(.button-form-item) {
  margin-right: 24px;
  margin-bottom: 0;
}

.add-buttons-section :deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

.add-buttons-section :deep(.el-input-number) {
  width: 150px !important;
}

.add-buttons-section :deep(.el-button) {
  min-width: 120px;
}
</style>
