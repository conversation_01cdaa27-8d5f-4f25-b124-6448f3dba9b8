<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>77can游戏数据加密解密工具</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <style>
      :root {
        --primary-color: #3498db;
        --secondary-color: #2980b9;
        --success-color: #2ecc71;
        --danger-color: #e74c3c;
        --dark-color: #34495e;
        --light-color: #ecf0f1;
        --gray-color: #bdc3c7;
      }

      * {
        box-sizing: border-box;
        margin: 0;
        padding: 0;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f9f9f9;
        padding: 20px;
      }

      header {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      h1 {
        color: var(--primary-color);
        text-align: center;
        margin-bottom: 10px;
      }

      .header-desc {
        text-align: center;
        color: #666;
        max-width: 800px;
        margin: 0 auto;
      }

      .container {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        max-width: 1200px;
        margin: 0 auto;
      }

      .box {
        background-color: #fff;
        border-radius: 8px;
        padding: 20px;
        flex: 1 1 45%;
        min-width: 300px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      }

      h2 {
        color: var(--dark-color);
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid var(--gray-color);
      }

      .form-group {
        margin-bottom: 15px;
      }

      label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
        color: var(--dark-color);
      }

      textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid var(--gray-color);
        border-radius: 4px;
        min-height: 150px;
        font-family: monospace;
        resize: vertical;
      }

      button {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 4px;
        cursor: pointer;
        font-weight: bold;
        transition: background-color 0.3s;
      }

      button:hover {
        background-color: var(--secondary-color);
      }

      .btn-success {
        background-color: var(--success-color);
      }

      .btn-success:hover {
        background-color: #27ae60;
      }

      .btn-danger {
        background-color: var(--danger-color);
      }

      .btn-danger:hover {
        background-color: #c0392b;
      }

      .output {
        margin-top: 15px;
        min-height: 100px;
        max-height: 400px;
        overflow-y: auto;
        background-color: #f5f5f5;
        border: 1px solid var(--gray-color);
        border-radius: 4px;
        padding: 10px;
        font-family: monospace;
        white-space: pre-wrap;
        word-break: break-all;
      }

      .buttons {
        display: flex;
        gap: 10px;
        margin-bottom: 15px;
      }

      .alert {
        padding: 10px;
        margin: 10px 0;
        border-radius: 4px;
        color: white;
      }

      .alert-success {
        background-color: var(--success-color);
      }

      .alert-error {
        background-color: var(--danger-color);
      }

      footer {
        text-align: center;
        margin-top: 30px;
        color: #666;
        padding: 20px;
        border-top: 1px solid var(--gray-color);
      }

      .hidden {
        display: none;
      }

      .tooltip {
        position: relative;
        display: inline-block;
        cursor: pointer;
        color: var(--primary-color);
        margin-left: 5px;
      }

      .tooltip .tooltiptext {
        visibility: hidden;
        width: 200px;
        background-color: #555;
        color: #fff;
        text-align: center;
        border-radius: 6px;
        padding: 5px;
        position: absolute;
        z-index: 1;
        bottom: 125%;
        left: 50%;
        transform: translateX(-50%);
        opacity: 0;
        transition: opacity 0.3s;
      }

      .tooltip:hover .tooltiptext {
        visibility: visible;
        opacity: 1;
      }

      .copy-btn {
        background-color: var(--dark-color);
        color: white;
        border: none;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8em;
        margin-top: 5px;
      }

      .copy-btn:hover {
        background-color: #2c3e50;
      }

      .output-container {
        position: relative;
      }

      .copy-result {
        position: absolute;
        top: 5px;
        right: 5px;
      }

      .json-key {
        color: #a6e22e;
      }
      .json-string {
        color: #f92672;
      }
      .json-number {
        color: #ae81ff;
      }
      .json-boolean {
        color: #fd971f;
      }
      .json-null {
        color: #999;
      }

      #loadSample {
        margin-top: 10px;
        background-color: #95a5a6;
      }

      #loadSample:hover {
        background-color: #7f8c8d;
      }

      .tips {
        font-size: 0.9em;
        color: #7f8c8d;
        margin-top: 5px;
      }
    </style>
  </head>
  <body>
    <header>
      <h1>77can游戏数据加密解密工具</h1>
      <p class="header-desc">
        本工具用于解密和加密77can游戏中的用户数据。使用deflate(raw)压缩和Base64编码进行数据处理。
      </p>
    </header>

    <div class="container">
      <div class="box">
        <h2>解密数据</h2>
        <div class="form-group">
          <label for="base64Input">
            输入Base64编码数据:
            <span class="tooltip"
              >ℹ️
              <span class="tooltiptext"
                >粘贴从game.js或network请求中获取的Base64编码字符串</span
              >
            </span>
          </label>
          <textarea
            id="base64Input"
            placeholder="在这里粘贴Base64编码的数据..."
          ></textarea>
          <p class="tips">提示：输入时会自动删除空格、换行等非Base64字符</p>
        </div>
        <div class="buttons">
          <button id="decryptBtn">解密数据</button>
          <button id="clearDecryptBtn" class="btn-danger">清空</button>
          <button id="loadSample">加载示例</button>
        </div>
        <div id="decryptAlert" class="alert hidden"></div>
        <div class="form-group">
          <label for="jsonOutput">解密结果 (JSON):</label>
          <div class="output-container">
            <div id="jsonOutput" class="output"></div>
            <button id="copyJsonBtn" class="copy-btn copy-result">复制</button>
          </div>
        </div>
      </div>

      <div class="box">
        <h2>加密数据</h2>
        <div class="form-group">
          <label for="jsonInput">
            输入JSON数据:
            <span class="tooltip"
              >ℹ️
              <span class="tooltiptext">粘贴或编辑要加密的JSON数据</span>
            </span>
          </label>
          <textarea
            id="jsonInput"
            placeholder="在这里粘贴或编辑JSON数据..."
          ></textarea>
        </div>
        <div class="buttons">
          <button id="encryptBtn" class="btn-success">加密数据</button>
          <button id="formatJsonBtn">格式化JSON</button>
          <button id="clearEncryptBtn" class="btn-danger">清空</button>
        </div>
        <div id="encryptAlert" class="alert hidden"></div>
        <div class="form-group">
          <label for="base64Output">加密结果 (Base64):</label>
          <div class="output-container">
            <div id="base64Output" class="output"></div>
            <button id="copyBase64Btn" class="copy-btn copy-result">
              复制
            </button>
          </div>
        </div>
      </div>
    </div>

    <footer>
      <p>© 2024 77can游戏数据工具 | 仅用于学习和研究目的</p>
      <p>加密方法: JSON → deflate(raw) → Base64</p>
      <p>解密方法: Base64 → inflate(raw) → JSON</p>
    </footer>

    <script>
      // Base64样例数据（可替换为实际的样例数据）
      const sampleBase64 =
        "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";

      /**
       * 清理和验证Base64字符串
       * @param {string} input - 输入的可能是Base64的字符串
       * @returns {object} - 包含清理后的字符串和验证结果
       */
      function cleanAndValidateBase64(input) {
        if (!input) {
          return {
            isValid: false,
            cleanedString: "",
            error: "输入为空",
          };
        }

        // 移除所有非Base64字符（包括空格、换行等）
        let cleaned = input.replace(/[^A-Za-z0-9+/=]/g, "");

        // 处理URL安全的Base64（替换 - 为 + 和 _ 为 /）
        cleaned = cleaned.replace(/-/g, "+").replace(/_/g, "/");

        // 确保字符串长度是4的倍数，必要时添加=号补充
        while (cleaned.length % 4 !== 0) {
          cleaned += "=";
        }

        // 检查是否是有效的Base64格式
        const validBase64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
        const isValid = validBase64Regex.test(cleaned);

        return {
          isValid,
          cleanedString: cleaned,
          error: isValid ? "" : "Base64格式无效",
        };
      }

      /**
       * 解密用户数据中的info字段
       * 解密方法: Base64解码 + deflate(raw inflate)解压缩
       */
      function decryptUserInfo(base64Data) {
        try {
          // 清理和验证Base64
          const cleanResult = cleanAndValidateBase64(base64Data);
          if (!cleanResult.isValid) {
            return {
              success: false,
              error: cleanResult.error,
            };
          }

          // 步骤1: Base64解码
          let binaryString;
          try {
            binaryString = atob(cleanResult.cleanedString);
          } catch (e) {
            return {
              success: false,
              error: "Base64解码失败: " + e.message,
            };
          }

          // 转换为Uint8Array供pako使用
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }

          // 步骤2: 使用deflate(raw inflate)解压缩
          let inflated;
          try {
            inflated = pako.inflate(bytes, { raw: true });
          } catch (e) {
            return {
              success: false,
              error: "解压缩失败: " + e.message,
            };
          }

          // 步骤3: 转换为JSON对象
          const textDecoder = new TextDecoder();
          const jsonString = textDecoder.decode(inflated);
          let jsonData;
          try {
            jsonData = JSON.parse(jsonString);
          } catch (e) {
            return {
              success: false,
              error: "JSON解析失败: " + e.message,
            };
          }

          return {
            success: true,
            data: jsonData,
          };
        } catch (error) {
          return {
            success: false,
            error: "未知错误: " + error.message,
          };
        }
      }

      /**
       * 加密用户数据为info字段
       * 加密方法: deflate(raw deflate)压缩 + Base64编码
       */
      function encryptUserInfo(jsonData) {
        try {
          // 步骤1: 转换为JSON字符串
          const jsonString = JSON.stringify(jsonData);

          // 步骤2: 使用deflate压缩
          const textEncoder = new TextEncoder();
          const bytes = textEncoder.encode(jsonString);
          const deflated = pako.deflate(bytes, { raw: true });

          // 步骤3: Base64编码
          let binaryString = "";
          deflated.forEach((byte) => {
            binaryString += String.fromCharCode(byte);
          });
          const base64Data = btoa(binaryString);

          return {
            success: true,
            data: base64Data,
          };
        } catch (error) {
          return {
            success: false,
            error: error.message,
          };
        }
      }

      /**
       * 格式化JSON字符串为带语法高亮的HTML
       */
      function formatJSON(json) {
        if (typeof json !== "string") {
          json = JSON.stringify(json, null, 2);
        }

        json = json
          .replace(/&/g, "&amp;")
          .replace(/</g, "&lt;")
          .replace(/>/g, "&gt;");

        return json.replace(
          /("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+\-]?\d+)?)/g,
          function (match) {
            let cls = "json-number";
            if (/^"/.test(match)) {
              if (/:$/.test(match)) {
                cls = "json-key";
              } else {
                cls = "json-string";
              }
            } else if (/true|false/.test(match)) {
              cls = "json-boolean";
            } else if (/null/.test(match)) {
              cls = "json-null";
            }
            return '<span class="' + cls + '">' + match + "</span>";
          }
        );
      }

      /**
       * 显示警告信息
       */
      function showAlert(elementId, message, isError = false) {
        const alertElement = document.getElementById(elementId);
        alertElement.textContent = message;
        alertElement.classList.remove("hidden", "alert-success", "alert-error");
        alertElement.classList.add(isError ? "alert-error" : "alert-success");

        // 3秒后自动隐藏
        setTimeout(() => {
          alertElement.classList.add("hidden");
        }, 3000);
      }

      // 解密按钮点击事件
      document
        .getElementById("decryptBtn")
        .addEventListener("click", function () {
          const base64Input = document
            .getElementById("base64Input")
            .value.trim();

          if (!base64Input) {
            showAlert("decryptAlert", "请输入Base64编码的数据", true);
            return;
          }

          // 先清理输入
          const cleanResult = cleanAndValidateBase64(base64Input);
          if (cleanResult.cleanedString !== base64Input) {
            document.getElementById("base64Input").value =
              cleanResult.cleanedString;
          }

          const result = decryptUserInfo(cleanResult.cleanedString);

          if (result.success) {
            const jsonOutput = document.getElementById("jsonOutput");
            jsonOutput.innerHTML = formatJSON(
              JSON.stringify(result.data, null, 2)
            );
            showAlert("decryptAlert", "解密成功！");
          } else {
            showAlert("decryptAlert", "解密失败: " + result.error, true);
          }
        });

      // 加密按钮点击事件
      document
        .getElementById("encryptBtn")
        .addEventListener("click", function () {
          const jsonInput = document.getElementById("jsonInput").value.trim();

          if (!jsonInput) {
            showAlert("encryptAlert", "请输入JSON数据", true);
            return;
          }

          try {
            const jsonData = JSON.parse(jsonInput);
            const result = encryptUserInfo(jsonData);

            if (result.success) {
              document.getElementById("base64Output").textContent = result.data;
              showAlert("encryptAlert", "加密成功！");
            } else {
              showAlert("encryptAlert", "加密失败: " + result.error, true);
            }
          } catch (error) {
            showAlert("encryptAlert", "JSON解析错误: " + error.message, true);
          }
        });

      // 格式化JSON按钮点击事件
      document
        .getElementById("formatJsonBtn")
        .addEventListener("click", function () {
          const jsonInput = document.getElementById("jsonInput").value.trim();

          if (!jsonInput) {
            showAlert("encryptAlert", "请输入JSON数据", true);
            return;
          }

          try {
            const formattedJson = JSON.stringify(
              JSON.parse(jsonInput),
              null,
              2
            );
            document.getElementById("jsonInput").value = formattedJson;
            showAlert("encryptAlert", "JSON格式化成功！");
          } catch (error) {
            showAlert("encryptAlert", "JSON解析错误: " + error.message, true);
          }
        });

      // 自动清理Base64输入
      document
        .getElementById("base64Input")
        .addEventListener("input", function () {
          const input = this.value;
          // 仅在输入有明显变化时才进行清理，以避免影响用户输入体验
          if (
            input.includes(" ") ||
            input.includes("\n") ||
            input.includes("\t")
          ) {
            const cleanResult = cleanAndValidateBase64(input);
            this.value = cleanResult.cleanedString;
          }
        });

      // 清空按钮点击事件
      document
        .getElementById("clearDecryptBtn")
        .addEventListener("click", function () {
          document.getElementById("base64Input").value = "";
          document.getElementById("jsonOutput").innerHTML = "";
        });

      document
        .getElementById("clearEncryptBtn")
        .addEventListener("click", function () {
          document.getElementById("jsonInput").value = "";
          document.getElementById("base64Output").textContent = "";
        });

      // 加载示例按钮点击事件
      document
        .getElementById("loadSample")
        .addEventListener("click", function () {
          document.getElementById("base64Input").value = sampleBase64;
          showAlert("decryptAlert", "已加载示例数据");
        });

      // 复制按钮点击事件
      document
        .getElementById("copyJsonBtn")
        .addEventListener("click", function () {
          const jsonOutput = document.getElementById("jsonOutput").innerText;
          navigator.clipboard
            .writeText(jsonOutput)
            .then(() => showAlert("decryptAlert", "已复制到剪贴板"))
            .catch(() =>
              showAlert("decryptAlert", "复制失败，请手动复制", true)
            );
        });

      document
        .getElementById("copyBase64Btn")
        .addEventListener("click", function () {
          const base64Output =
            document.getElementById("base64Output").textContent;
          navigator.clipboard
            .writeText(base64Output)
            .then(() => showAlert("encryptAlert", "已复制到剪贴板"))
            .catch(() =>
              showAlert("encryptAlert", "复制失败，请手动复制", true)
            );
        });

      // 自动解密-加密测试
      document.addEventListener("DOMContentLoaded", function () {
        // 解密示例
        const decryptResult = decryptUserInfo(sampleBase64);
        if (decryptResult.success) {
          // 再次加密
          const encryptResult = encryptUserInfo(decryptResult.data);
          if (encryptResult.success) {
            // 再次解密验证
            const reDecryptResult = decryptUserInfo(encryptResult.data);
            if (reDecryptResult.success) {
              console.log("加密-解密验证成功！");
            }
          }
        }
      });
    </script>
  </body>
</html>
