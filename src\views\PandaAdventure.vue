<template>
  <div class="panda-adventure">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <Platform />
              </el-icon>
              <span>熊猫冒险传奇</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 数据输入区域 -->
          <div class="data-input-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon><Document /></el-icon>
                <span>游戏数据</span>
              </div>
              <el-button
                type="danger"
                size="small"
                class="clear-button"
                @click="handleClearData"
                :icon="Delete"
              >
                清空数据
              </el-button>
            </div>

            <el-form :model="formData" label-position="top">
              <el-form-item label="OpenID">
                <el-input
                  v-model="formData.openId"
                  placeholder="请输入OpenID"
                  clearable
                  :disabled="loading.download || loading.upload"
                />
              </el-form-item>
              <div class="action-buttons">
                <el-button
                  type="primary"
                  :loading="loading.download"
                  @click="handleDownload"
                  :icon="Download"
                >
                  下载数据
                </el-button>
                <el-button
                  type="success"
                  :loading="loading.upload"
                  @click="handleUpload"
                  :icon="Upload"
                >
                  上传数据
                </el-button>
              </div>
            </el-form>
          </div>

          <!-- 游戏数据编辑区域 -->
          <div v-if="hasData" class="game-data-section">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              <span>游戏数据</span>
            </div>
            
            <el-tabs type="border-card">
              <!-- 基础属性 -->
              <el-tab-pane label="基础属性">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="金币">
                        <el-input v-model="gameData.coins" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="钻石">
                        <el-input v-model="gameData.diamonds" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="体力">
                        <el-input v-model="gameData.energy" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="等级">
                        <el-input v-model="gameData.level" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="经验">
                        <el-input v-model="gameData.exp" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="竹子">
                        <el-input v-model="gameData.bamboo" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 装备系统 -->
              <el-tab-pane label="装备系统">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="武器ID">
                        <el-input v-model="gameData.weaponId" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="护甲ID">
                        <el-input v-model="gameData.armorId" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="饰品ID">
                        <el-input v-model="gameData.accessoryId" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="已拥有装备">
                        <el-input v-model="gameData.ownedEquipments" type="textarea" :rows="2" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="装备强化等级">
                        <el-input v-model="gameData.equipmentLevels" type="textarea" :rows="2" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 技能系统 -->
              <el-tab-pane label="技能系统">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="已解锁技能">
                        <el-input v-model="gameData.unlockedSkills" type="textarea" :rows="2" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="技能等级">
                        <el-input v-model="gameData.skillLevels" type="textarea" :rows="2" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 关卡进度 -->
              <el-tab-pane label="关卡进度">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="当前章节">
                        <el-input v-model="gameData.currentChapter" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="当前关卡">
                        <el-input v-model="gameData.currentLevel" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="星级评价">
                        <el-input v-model="gameData.starRating" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="已通关关卡">
                        <el-input v-model="gameData.clearedLevels" type="textarea" :rows="2" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 操作历史记录 -->
          <div v-if="operationHistory.length > 0" class="history-section">
            <div class="section-title">
              <el-icon><Timer /></el-icon>
              <span>操作历史</span>
            </div>
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in operationHistory"
                :key="index"
                :type="history.success ? 'success' : 'danger'"
                :timestamp="history.time"
                size="large"
              >
                {{ history.operation }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Platform,
  Document,
  Download,
  Upload,
  Delete,
  Timer
} from '@element-plus/icons-vue'

// 处理 ResizeObserver 警告
const ignoreResizeObserverErrors = () => {
  window.addEventListener('error', (e) => {
    if (e.message === 'ResizeObserver loop completed with undelivered notifications.') {
      e.stopPropagation()
      e.preventDefault()
    }
  })
}

onMounted(() => {
  ignoreResizeObserverErrors()
})

// 表单数据
const formData = reactive({
  openId: 'otUcW7R20ozGuRyq6RSVW-QNyJag'
})

// 游戏数据
const gameData = reactive({
  // 基础属性
  coins: '0',           // 金币
  diamonds: '0',        // 钻石
  energy: '100',        // 体力
  level: '1',           // 等级
  exp: '0',             // 经验
  bamboo: '0',          // 竹子

  // 装备系统
  weaponId: '1',        // 武器ID
  armorId: '1',         // 护甲ID
  accessoryId: '1',     // 饰品ID
  ownedEquipments: '1,2,3',  // 已拥有装备
  equipmentLevels: '1,1,1',  // 装备强化等级

  // 技能系统
  unlockedSkills: '1,2',     // 已解锁技能
  skillLevels: '1,1',        // 技能等级

  // 关卡进度
  currentChapter: '1',       // 当前章节
  currentLevel: '1',         // 当前关卡
  starRating: '0',          // 星级评价
  clearedLevels: '1,2,3',   // 已通关关卡
})

// 数据状态
const hasData = ref(false)

// 加载状态
const loading = reactive({
  download: false,
  upload: false
})

// 操作历史
const operationHistory = ref([])

// 下载数据
const handleDownload = async () => {
  if (!formData.openId.trim()) {
    ElMessage.warning('请输入OpenID')
    return
  }

  loading.download = true
  try {
    // 第一步：登录获取token
    const loginResponse = await fetch('https://panda.smsh.fun/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
        'Accept': '*/*',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Referer': 'https://servicewechat.com/wx7a5723c42e8d926a/53/page-frame.html',
        'xweb_xhr': '1',
        'Authorization': 'Bearer'
      },
      body: JSON.stringify({
        openid: formData.openId,
        channel: 'wx'
      })
    })
    
    if (!loginResponse.ok) {
      throw new Error(`HTTP error! status: ${loginResponse.status}`)
    }
    
    const loginData = await loginResponse.text()
    try {
      // 清理响应数据中的无效字符
      const cleanedData = loginData.replace(/^\uFEFF/, '')  // 移除 BOM
        .replace(/^[^\{]*/, '')  // 移除开头的非JSON字符
        .replace(/[^\}]*$/, '')  // 移除结尾的非JSON字符
      
      console.log('清理后的数据:', cleanedData)
      const userData = JSON.parse(cleanedData).user
      
      // 更新游戏数据
      gameData.coins = userData.bagList.find(item => item.id === 3)?.count.toString() || '0'  // 金币
      gameData.diamonds = userData.bagList.find(item => item.id === 2)?.count.toString() || '0'  // 钻石
      gameData.energy = userData.bagList.find(item => item.id === 4)?.count.toString() || '0'  // 体力
      gameData.level = userData.stageLv.toString()  // 等级
      gameData.exp = '0'  // 经验值暂无
      gameData.bamboo = userData.bagList.find(item => item.id === 6)?.count.toString() || '0'  // 竹子

      // 装备相关
      gameData.weaponId = userData.weaponId.toString()
      gameData.weaponList = JSON.stringify(userData.weaponList)
      gameData.equipList = JSON.stringify(userData.equipList)
      
      // 已通关关卡
      gameData.currentChapter = '1'
      gameData.currentLevel = userData.stageLv.toString()
      gameData.starRating = userData.stageRewardList.flat().filter(x => x > 0).length.toString()
      gameData.clearedLevels = JSON.stringify(userData.stageRewardList)

      hasData.value = true
      
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: '数据下载成功',
        success: true
      })
      
      ElMessage.success('数据下载成功')
    } catch (parseError) {
      console.error('解析响应数据失败:', parseError)
      console.log('原始响应:', loginData)
      throw new Error('解析响应数据失败')
    }
  } catch (error) {
    console.error('下载数据失败:', error)
    ElMessage.error(error.message || '数据下载失败')
    
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '下载失败: ' + error.message,
      success: false
    })
  } finally {
    loading.download = false
  }
}

// 上传数据
const handleUpload = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载或输入数据')
    return
  }

  loading.upload = true
  try {
    // TODO: 实现数据上传逻辑
    const response = await fetch('/api/panda/saveData', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        openId: formData.openId,
        gameData: gameData
      })
    })
    
    const data = await response.json()
    if (data.code === 0) {
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: '数据上传成功',
        success: true
      })
      
      ElMessage.success('数据上传成功')
    } else {
      throw new Error(data.msg || '数据上传失败')
    }
  } catch (error) {
    console.error('上传数据失败:', error)
    ElMessage.error(error.message || '数据上传失败')
    
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '上传失败: ' + error.message,
      success: false
    })
  } finally {
    loading.upload = false
  }
}

// 清空数据
const handleClearData = () => {
  ElMessageBox.confirm(
    '确定要清空数据吗？此操作不可恢复',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      // 重置游戏数据
      Object.keys(gameData).forEach(key => {
        if (typeof gameData[key] === 'string') {
          gameData[key] = '0'
        }
      })
      hasData.value = false
      
      ElMessage({
        type: 'success',
        message: '数据已清空'
      })
      
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: '清空所有数据',
        success: true
      })
    })
    .catch(() => {
      // 取消清空操作
    })
}
</script>

<style scoped>
.panda-adventure {
  padding: 20px;
  height: 100vh;
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
  box-sizing: border-box;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Chrome, Safari and Opera */
.panda-adventure::-webkit-scrollbar {
  display: none;
}

.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  height: auto;
  min-height: calc(100vh - 40px);
}

/* 头部区域样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: -20px -20px 24px -20px;
  background: linear-gradient(135deg, #28a745 0%, #34d058 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.title .el-icon {
  font-size: 28px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

.status-tag {
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  font-weight: 500;
  font-size: 14px;
}

/* 数据输入区域样式 */
.data-input-section {
  margin: 16px 0;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.clear-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 操作按钮区域 */
.action-buttons {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.action-buttons .el-button {
  flex: 1;
}

/* 游戏数据编辑区域 */
.game-data-section {
  margin: 24px 0;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

/* 历史记录区域 */
.history-section {
  margin: 24px 20px;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .panda-adventure {
    padding: 12px;
  }

  .header-section {
    padding: 16px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    width: 100%;
  }

  .el-form-item {
    margin-bottom: 12px;
  }
}

/* 标签页样式优化 */
:deep(.el-tabs__item) {
  font-size: 14px;
  height: 40px;
  line-height: 40px;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__active-bar) {
  height: 2px;
}

/* 表单样式优化 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

:deep(.el-input__inner) {
  height: 36px;
  line-height: 36px;
}

:deep(.el-textarea__inner) {
  font-family: monospace;
}
</style> 