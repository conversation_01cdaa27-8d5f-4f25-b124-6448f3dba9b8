import axios from 'axios'
import ZipUtil from '@/utils/zipUtil'

const BASE_URL = 'https://gamedata.leishouwin.cc'

// 创建 axios 实例
const gameApi = axios.create({
  baseURL: BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 获取游戏数据
export const fetchGameData = async (userId) => {
  try {
    const response = await gameApi.get('/getuserdata', {
      params: {
        gameId: 368,
        channelId: 138,
        secret: 'Pos9sqd',
        version: 0,
        userId
      },
      responseType: 'arraybuffer'
    })

    if (!response.data) {
      throw new Error('获取数据失败')
    }

    return new Uint8Array(response.data)
  } catch (error) {
    throw new Error(error.response?.data?.message || '网络请求失败')
  }
}

// 获取原始数据
export const getOriginalData = async (userId) => {
  try {
    const response = await gameApi.get('/getuserdata', {
      params: {
        gameId: 368,
        channelId: 138,
        secret: 'Pos9sqd',
        version: 0,
        userId
      },
      responseType: 'arraybuffer'
    })

    if (!response.data) {
      throw new Error('获取原始数据失败')
    }

    const data = new Uint8Array(response.data).slice(5)
    const unzipdata = ZipUtil.unzip(data)
    return JSON.parse(unzipdata)

  } catch (error) {
    throw new Error(error.response?.data?.message || '获取原始数据失败')
  }
}

// 更新游戏数据
export const updateGameData = async (userId, version, data) => {
  try {
    // 压缩数据
    const zipData = ZipUtil.zipToUint8Array(JSON.stringify(data))
    
    const response = await gameApi.post('/saveuserdata', zipData, {
      params: {
        gameId: 368,
        channelId: 138,
        secret: 'Pos9sqd',
        version,
        userId
      }
    })

    return response.data
  } catch (error) {
    throw new Error(error.response?.data?.message || '保存数据失败')
  }
} 