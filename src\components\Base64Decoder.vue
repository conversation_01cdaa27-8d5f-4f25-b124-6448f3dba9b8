<template>
  <div class="decoder-container">
    <div class="header">
      <div class="header-content">
        <h2>解码工具</h2>
        <div class="header-right">
          <p class="subtitle">自动提取并解码 ey 开头的 Base64 字符串,支持AI分析</p>
          <el-button type="primary" @click="openSettings" class="settings-button">
            <el-icon>
              <Setting />
            </el-icon>
            大模型设置
          </el-button>
        </div>
      </div>
    </div>

    <div class="input-section">
      <el-input v-model="inputText" type="textarea" :rows="6" placeholder="请输入包含 Base64 的文本，将自动提取 ey 开头的字符串"
        @input="handleInput" />

      <div class="actions">
        <el-button type="primary" @click="handleDecode">解码</el-button>
        <el-button @click="clearAll">清空</el-button>
      </div>
    </div>

    <div class="results-section" v-if="results.length">
      <div class="results-header">
        <h3>解码结果</h3>
        <el-button link @click="copyAllResults">
          <el-icon>
            <Document />
          </el-icon> 复制全部
        </el-button>
      </div>

      <div class="result-cards">
        <el-card v-for="(result, index) in results" :key="index" class="result-card">
          <div class="result-header">
            <span class="index">#{{ 'result-' + (index + 1) }}</span>
            <el-button link @click="copyResult(result.decoded)">
              <el-icon>
                <Document />
              </el-icon> 复制
            </el-button>
          </div>

          <div class="base64-text">
            <div class="label">原始Base64:</div>
            <div class="content">{{ result.original }}</div>
          </div>

          <div class="decoded-text">
            <div class="label">解码结果:</div>
            <div class="content clickable" @click="copyResult(result.decoded)" title="点击复制">
              {{ result.decoded }}
            </div>
          </div>

          <!-- 修改AI分析结果部分 -->
          <div class="analysis-section">
            <div class="label">AI分析:</div>
            <div v-if="result.analyzing" class="analysis-loading">
              <div class="typing-indicator">
                <div class="dot"></div>
                <div class="dot"></div>
                <div class="dot"></div>
              </div>
              <span class="loading-text">AI正在分析中...</span>
            </div>
            <div v-else-if="analysisResults[index]" class="content analysis-content">
              {{ analysisResults[index].analysis }}
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加设置对话框 -->
    <el-dialog v-model="showSettingsDialog" title="OpenAI API设置" width="50%">
      <el-form :model="tempSettings" label-width="120px">
        <el-form-item label="API Key" required>
          <el-input v-model="tempSettings.apiKey" type="password" show-password placeholder="请输入OpenAI API Key" />
        </el-form-item>

        <el-form-item label="API端点">
          <el-input v-model="tempSettings.apiEndpoint" placeholder="API端点地址" />
          <div class="form-tip">默认使用: api.chatanywhere.tech</div>
        </el-form-item>

        <el-form-item label="模型">
          <el-select v-model="tempSettings.model" style="width: 100%" placeholder="请选择模型">
            <el-option-group v-for="group in MODEL_OPTIONS" :key="group.label" :label="group.label">
              <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value"
                @mouseenter="showTooltip($event, item.description)" @mouseleave="hideTooltip">
                <span style="float: left">{{ item.label }}</span>
              </el-option>
            </el-option-group>
          </el-select>
          <div class="form-tip">
            <template v-if="tempSettings.model.includes('gpt-4')">
              注意: GPT-4 模型的费用较高，请谨慎使用
            </template>
            <template v-else-if="tempSettings.model.includes('16k')">
              支持更长的上下文，最支持16k tokens
            </template>
            <template v-else>
              GPT-3.5-turbo 模型性价比最高，适合大多数场景
            </template>
          </div>
        </el-form-item>

        <el-form-item label="Temperature">
          <el-slider v-model="tempSettings.temperature" :min="0" :max="2" :step="0.1" show-input />
          <div class="form-tip">值越高，回答越随机创造性；值越低，回答越确定精确</div>
        </el-form-item>

        <el-form-item label="最Token">
          <el-input-number v-model="tempSettings.maxTokens" :min="100" :max="4000" :step="100" style="width: 100%" />
          <div class="form-tip">控制回答的最大长度，建议保持在2000左右</div>
        </el-form-item>

        <el-form-item label="系统提示词">
          <el-input v-model="tempSettings.systemPrompt" type="textarea" :rows="3" placeholder="设置AI的角色和任务描述" />
          <div class="form-tip">定义AI助手的角色和任务说明</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetSettings">重置默认值</el-button>
          <el-button @click="showSettingsDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 添加悬浮提示组件 -->
    <div v-show="tooltipVisible" class="model-tooltip" :style="tooltipStyle">
      {{ tooltipContent }}
    </div>

    <el-backtop :right="40" :bottom="40" target=".decoder-container">
      <div class="back-to-top">
        <el-icon><CaretTop /></el-icon>
      </div>
    </el-backtop>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onBeforeUnmount } from 'vue'
import { ElMessage, ElLoading, ElBacktop } from 'element-plus'
import { Document, Key, Setting, CaretTop } from '@element-plus/icons-vue'  // 移除 CaretTop
import AIAnalyzer from './AIAnalyzer.vue'
import { debounce } from 'lodash-es'  // 添加 debounce 导入

// 修改 MODEL_OPTIONS 常量
const MODEL_OPTIONS = [
  {
    label: 'Gemini',
    options: [
      {
        label: 'Gemma-7b-it',
        value: 'gemma-7b-it',
        description: 'Gemini 7B 指令微调版本'
      },
      {
        label: 'Gemma-2-9b-it',
        value: 'gemma2-9b-it',
        description: 'Gemini 9B 增强版本'
      }
    ]
  },
  {
    label: 'LLaMA3',
    options: [
      {
        label: 'LLaMA-3.1-70B',
        value: 'llama-3.1-70b-versatile',
        description: '通用型大模型'
      },
      {
        label: 'LLaMA-3.1-8B',
        value: 'llama-3.1-8b-instant',
        description: '快速响应版本'
      }
    ]
  },
  {
    label: 'LLaMA3.2',
    options: [
      {
        label: 'LLaMA-3.2-11B Text',
        value: 'llama-3.2-11b-text-preview',
        description: '文本处理预览版'
      },
      {
        label: 'LLaMA-3.2-11B Vision',
        value: 'llama-3.2-11b-vision-preview',
        description: '视觉分析预览版'
      },
      {
        label: 'LLaMA-3.2-1B',
        value: 'llama-3.2-1b-preview',
        description: '轻量级预览版'
      },
      {
        label: 'LLaMA-3.2-3B',
        value: 'llama-3.2-3b-preview',
        description: '规预览版'
      },
      {
        label: 'LLaMA-3.2-90B Text',
        value: 'llama-3.2-90b-text-preview',
        description: '规模文本处理预览版（推荐用于代码分析）'  // 添加推说明
      },
      {
        label: 'LLaMA-3.2-90B Vision',
        value: 'llama-3.2-90b-vision-preview',
        description: '大规模视觉分析览版'
      }
    ]
  }
]

const inputText = ref('')
const results = ref([])
const analysisResults = ref([])
const isAnalyzing = ref(false)

// 修改默认配置
const openAIConfig = reactive({
  apiKey: localStorage.getItem('groq_api_key') || '',
  model: localStorage.getItem('groq_model') || 'llama-3.2-90b-text-preview', // 更新为90B模型
  temperature: Number(localStorage.getItem('groq_temperature')) || 0.3, // 降低temperature以获得更精确的回答
  maxTokens: Number(localStorage.getItem('groq_max_tokens')) || 4096,
  systemPrompt: localStorage.getItem('groq_system_prompt') || '你是一个专业的程序员和代码分析专家。请使用中文回复分析代码时，请详细说明代码的结构、功能、潜在问题和优化建议。如果是JSON数据，请解释其格式和字段含义。',
  apiEndpoint: 'https://api.groq.com/openai/v1/chat/completions'
})

const showSettingsDialog = ref(false)
const tempSettings = reactive({
  apiKey: '',
  model: 'llama-3.2-90b-text-preview',
  temperature: 0.3,
  maxTokens: 4096,
  systemPrompt: '你是一个专业的程序员和代码分析专家。请使用中文回复。分析代码时，详细说明代码的结构、功能、潜在问题和优化建议。如果是JSON数据，请解释其格式和字段含义。',
  apiEndpoint: 'https://api.groq.com/openai/v1/chat/completions'
})

const extractBase64 = (text) => {
  const regex = /ey[A-Za-z0-9_-]+/g
  return text.match(regex) || []
}

const decodeBase64 = (base64String) => {
  try {
    const cleanedString = base64String.replace(/-/g, '+').replace(/_/g, '/')
    const decoded = atob(cleanedString)
    return decoded
  } catch (e) {
    return '解码失败: ' + e.message
  }
}

// 防抖处理的输入验证
const validateInput = debounce((value) => {
  if (!value) return
  
  // 检查是否包含 ey 开头的字符串
  const hasBase64 = /ey[A-Za-z0-9_-]+/.test(value)
  if (!hasBase64) {
    ElMessage.warning('未检测到有效的Base64字符串(ey开头)')
  }
}, 500)

// 监听输入变化
watch(inputText, (newValue) => {
  validateInput(newValue)
})

// 防抖处理的解码
const debouncedDecode = debounce(() => {
  handleDecode()
}, 500)

// 防抖处理的AI分析
const debouncedAnalysis = debounce(() => {
  if (!results.value.length) {
    ElMessage.warning('请先解码数据')
    return
  }
  startAnalysis()
}, 800)

// 修改 handleInput 方法
const handleInput = () => {
  if (!inputText.value) {
    results.value = []
    return
  }
  debouncedDecode()
}

// 修改 startAnalysis 方法
const startAnalysis = () => {
  debouncedAnalysis()
}

// 在组件卸载时取消防抖
onBeforeUnmount(() => {
  validateInput.cancel()
  debouncedDecode.cancel()
  debouncedAnalysis.cancel()
})

const handleDecode = async () => {
  if (!inputText.value) {
    ElMessage.warning('请输入需要解码的文本')
    return
  }

  const base64Strings = extractBase64(inputText.value)
  if (!base64Strings.length) {
    ElMessage.warning('未找到有效的Base64字符串')
    return
  }

  results.value = base64Strings.map(str => ({
    original: str,
    decoded: decodeBase64(str)
  }))

  // 移除这段代码
  // setTimeout(() => {
  //   if (handleScroll.value) {
  //     handleScroll.value()
  //   }
  // }, 100)

  // 修AI分析的加动画
  if (openAIConfig.apiKey) {
    isAnalyzing.value = true

    try {
      const analysisPromises = results.value.map(async (result, index) => {
        try {
          results.value[index].analyzing = true
          const analysis = await analyzeWithGPT(result.decoded)
          results.value[index].analyzing = false
          return {
            index,
            analysis
          }
        } catch (error) {
          results.value[index].analyzing = false
          return {
            index,
            analysis: '分析失败: ' + error.message
          }
        }
      })

      analysisResults.value = await Promise.all(analysisPromises)
      ElMessage.success(`成功解码并分析 ${results.value.length} 条数据`)
    } catch (error) {
      ElMessage.error('AI分析过程中出现错误')
    } finally {
      isAnalyzing.value = false
    }
  } else {
    ElMessage.success(`成功解码 ${results.value.length} 条数据`)
  }
}

// 修改分析请求中的提示语
const analyzeWithGPT = async (text, retries = 3) => {
  if (!openAIConfig.apiKey) {
    ElMessage.warning('请先设置Groq API Key')
    return null
  }

  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(openAIConfig.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${openAIConfig.apiKey}`
        },
        body: JSON.stringify({
          model: openAIConfig.model,
          messages: [{
            role: 'system',
            content: openAIConfig.systemPrompt
          }, {
            role: 'user',
            content: `请用文分析这段数据:\n${text}`  // 修改这里确保用中文请求
          }],
          temperature: openAIConfig.temperature,
          max_tokens: openAIConfig.maxTokens,
          n: 1,
          stream: false
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        console.error('API响应错误:', errorData)
        throw new Error(errorData.error?.message || `请求失败: ${response.status}`)
      }

      const data = await response.json()
      return data.choices[0].message.content
    } catch (error) {
      console.error(`第${i + 1}次调用失败:`, error)
      if (i === retries - 1) {
        ElMessage.error(`AI分析失败: ${error.message}`)
        return null
      }
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}

const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('复制成功')
  } catch (err) {
    ElMessage.error('复制失败')
  }
}

const copyResult = (text) => {
  copyToClipboard(text)
}

const copyAllResults = () => {
  const allText = results.value
    .map((r, i) => `#result-${i + 1}\n原文：${r.original}\n结果：${r.decoded}`)
    .join('\n\n')
  copyToClipboard(allText)
}

const clearAll = () => {
  inputText.value = ''
  results.value = []
}

const openSettings = () => {
  // 深拷贝配置到临时设置
  Object.assign(tempSettings, {
    apiKey: openAIConfig.apiKey,
    model: openAIConfig.model,
    temperature: openAIConfig.temperature,
    maxTokens: openAIConfig.maxTokens,
    systemPrompt: openAIConfig.systemPrompt,
    apiEndpoint: openAIConfig.apiEndpoint
  })
  showSettingsDialog.value = true
}

const saveSettings = () => {
  if (!tempSettings.apiKey) {
    ElMessage.warning('请输入Groq API Key')
    return
  }

  // 更新配置
  Object.assign(openAIConfig, {
    apiKey: tempSettings.apiKey,
    model: tempSettings.model,
    temperature: tempSettings.temperature,
    maxTokens: tempSettings.maxTokens,
    systemPrompt: tempSettings.systemPrompt,
    apiEndpoint: tempSettings.apiEndpoint
  })

  // 保存到localStorage，使用groq前缀
  localStorage.setItem('groq_api_key', tempSettings.apiKey)
  localStorage.setItem('groq_model', tempSettings.model)
  localStorage.setItem('groq_temperature', tempSettings.temperature.toString())
  localStorage.setItem('groq_max_tokens', tempSettings.maxTokens.toString())
  localStorage.setItem('groq_system_prompt', tempSettings.systemPrompt)

  showSettingsDialog.value = false
  ElMessage.success('设置已保存')
}

// 修改重置设置方法中的默认模型
const resetSettings = () => {
  tempSettings.model = 'llama-3.2-90b-text-preview'
  tempSettings.temperature = 0.3
  tempSettings.maxTokens = 4096
  tempSettings.systemPrompt = '你是一个专业的程序员和代码分析专家。请使用中文回复。分析代码时，请详细说明代码的结构、功能、潜在问题和优化建议。如果是JSON数据，请解释其式和字段含义。'
  tempSettings.apiEndpoint = 'https://api.groq.com/openai/v1/chat/completions'
}

const tooltipVisible = ref(false)
const tooltipContent = ref('')
const tooltipStyle = ref({
  top: '0px',
  left: '0px'
})

const showTooltip = (event, description) => {
  tooltipContent.value = description
  tooltipVisible.value = true

  // 计算提示框位置，使其显示在鼠标上方
  const offset = 10 // 与鼠标的垂直距离
  tooltipStyle.value = {
    top: `${event.clientY - offset}px`,
    left: `${event.clientX}px`,
    transform: 'translate(-50%, -100%)' // 使提示框水平居中并位于鼠标上方
  }
}

const hideTooltip = () => {
  tooltipVisible.value = false
}
</script>

<style scoped>
.decoder-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
  height: 100vh; /* 修改为视窗高度 */
  overflow-y: auto;
  position: relative;
  
  /* 添加以下样式来隐藏滚动条但保持滚动功能 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  
  /* 设置滚动行为 */
  scroll-behavior: smooth;
}

/* 隐藏 Webkit 浏览器的滚动条 */
.decoder-container::-webkit-scrollbar {
  display: none;
}

/* 确保内容区域有足够的底部间距 */
.results-section {
  margin-bottom: 60px; /* 为回到顶部按钮留出空间 */
}

/* 优化回到顶部按钮的样式 */
:deep(.el-backtop) {
  right: 40px !important;
  bottom: 40px !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.back-to-top {
  height: 40px;
  width: 40px;
  border-radius: 4px;
  background-color: var(--el-bg-color);
  box-shadow: var(--el-box-shadow-lighter);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.back-to-top:hover {
  background-color: var(--el-color-primary-light-9);
  transform: translateY(-2px);
}

:deep(.el-icon) {
  font-size: 18px;
  color: var(--el-color-primary);
}

.header {
  margin-bottom: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.header h2 {
  margin: 0;
  color: #1d1d1f;
  font-size: 24px;
  white-space: nowrap;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
  flex: 1;
}

.subtitle {
  margin: 0;
  color: #86868b;
  font-size: 0.9em;
  flex: 1;
  text-align: right;
  padding-right: 20px;
}

.settings-button {
  white-space: nowrap;
}

/* 移动端��配 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .subtitle {
    text-align: left;
    padding-right: 0;
  }
}

.input-section {
  margin-bottom: 30px;
}

/* 修改 actions 相关样式 */
.actions {
  margin-top: 20px;  /* 增加上边距 */
  display: flex;
  gap: 16px;  /* 增加按钮间距 */
  justify-content: center;
}

/* 添加按钮样式 */
.actions :deep(.el-button) {
  padding: 12px 24px;  /* 增加内边距使按钮变大 */
  font-size: 16px;  /* 增加字体大小 */
  height: 48px;  /* 设置固定高度 */
  min-width: 120px;  /* 设置最小宽度 */
}

/* 添加悬浮效果 */
.actions :deep(.el-button:hover) {
  transform: translateY(-2px);
  transition: all 0.3s ease;
}

/* 确保在移动设备上也能保持合适的大小 */
@media (max-width: 768px) {
  .actions {
    flex-direction: column;  /* 在小屏幕上垂直排列 */
    padding: 0 20px;  /* 添加两侧内边距 */
  }

  .actions :deep(.el-button) {
    width: 100%;  /* 在小屏幕上占满宽度 */
    margin: 0;  /* 移除默认外边距 */
  }
}

.results-section {
  background: #f5f5f7;
  border-radius: 12px;
  padding: 20px;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.results-header h3 {
  margin: 0;
  color: #1d1d1f;
}

.result-cards {
  display: grid;
  gap: 16px;
}

.result-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.index {
  color: #86868b;
  font-size: 0.9em;
}

.base64-text,
.decoded-text {
  margin-top: 12px;
}

.label {
  color: #86868b;
  font-size: 0.9em;
  margin-bottom: 4px;
}

.content {
  word-break: break-all;
  font-family: monospace;
  background: #f5f5f7;
  padding: 8px;
  border-radius: 6px;
  font-size: 0.9em;
}

:deep(.el-textarea__inner) {
  border-radius: 12px;
  font-family: monospace;
}

:deep(.el-button) {
  border-radius: 8px;
}

.settings-button {
  position: absolute;
  right: 0;
  /* 改为0 */
  top: 50%;
  /* 垂直居中 */
  transform: translateY(-50%);
  /* 精确垂直居中 */
  z-index: 2;
  /* 确保按钮在最上层 */
}

/* 在小屏幕上调整布局 */
@media (max-width: 768px) {
  .header {
    padding-right: 100px;
    /* 为按钮预留空间 */
  }

  .subtitle {
    max-width: 100%;
    padding-right: 20px;
  }

  .settings-button {
    top: 0;
    transform: none;
  }
}

.analysis-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.analysis-content {
  white-space: pre-wrap;
  line-height: 1.5;
  color: #444;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.analysis-loading {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.loading-text {
  color: #606266;
  font-size: 14px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #409eff;
  animation: bounce 1.4s infinite ease-in-out;
}

.dot:nth-child(1) {
  animation-delay: -0.32s;
}

.dot:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes bounce {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

/* 添加新样式 */
.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

:deep(.el-slider) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 24px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep(.el-dialog__body) {
  padding: 20px 30px;
}

.content.clickable {
  cursor: pointer;
  transition: background-color 0.2s;
}

.content.clickable:hover {
  background-color: #e6f1fc;
}

.content.clickable:active {
  background-color: #d9ecff;
}

/* 添加提示样 */
.content.clickable::after {
  content: '点击复制';
  position: absolute;
  right: 8px;
  font-size: 12px;
  color: #909399;
  opacity: 0;
  transition: opacity 0.2s;
}

.content.clickable:hover::after {
  opacity: 1;
}

.model-tooltip {
  position: fixed;
  z-index: 9999;
  /* 更新背景色为渐变色 */
  background: linear-gradient(145deg, #2c3e50, #3498db);
  color: #ffffff;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 14px;
  pointer-events: none;
  max-width: 300px;
  word-wrap: break-word;
  /* 更新阴影效果 */
  box-shadow: 0 4px 20px rgba(52, 152, 219, 0.2);
  /* 添加边框 */
  border: 1px solid rgba(255, 255, 255, 0.1);
  /* 添加���玻璃效果 */
  backdrop-filter: blur(8px);
  /* 添加文字阴影 */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 更新小三角形的颜色 */
.model-tooltip::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px 5px 0;
  border-style: solid;
  /* 使用渐变色中较深的颜色 */
  border-color: #2c3e50 transparent transparent;
}

.back-to-top {
  height: 100%;
  width: 100%;
  background-color: var(--el-bg-color);
  box-shadow: var(--el-box-shadow-lighter);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--el-transition-all);
}

.back-to-top:hover {
  background-color: var(--el-color-primary-light-9);
}

:deep(.el-backtop) {
  background-color: transparent;
  border: none;
  box-shadow: none;
}

:deep(.el-icon) {
  color: var(--el-color-primary);
}
</style>









