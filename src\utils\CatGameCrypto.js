/**
 * 喵桑活下去游戏数据加解密工具类
 */
export class CatGameCrypto {
    static DELTA = 2654435769;
    static KEY = 'xYzfa@!& X';

    /**
     * 解密游戏数据
     * @param {string} encryptedData Base64编码的加密数据
     * @returns {Object} 解密后的游戏数据对象
     */
    static decryptGameData(encryptedData) {
        try {
            const decrypted = this.s(atob(encryptedData), this.KEY);
            
            // 尝试直接解析为JSON
            try {
                return {
                    prefix: '',
                    data: JSON.parse(decrypted)
                };
            } catch (e) {
                // 如果直接解析失败，查找最后一个有效的JSON开始位置
                let lastValidJson = null;
                let lastValidPrefix = '';
                let currentIndex = 0;
                
                while (true) {
                    const jsonStart = decrypted.indexOf('{', currentIndex);
                    if (jsonStart === -1) break;
                    
                    try {
                        const possibleJson = decrypted.substring(jsonStart);
                        const parsed = JSON.parse(possibleJson);
                        lastValidJson = parsed;
                        lastValidPrefix = this.convertToHex(decrypted.substring(0, jsonStart));
                    } catch (err) {
                        // 如果解析失败，继续查找下一个 '{'
                    }
                    currentIndex = jsonStart + 1;
                }
                
                if (lastValidJson) {
                    return {
                        prefix: lastValidPrefix,
                        data: lastValidJson
                    };
                }
                
                // 如果找不到有效的JSON，返回原始格式
                return {
                    prefix: this.convertToHex(decrypted),
                    data: null
                };
            }
        } catch (error) {
            console.error('Game data decryption failed:', error);
            throw new Error(`游戏数据解密失败: ${error.message}`);
        }
    }

    /**
     * 加密游戏数据
     * @param {Object} gameData 游戏数据对象
     * @param {string} prefix 加密前缀
     * @returns {string} Base64编码的加密数据
     */
    static encryptGameData(gameData, prefix) {
        try {
            // 将十六进制格式的前缀转换回原始字符串
            const originalPrefix = prefix.replace(/\\x([0-9a-fA-F]{2})/g, 
                (_, hex) => String.fromCharCode(parseInt(hex, 16)));
                
            const jsonStr = JSON.stringify(gameData);
            const dataToEncrypt = originalPrefix + jsonStr;
            
            // 使用完全匹配原始代码的加密方式
            return btoa(this.u(dataToEncrypt, this.KEY));
        } catch (error) {
            console.error('Game data encryption failed:', error);
            throw new Error('游戏数据加密失败');
        }
    }

    /**
     * 批量解密游戏数据
     * @param {Object} archiveData 游戏存档数据对象
     * @returns {Object} 解密后的游戏数据对象
     */
    static decryptArchiveData(archiveData) {
        const result = {};
        try {
            // 遍所有 archive 属性
            for (const [key, value] of Object.entries(archiveData)) {
                if (!value) {
                    result[key] = {
                        prefix: '',
                        data: null,
                        error: '空数据'
                    };
                    continue;
                }

                try {
                    const decrypted = this.decryptGameData(value);
                    result[key] = {
                        ...decrypted,
                        originalValue: value
                    };
                } catch (error) {
                    console.warn(`Failed to decrypt ${key}:`, error);
                    result[key] = {
                        prefix: '',
                        data: value,
                        error: error.message || '解密失败',
                        originalValue: value
                    };
                }
            }
            return result;
        } catch (error) {
            console.error('Archive data decryption failed:', error);
            throw new Error(`游戏存档解密失败: ${error.message}`);
        }
    }

    /**
     * 批量加密游戏数据
     * @param {Object} archiveData 游戏存档数据对象
     * @returns {Object} 加密后的游戏数据对象
     */
    static encryptArchiveData(archiveData) {
        const result = {};
        try {
            for (const [key, value] of Object.entries(archiveData)) {
                try {
                    result[key] = this.encryptGameData(value.data, value.prefix);
                } catch (error) {
                    console.warn(`Failed to encrypt ${key}:`, error);
                    result[key] = value.originalValue || '';
                }
            }
        } catch (error) {
            console.error('Archive data encryption failed:', error);
            throw new Error('游戏存档加密失败');
        }
        return result;
    }

    // 内部方法，完全匹配原始实现
    static s(r, e) {
        return null == r || 0 === r.length ? r : (
            e = this.f(e),
            this.d(this.jm_o(this.decryptCore(this.a(r, !1), this.h(this.a(e, !1))), !0))
        );
    }

    static u(r, e) {
        return null == r || 0 === r.length ? r : (
            r = this.f(r),
            e = this.f(e),
            this.jm_o(((r, e) => {
                var t, o, a, h, f, d, u = r.length, s = u - 1;
                for (o = r[s],
                a = 0,
                d = 0 | Math.floor(6 + 52 / u); d > 0; --d) {
                    for (h = (a = this.c(a + this.DELTA)) >>> 2 & 3,
                    f = 0; f < s; ++f)
                        t = r[f + 1],
                        o = r[f] = this.c(r[f] + this.i(a, t, o, f, h, e));
                    t = r[0],
                    o = r[s] = this.c(r[s] + this.i(a, t, o, s, h, e));
                }
                return r;
            })(this.a(r, !0), this.h(this.a(e, !1))), !1)
        );
    }

    static a(r, e) {
        var t, n = r.length, o = n >> 2;
        0 != (3 & n) && ++o,
        e ? (t = new Array(o + 1))[o] = n : t = new Array(o);
        for (var a = 0; a < n; ++a)
            t[a >> 2] |= r.charCodeAt(a) << ((3 & a) << 3);
        return t;
    }

    static c(r) {
        return 4294967295 & r;
    }

    static h(r) {
        return r.length < 4 && (r.length = 4), r;
    }

    static f(r) {
        if (/^[\x00-\x7f]*$/.test(r)) return r;
        for (var e = [], t = r.length, n = 0, o = 0; n < t; ++n, ++o) {
            var a = r.charCodeAt(n);
            if (a < 128)
                e[o] = r.charAt(n);
            else if (a < 2048)
                e[o] = String.fromCharCode(192 | a >> 6, 128 | 63 & a);
            else {
                if (!(a < 55296 || a > 57343)) {
                    if (n + 1 < t) {
                        var c = r.charCodeAt(n + 1);
                        if (a < 56320 && 56320 <= c && c <= 57343) {
                            var i = 65536 + ((1023 & a) << 10 | 1023 & c);
                            e[o] = String.fromCharCode(240 | i >> 18 & 63, 128 | i >> 12 & 63, 128 | i >> 6 & 63, 128 | 63 & i),
                            ++n;
                            continue;
                        }
                    }
                    throw new Error("Malformed string");
                }
                e[o] = String.fromCharCode(224 | a >> 12, 128 | a >> 6 & 63, 128 | 63 & a);
            }
        }
        return e.join("");
    }

    static d(r, e) {
        return (null == e || e < 0) && (e = r.length),
        0 === e ? "" : /^[\x00-\x7f]*$/.test(r) || !/^[\x00-\xff]*$/.test(r) ? e === r.length ? r : r.substr(0, e) : e < 32767 ? this.d1(r, e) : this.d2(r, e);
    }

    static d1(r, e) {
        for (var t = new Array(e), n = 0, o = 0, a = r.length; n < e && o < a; n++) {
            var c = r.charCodeAt(o++);
            switch (c >> 4) {
                case 0: case 1: case 2: case 3: case 4: case 5: case 6: case 7:
                    t[n] = c;
                    break;
                case 12: case 13:
                    if (!(o < a)) throw new Error("Unfinished UTF-8 octet sequence");
                    t[n] = (31 & c) << 6 | 63 & r.charCodeAt(o++);
                    break;
                case 14:
                    if (!(o + 1 < a)) throw new Error("Unfinished UTF-8 octet sequence");
                    t[n] = (15 & c) << 12 | (63 & r.charCodeAt(o++)) << 6 | 63 & r.charCodeAt(o++);
                    break;
                case 15:
                    if (!(o + 2 < a)) throw new Error("Unfinished UTF-8 octet sequence");
                    var i = ((7 & c) << 18 | (63 & r.charCodeAt(o++)) << 12 | (63 & r.charCodeAt(o++)) << 6 | 63 & r.charCodeAt(o++)) - 65536;
                    if (!(0 <= i && i <= 1048575)) throw new Error("Character outside valid Unicode range: 0x" + i.toString(16));
                    t[n++] = i >> 10 & 1023 | 55296,
                    t[n] = 1023 & i | 56320;
                    break;
                default:
                    throw new Error("Bad UTF-8 encoding 0x" + c.toString(16));
            }
        }
        return n < e && (t.length = n),
        String.fromCharCode.apply(String, t);
    }

    static d2(r, e) {
        return this.d1(r, e);
    }

    static jm_o(r, e) {
        var t = r.length, n = t << 2;
        if (e) {
            var o = r[t - 1];
            if (o < (n -= 4) - 3 || o > n) return null;
            n = o;
        }
        for (var a = 0; a < t; a++)
            r[a] = String.fromCharCode(255 & r[a], r[a] >>> 8 & 255, r[a] >>> 16 & 255, r[a] >>> 24 & 255);
        var c = r.join("");
        return e ? c.substring(0, n) : c;
    }

    static i(r, e, t, n, o, a) {
        return (t >>> 5 ^ e << 2) + (e >>> 3 ^ t << 4) ^ (r ^ e) + (a[3 & n ^ o] ^ t);
    }

    static encryptCore(r, e) {
        var t, o, a, h, f, d, u = r.length, s = u - 1;
        for (o = r[s],
        a = 0,
        d = 0 | Math.floor(6 + 52 / u); d > 0; --d) {
            for (h = (a = this.c(a + this.DELTA)) >>> 2 & 3,
            f = 0; f < s; ++f)
                t = r[f + 1],
                o = r[f] = this.c(r[f] + this.i(a, t, o, f, h, e));
            t = r[0],
            o = r[s] = this.c(r[s] + this.i(a, t, o, s, h, e));
        }
        return r;
    }

    static decryptCore(r, e) {
        var t, o, a, h, f, d = r.length, u = d - 1;
        for (t = r[0], a = this.c(Math.floor(6 + 52 / d) * this.DELTA); 0 !== a; a = this.c(a - this.DELTA)) {
            for (h = a >>> 2 & 3, f = u; f > 0; --f)
                o = r[f - 1],
                t = r[f] = this.c(r[f] - this.i(a, t, o, f, h, e));
            o = r[u],
            t = r[0] = this.c(r[0] - this.i(a, t, o, 0, h, e));
        }
        return r;
    }

    /**
     * 将字符串中的不可打印字符转换为十六进制表示
     */
    static convertToHex(input) {
        let output = "";
        for (let i = 0; i < input.length; i++) {
            let charCode = input.charCodeAt(i);
            if (charCode < 32 || charCode > 126) {
                output += "\\x" + charCode.toString(16).padStart(2, "0");
            } else {
                output += input.charAt(i);
            }
        }
        return output;
    }
} 