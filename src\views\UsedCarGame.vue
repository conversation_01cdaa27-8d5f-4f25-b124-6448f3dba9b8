<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="used-car-game">
      <el-card class="game-card" :body-style="{ padding: '0' }">
        <!-- 顶部标题区域 -->
        <div class="card-header-wrapper">
          <div class="card-header">
            <el-icon class="header-icon">
              <Van />
            </el-icon>
            <span>二手车模拟器数据修改</span>
          </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="card-content">
          <el-form :model="form" label-width="100px" class="game-form">
            <!-- 基础信息部分 - 始终显示 -->
            <div class="form-section">
              <div class="section-title">
                <el-icon>
                  <Key />
                </el-icon>
                <span>基础信息</span>
              </div>
              <div class="basic-info-container">
                <el-form-item label="OpenID">
                  <el-input v-model="form.openId" placeholder="请输入OpenID" size="default">
                    <template #prefix>
                      <el-icon>
                        <User />
                      </el-icon>
                    </template>
                  </el-input>
                </el-form-item>
              </div>

              <!-- 修改按钮布局 -->
              <el-form-item>
                <div class="button-group">
                  <el-button type="primary" @click="handleDownload" :loading="loading.download" :icon="Download">
                    下载数据
                  </el-button>
                  <el-button type="success" @click="handleUpload" :loading="loading.upload" :icon="Upload"
                    :disabled="!hasData">
                    上传数据
                  </el-button>
                </div>
              </el-form-item>
            </div>

            <!-- 数据已下载后才显示的内容 -->
            <transition name="expand">
              <div v-if="hasData" class="data-content">
                <!-- 游戏数据部分 -->
                <div class="form-section animate-section">
                  <div class="section-title">
                    <el-icon>
                      <DataLine />
                    </el-icon>
                    <span>游戏数据</span>
                    <div class="tip-text" @mouseenter="showTipImage = true" @mouseleave="showTipImage = false">
                      设置--保存进度--上传存档/下载同步
                      <!-- 悬浮提示图片 -->
                      <div class="tip-image" v-show="showTipImage">
                        <img src="@/assets/save-tip.png" alt="保存提示">
                      </div>
                    </div>
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="绿钞">
                        <el-input-number v-model="form.gems" :min="0" :max="999999999999999" controls-position="right">
                          <template #prefix>💵</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="钻石">
                        <el-input-number v-model="form.diamond" :min="0" :max="999999999" controls-position="right">
                          <template #prefix>💎</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="声望">
                        <el-input-number v-model="form.star" :min="0" :max="999999" controls-position="right">
                          <template #prefix>👑</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="好感度">
                        <el-input-number v-model="form.feel" :min="0" :max="999999" controls-position="right">
                          <template #prefix>❤️</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="喜爱度">
                        <el-input-number v-model="form.like" :min="0" :max="999999" controls-position="right">
                          <template #prefix>👍</template>
                        </el-input-number>
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="VIP">
                        <div class="vip-wrapper" @mouseenter="showVipImage = true" @mouseleave="showVipImage = false">
                          <el-switch v-model="form.vip" active-text="已开通" inactive-text="未开通" :active-value="true"
                            :inactive-value="false" />
                          <!-- VIP 悬浮提示图片 -->
                          <div class="tip-image vip-tip" v-show="showVipImage">
                            <img src="@/assets/vip.png" alt="VIP提示">
                          </div>
                        </div>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 高级设置部分 -->
                <div class="form-section animate-section">
                  <div class="section-title">
                    <el-icon>
                      <Setting />
                    </el-icon>
                    <span>高级设置</span>
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="音乐">
                        <el-switch v-model="form.musicEnabled" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="音效">
                        <el-switch v-model="form.soundEnabled" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="性别">
                        <el-select v-model="form.playerSex">
                          <el-option label="男" :value="1" />
                          <el-option label="女" :value="2" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </div>

                <!-- 添加游戏状态部分 -->
                <div class="form-section animate-section">
                  <div class="section-title">
                    <el-icon>
                      <InfoFilled />
                    </el-icon>
                    <span>游戏状态</span>
                  </div>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="在线时长">
                      {{ formatOnlineTime(form.onlineTime) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="启动次数">
                      {{ form.launchTimes }} 次
                    </el-descriptions-item>
                    <el-descriptions-item label="最后更新">
                      {{ formatDate(form.lastUpdate) }}
                    </el-descriptions-item>
                    <el-descriptions-item label="数据版本">
                      {{ form.bookId }}
                    </el-descriptions-item>
                  </el-descriptions>
                </div>
              </div>
            </transition>

            <!-- 未下载数据时显示的提示 -->
            <transition name="fade">
              <template v-if="!hasData">
                <el-empty description="请先下载数据" :image-size="200">
                  <template #description>
                    <p class="empty-text">请输入OpenID并下载数据</p>
                  </template>
                </el-empty>
              </template>
            </transition>
          </el-form>
        </div>
      </el-card>

      <!-- 消息提示 -->
      <transition name="slide-down">
        <div v-if="showResult" class="alert-message" :class="resultConfig.icon">
          <el-icon class="alert-icon">
            <CircleCheckFilled v-if="resultConfig.icon === 'success'" />
            <CircleCloseFilled v-else />
          </el-icon>
          <span class="alert-content">{{ resultConfig.subTitle }}</span>
          <el-icon class="close-icon" @click="showResult = false">
            <Close />
          </el-icon>
        </div>
      </transition>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref, reactive, watch, onUnmounted, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Van,
  Download,
  Upload,
  User,
  Key,
  DataLine,
  Setting,
  CircleCheckFilled,
  CircleCloseFilled,
  Close,
  InfoFilled
} from '@element-plus/icons-vue'
import {
  ElDescriptions,
  ElDescriptionsItem
} from 'element-plus'
import axios from 'axios'

const formatOnlineTime = (timestamp) => {
  if (!timestamp) return '0分钟'
  const minutes = Math.floor((Date.now() - timestamp) / 1000 / 60)
  if (minutes < 60) {
    return `${minutes}分钟`
  }
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60
  return `${hours}小时${remainingMinutes}分钟`
}

const formatDate = (timestamp) => {
  if (!timestamp) return '-'
  return new Date(timestamp).toLocaleString()
}

const form = reactive({
  openId: 'o14qs66zC-4S9F2kU2Sp0Z74yQ6E',
  gems: 0,
  diamond: 0,
  star: 0,
  feel: 0,
  like: 0,
  vip: false,
  musicEnabled: true,
  soundEnabled: true,
  playerSex: 1,
  onlineTime: 0,
  launchTimes: 0,
  lastUpdate: null,
  bookId: null
})

const loading = reactive({
  download: false,
  upload: false
})

const hasData = ref(false)
const showResult = ref(false)
const resultConfig = reactive({
  icon: 'success',
  title: '',
  subTitle: ''
})

// 添加触摸事件配置
const touchOptions = {
  passive: true
}

onMounted(() => {
  // 为可滚动元素添加触摸事件监听器
  const scrollableElements = document.querySelectorAll('.card-content')
  scrollableElements.forEach(element => {
    element.addEventListener('touchstart', () => { }, touchOptions)
    element.addEventListener('touchmove', () => { }, touchOptions)
  })
})

onUnmounted(() => {
  // 清理事件监听器
  const scrollableElements = document.querySelectorAll('.card-content')
  scrollableElements.forEach(element => {
    element.removeEventListener('touchstart', () => { }, touchOptions)
    element.removeEventListener('touchmove', () => { }, touchOptions)
  })
})

// 添加一个变量保存原始数据
const originalData = ref(null)

// 修改下载数据处理函数
const handleDownload = async () => {
  if (!form.openId) {
    showErrorResult('请输入OpenID')
    return
  }

  loading.download = true
  try {
    const bookIdResponse = await axios.get(
      `https://userbooks.cn.hundongstudio.com/api/books/com.soul.used.car.tycoon.h5.cn/${form.openId}`
    )

    if (!bookIdResponse.data?.data?.length) {
      throw new Error('无法获取游戏进度')
    }

    const bookId = bookIdResponse.data.data[0].book_id
    const downloadResponse = await axios.get(
      `https://userbooks.cn.hundongstudio.com/api/download_books/com.soul.used.car.tycoon.h5.cn/${form.openId}/${bookId}`
    )

    // 保存原始数据
    originalData.value = JSON.parse(downloadResponse.data.data.value)
    const userData = JSON.parse(originalData.value.GAME_USER_DATA)

    // 更新表单数据
    form.gems = userData.gems
    form.diamond = userData.diamond
    form.star = userData.star
    form.feel = userData.feel
    form.like = userData.like
    form.vip = originalData.value.GAME_USER_VIP === 1
    form.musicEnabled = originalData.value.GAME_MUSIC === 1
    form.soundEnabled = originalData.value.GAME_SOUND === 1
    form.playerSex = originalData.value.GAME_PLAYER_SEX

    // 添加新数据
    form.onlineTime = originalData.value.GAME_ON_LINE_TIME
    form.launchTimes = originalData.value.GAME_MASTER_LAUNCH_TIMES
    form.lastUpdate = downloadResponse.data.data.updated_at
    form.bookId = downloadResponse.data.data.book_id

    hasData.value = true
    showSuccessResult('数据下载成功')

  } catch (error) {
    console.error('下载数据失败:', error)
    showErrorResult(error.message || '下载数据失败')
  } finally {
    loading.download = false
  }
}

// 修改上传数据处理函数
const handleUpload = async () => {
  if (!hasData.value || !originalData.value) {
    showErrorResult('请先下载数据')
    return
  }

  loading.upload = true
  try {
    // 基于原始数据进行修改
    const gameData = { ...originalData.value }

    // 更新用户数据
    gameData.GAME_USER_DATA = JSON.stringify({
      gems: form.gems,
      diamond: form.diamond,
      star: form.star,
      feel: form.feel,
      like: form.like
    })

    // 更新其他设置
    gameData.GAME_USER_VIP = form.vip ? 1 : 0
    gameData.GAME_MUSIC = form.musicEnabled ? 1 : 0
    gameData.GAME_SOUND = form.soundEnabled ? 1 : 0
    gameData.GAME_PLAYER_SEX = form.playerSex
    gameData.GAME_COMMON_DIAMOND = form.diamond

    // 更新时间��关字段
    const currentTime = Date.now()
    gameData.GAME_ON_LINE_TIME = currentTime
    gameData.GAME_ON_LINE_TIME_FEEL = currentTime
    gameData.GAME_AD_PUBLISH_TIME = currentTime - 2000
    gameData.GAME_RED_AD_SETTING = currentTime + 100000
    gameData.GAME_PUBLICITY_SIGNIN_TIME = currentTime

    // 创建 FormData 对象
    const formData = new FormData()
    formData.append('value', JSON.stringify(gameData))
    formData.append('book_name', '%E6%B8%B8%E6%88%8F%E5%AD%98%E6%A1%A3')

    // 发送请求
    await axios.post(
      `https://userbooks.cn.hundongstudio.com/api/add_books/com.soul.used.car.tycoon.h5.cn/${form.openId}`,
      formData,
      {
        headers: {
          'xweb_xhr': '1',
          'content-type': 'multipart/form-data; boundary=soulformdata',
          'accept': '*/*',
          'accept-language': 'zh-CN,zh;q=0.9'
        }
      }
    )

    showSuccessResult('数据上传成功')

  } catch (error) {
    console.error('上传数据失败:', error)
    showErrorResult('数据上传失败')
  } finally {
    loading.upload = false
  }
}

// 显示成功结果
const showSuccessResult = (message) => {
  resultConfig.icon = 'success'
  resultConfig.title = '操作成功'
  resultConfig.subTitle = message
  showResult.value = true
  setTimeout(() => {
    showResult.value = false
  }, 3000)
}

// 显示错误结果
const showErrorResult = (message) => {
  resultConfig.icon = 'error'
  resultConfig.title = '操作失败'
  resultConfig.subTitle = message
  showResult.value = true
  setTimeout(() => {
    showResult.value = false
  }, 3000)
}

// 清理定时器
let hideTimer = null

watch(showResult, (newVal) => {
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
  if (newVal) {
    hideTimer = setTimeout(() => {
      showResult.value = false
    }, 3000)
  }
})

onUnmounted(() => {
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
})

const showTipImage = ref(false)
const showVipImage = ref(false)
</script>

<style scoped>
.used-car-game {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.game-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.card-header-wrapper {
  background: linear-gradient(135deg, #67C23A 0%, #95D475 100%);
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.header-icon {
  font-size: 20px;
}

.card-content {
  padding: 15px;
}

.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  position: relative;
}

.basic-info-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 10px;
}

/* 动画相关样式 */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.5s ease;
  max-height: 2000px;
  opacity: 1;
  margin-top: 10px;
}

.expand-enter-from,
.expand-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  margin-top: 0;
}

.animate-section {
  animation: slideIn 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 消息提示样式 */
.alert-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 14px 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  z-index: 9999;
  min-width: 380px;
  max-width: 600px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.alert-message.success {
  background: rgba(240, 249, 235, 0.95);
  border-left: 4px solid #67c23a;
}

.alert-message.error {
  background: rgba(254, 240, 240, 0.95);
  border-left: 4px solid #f56c6c;
}

.alert-icon {
  font-size: 22px;
  flex-shrink: 0;
}

.success .alert-icon {
  color: #67c23a;
  animation: bounceIn 0.5s;
}

.error .alert-icon {
  color: #f56c6c;
  animation: shakeX 0.5s;
}

.alert-content {
  flex: 1;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  line-height: 1.4;
}

.close-icon {
  cursor: pointer;
  color: #909399;
  font-size: 18px;
  transition: all 0.3s;
  opacity: 0.7;
  flex-shrink: 0;
  margin-left: 8px;
}

.close-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* 响应布局调整 */
@media (max-width: 768px) {
  .used-car-game {
    padding: 10px;
  }

  .form-section {
    padding: 10px;
  }

  .el-col {
    width: 100% !important;
  }

  .basic-info-container {
    padding: 0 10px;
  }

  .alert-message {
    min-width: 300px;
    max-width: 90%;
    padding: 12px 16px;
  }

  .alert-icon {
    font-size: 20px;
  }

  .alert-content {
    font-size: 13px;
  }
}

/* Element Plus 组件样式覆盖 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-switch) {
  --el-switch-on-color: #67C23A;
}

:deep(.el-button) {
  min-width: 120px;
}

:deep(.el-form-item__content) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

:deep(.el-row) {
  margin: 0 -10px;
}

:deep(.el-col) {
  padding: 0 10px;
}

/* 隐藏滚动条但保持滚动功能 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
  margin-right: -17px !important;
  /* 补偿滚动条宽度 */
  margin-bottom: -17px !important;
}

:deep(.el-scrollbar__view) {
  padding-right: 17px;
  /* 补偿边距 */
  padding-bottom: 17px;
}

:deep(.el-scrollbar__bar.is-horizontal) {
  display: none !important;
}

:deep(.el-scrollbar__bar.is-vertical) {
  display: none !important;
}

/* 在 WebKit 浏览器中隐藏滚动条 */
.used-car-game::-webkit-scrollbar {
  display: none;
}

/* 在 Firefox 中隐藏滚动条 */
.used-car-game {
  scrollbar-width: none;
}

.tip-text {
  margin-left: auto;
  color: #f56c6c;
  font-size: 14px;
  cursor: pointer;
  position: relative;
}

.tip-image {
  position: absolute;
  right: 50%;
  transform: translateX(50%);
  bottom: 100%;
  margin-bottom: 10px;
  z-index: 1000;
  padding: 5px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.tip-image::after {
  content: '';
  position: absolute;
  bottom: -6px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid white;
}

.tip-image img {
  max-width: 300px;
  height: auto;
  display: block;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .tip-text {
    font-size: 12px;
  }

  .tip-image img {
    max-width: 200px;
  }
}

.vip-wrapper {
  position: relative;
  display: inline-block;
}

.vip-tip {
  position: absolute;
  bottom: 100%;
  right: 0;
  transform: translateX(-20%);
  margin-bottom: 10px;
  z-index: 1000;
  padding: 5px;
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.vip-tip::after {
  content: '';
  position: absolute;
  bottom: -6px;
  right: 20%;
  transform: translateX(50%);
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid white;
}

.vip-tip img {
  max-width: 300px;
  height: 300px;
  display: block;
  object-fit: contain;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .vip-tip img {
    max-width: 200px;
    height: 200px;
  }
}
</style>