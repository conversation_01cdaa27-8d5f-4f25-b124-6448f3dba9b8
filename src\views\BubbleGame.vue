<template>
  <div class="bubble-game">
    <el-card class="game-card" :body-style="{ padding: '0' }">
      <!-- 顶部标题区域 -->
      <div class="card-header-wrapper">
        <div class="card-header">
          <el-icon class="header-icon"><Lollipop /></el-icon>
          <span>一起泡泡龙数据修改</span>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="card-content">
        <el-form :model="form" label-width="100px" class="game-form">
          <!-- 基础信息部分 - 始终显示 -->
          <div class="form-section">
            <div class="section-title">
              <el-icon><Key /></el-icon>
              <span>基础信息</span>
            </div>
            <div class="basic-info-container">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="OpenID">
                    <el-input 
                      v-model="form.openid" 
                      placeholder="请输入OpenID"
                      size="default"
                    >
                      <template #prefix>
                        <el-icon><User /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="Token">
                    <el-input 
                      v-model="form.token" 
                      placeholder="请输入Token"
                      size="default"
                    >
                      <template #prefix>
                        <el-icon><Lock /></el-icon>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <!-- 修改按钮布局 -->
            <el-form-item>
              <div class="button-group">
                <el-button 
                  type="primary" 
                  @click="handleDownload" 
                  :loading="loading.download" 
                  :icon="Download"
                >
                  下载数据
                </el-button>
                <el-button 
                  type="success" 
                  @click="handleUpload" 
                  :loading="loading.upload" 
                  :icon="Upload"
                  :disabled="!hasData"
                >
                  上传数据
                </el-button>
              </div>
            </el-form-item>
          </div>

          <!-- 数据已下载后才显示的内容 -->
          <transition name="expand">
            <div v-if="hasData" class="data-content">
              <!-- 游戏数据部分 -->
              <div class="form-section animate-section">
                <div class="section-title">
                  <el-icon><DataLine /></el-icon>
                  <span>游戏数据</span>
                </div>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="体力值">
                      <el-input-number v-model="form.content" :min="0" :max="999999" controls-position="right"></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="金币">
                      <el-input-number v-model="form.coin" :min="0" :max="999999999" controls-position="right"></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="钻石">
                      <el-input-number v-model="form.diamond" :min="0" :max="999999" controls-position="right"></el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="8">
                    <el-form-item label="钞票">
                      <el-input-number v-model="form.ticket" :min="0" :max="999999" controls-position="right"></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="广告卡">
                      <el-input-number v-model="form.adcard" :min="0" :max="999999" controls-position="right"></el-input-number>
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="仓库格子">
                      <el-input-number v-model="form.newbox" :min="0" :max="999" controls-position="right"></el-input-number>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 特殊功能部分 -->
              <div class="form-section animate-section">
                <div class="section-title">
                  <el-icon><MagicStick /></el-icon>
                  <span>特殊功能</span>
                </div>
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-form-item label="剪刀次数">
                      <div class="feature-item">
                        <el-switch
                          v-model="form.unlockScissors"
                          active-text="6万次"
                          inactive-text="不增加"
                          inline-prompt
                        />
                        <el-tag 
                          :type="form.hasScissors ? 'success' : 'info'" 
                          size="small" 
                          class="ml-2"
                        >
                          <el-icon class="scissors-icon"><Scissor /></el-icon>
                          {{ form.hasScissors ? `剩余${form.scissorsTimes}次` : '未拥有' }}
                        </el-tag>
                        <el-tooltip
                          :content="form.hasScissors 
                            ? `剪刀在仓库中，当前剩余${form.scissorsTimes}次使用次数` 
                            : '仓库中未拥有剪刀道具'"
                          placement="top"
                          effect="dark"
                        >
                          <el-icon class="info-icon"><InfoFilled /></el-icon>
                        </el-tooltip>
                      </div>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="仓库容量">
                      <div class="feature-item">
                        <el-switch
                          v-model="form.unlockNewbox"
                          active-text="修改"
                          inactive-text="保持"
                          inline-prompt
                        />
                        <el-tooltip
                          content="开启后将修改仓库格子数量"
                          placement="top"
                          effect="dark"
                        >
                          <el-icon class="info-icon"><InfoFilled /></el-icon>
                        </el-tooltip>
                      </div>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </div>
          </transition>

          <!-- 未下载数据时显示的提示 -->
          <transition name="fade">
            <template v-if="!hasData">
              <el-empty 
                description="先下载数据" 
                :image-size="200"
              >
                <template #description>
                  <p class="empty-text">请输入OpenID和Token并下载数据</p>
                </template>
              </el-empty>
            </template>
          </transition>
        </el-form>
      </div>
    </el-card>

    <!-- 消息提示 -->
    <transition name="slide-down">
      <div v-if="showResult" class="alert-message" :class="resultConfig.icon">
        <el-icon class="alert-icon">
          <CircleCheckFilled v-if="resultConfig.icon === 'success'" />
          <CircleCloseFilled v-else />
        </el-icon>
        <span class="alert-content">{{ resultConfig.subTitle }}</span>
        <el-icon class="close-icon" @click="showResult = false">
          <Close />
        </el-icon>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { ref, reactive, watch, onUnmounted, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Lollipop, 
  Download, 
  Upload, 
  InfoFilled, 
  Key, 
  Lock, 
  User, 
  DataLine,
  MagicStick,
  CircleCheckFilled,
  CircleCloseFilled,
  Close,
  Scissor
} from '@element-plus/icons-vue'
import axios from 'axios'
import CryptoJS from 'crypto-js'

// 表单数据
const form = reactive({
  openid: '',
  token: '',
  content: 0,
  coin: 0,
  diamond: 0,
  ticket: 0,
  adcard: 0,
  newbox: 0,
  unlockScissors: false,
  unlockNewbox: false,
  hasScissors: false, // 是否有剪刀
  scissorsTimes: 0, // 剪刀可用次数
})

// 加载状态
const loading = reactive({
  download: false,
  upload: false
})

// 结果显示
const showResult = ref(false)
const resultConfig = reactive({
  icon: 'success',
  title: '',
  subTitle: ''
})

// 添加数据状态标志
const hasData = ref(false)

// 添加游戏数据存储
const gameData = ref(null)

// 在组件挂载时检查是否有数据
onMounted(() => {
  if (gameData.value) {
    try {
      const data = gameData.value
      // 更新表单数据
      form.content = data.userInfo.vitality.hp
      form.coin = data.userInfo.gold
      form.diamond = data.userInfo.diamond
      form.ticket = data.userInfo['main_task_num_new']
      form.adcard = data.userInfo['ad_card_data'].num
      form.newbox = data.userInfo.boxindex
      
      // 检查剪刀状态
      const scissors = data.userInfo.newBoxItemList.find(item => item.id === 9001)
      form.hasScissors = !!scissors
      form.scissorsTimes = scissors ? scissors.scissorstimes : 0
      
      hasData.value = true
    } catch (error) {
      console.error('数据解析失败:', error)
      gameData.value = null
    }
  }
})

// 下载数据
const handleDownload = async () => {
  if (!form.openid || !form.token) {
    ElMessage.error('请输入OpenID和Token')
    return
  }

  loading.download = true
  try {
    const url = 'https://goyqppl.qzzgame.com/v1.0/data/getData'
    const data = new URLSearchParams()
    data.append('token', form.token)
    data.append('version', '25.0')
    data.append('openid', form.openid)
    data.append('game_id', '1000114')

    const response = await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
      }
    })

    // 检查响应状态
    if (!response.data.status) {
      // 如果状态为false，抛出错误信息
      throw new Error(response.data.result.msg || '下载失败')
    }

    // 确保数据存在
    if (!response.data.result?.data?.base_data) {
      throw new Error('返回数据格式错误')
    }

    const res_data = JSON.parse(response.data.result.data.base_data)
    
    // 保存数据到响应式变量
    gameData.value = res_data
    
    // 更新表单数据
    form.content = res_data.userInfo.vitality.hp
    form.coin = res_data.userInfo.gold
    form.diamond = res_data.userInfo.diamond
    form.ticket = res_data.userInfo['main_task_num_new']
    form.adcard = res_data.userInfo['ad_card_data'].num
    form.newbox = res_data.userInfo.boxindex

    // 检查剪刀状态
    const scissors = res_data.userInfo.newBoxItemList.find(item => item.id === 9001)
    form.hasScissors = !!scissors
    form.scissorsTimes = scissors ? scissors.scissorstimes : 0

    hasData.value = true
    showSuccessResult('数据下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    hasData.value = false
    gameData.value = null
    const errorMessage = error.response?.data?.result?.msg || error.message || '下载失败'
    showErrorResult(errorMessage)
  } finally {
    loading.download = false
  }
}

// 上传数据
const handleUpload = async () => {
  if (!hasData.value || !gameData.value) {
    ElMessage.error('请先下载数据')
    return
  }

  if (!form.content || !form.coin || !form.diamond || !form.ticket || !form.adcard) {
    ElMessage.error('请先下载数据')
    return
  }

  loading.upload = true
  try {
    const url = 'https://goyqppl.qzzgame.com/v1.0/data/saveData'
    const value_data = gameData.value

    // 修改数据
    value_data.userInfo.vitality.hp = +form.content
    value_data.userInfo.gold = +form.coin
    value_data.userInfo.diamond = +form.diamond
    value_data.userInfo['main_task_num_new'] = +form.ticket
    value_data.userInfo['ad_card_data'].num = +form.adcard

    // 处理剪刀
    if (form.unlockScissors) {
      handleScissorsData(value_data)
    }

    // 处理仓库格子
    if (form.unlockNewbox) {
      value_data.userInfo.boxindex = parseInt(form.newbox, 10)
    }

    const data_str = JSON.stringify(value_data)
    const sign = createSign(data_str)

    const data = new URLSearchParams()
    data.append('field', 'base_data')
    data.append('value', data_str)
    data.append('sign', sign)
    data.append('key_version', '0')
    data.append('isReset', '0')
    data.append('token', form.token)
    data.append('version', '25.0')
    data.append('game_id', '1000114')

    await axios.post(url, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
      }
    })

    showSuccessResult('数据上传成功')
  } catch (error) {
    console.error('上传失败:', error)
    showErrorResult('数据上传失败')
  } finally {
    loading.upload = false
  }
}

// 处理剪刀数据
const handleScissorsData = (value_data) => {
  let inserted = false
  for (let i = 0; i < value_data.userInfo.newBoxItemList.length; i++) {
    const item = value_data.userInfo.newBoxItemList[i]
    if (('endTime' in item && Object.keys(item).length === 1) || Object.keys(item).length === 0) {
      value_data.userInfo.newBoxItemList.splice(i, 0, { id: 9001, state: 0, scissorstimes: 60000 })
      inserted = true
      break
    }
  }

  if (!inserted) {
    value_data.userInfo.newBoxItemList.push({ id: 9001, state: 0, scissorstimes: 60000 })
  }
}

// 创建签名
const createSign = (data) => {
  const signStr = data + "&" + '******************************************************'
  return CryptoJS.SHA1(signStr).toString(CryptoJS.enc.Hex)
}

// 修改显示成功结果函数
const showSuccessResult = (message) => {
  resultConfig.icon = 'success'
  resultConfig.title = '操作成功'
  resultConfig.subTitle = message
  showResult.value = true
  // 3秒后自动隐藏
  setTimeout(() => {
    showResult.value = false
  }, 3000)
}

// 修改显示错误结果函数
const showErrorResult = (message) => {
  resultConfig.icon = 'error'
  resultConfig.title = '操作失败'
  resultConfig.subTitle = message
  showResult.value = true
  // 3秒后自动隐藏
  setTimeout(() => {
    showResult.value = false
  }, 3000)
}

// 可以添加一个清理定时器的功能
let hideTimer = null

// 监听 showResult 的变化
watch(showResult, (newVal) => {
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
  if (newVal) {
    hideTimer = setTimeout(() => {
      showResult.value = false
    }, 3000)
  }
})

// 组件卸载时清理定时器
onUnmounted(() => {
  if (hideTimer) {
    clearTimeout(hideTimer)
  }
})
</script>

<style scoped>
.bubble-game {
  padding: 0;
  max-width: 1000px;
  margin: 0 auto;
  height: calc(100vh - 100px);
  display: flex;
  flex-direction: column;
}

.game-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.05);
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header-wrapper {
  background: linear-gradient(135deg, #409EFF 0%, #36cfc9 100%);
  padding: 16px 20px;
  border-radius: 8px 8px 0 0;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
}

.header-icon {
  font-size: 20px;
}

.card-content {
  padding: 15px;
  flex: 1;
  overflow-y: auto;
}

.form-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-icon {
  color: #909399;
  cursor: help;
  transition: color 0.3s;
}

.info-icon:hover {
  color: #409EFF;
}

.form-actions {
  margin-top: 15px;
  padding: 10px 0;
  background: #fff;
  position: sticky;
  bottom: 0;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-switch) {
  --el-switch-on-color: #409EFF;
}

:deep(.el-button) {
  padding: 12px 20px;
}

.result-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 2000;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  width: 320px;
  padding: 20px;
}

:deep(.el-result) {
  padding: 20px 0;
}

:deep(.el-result__icon) {
  margin-bottom: 15px;
}

:deep(.el-result__title) {
  margin-top: 10px;
  font-size: 16px;
}

:deep(.el-result__subtitle) {
  margin-top: 10px;
  font-size: 14px;
  padding: 0 20px;
}

:deep(.el-result__extra) {
  margin-top: 15px;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .bubble-game {
    height: calc(100vh - 80px);
  }

  .form-section {
    padding: 10px;
  }

  :deep(.el-col) {
    margin-bottom: 8px;
  }
}

/* 添加空状态样式 */
.empty-text {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

/* 调整基础信息区域的样式 */
.form-section:first-child {
  margin-bottom: 20px;
}

/* 未下载数据时的占位区域样式 */
.el-empty {
  padding: 40px 0;
}

/* 展开动画相关样式 */
.expand-enter-active,
.expand-leave-active {
  transition: all 0.5s ease;
  max-height: 2000px;
  opacity: 1;
  margin-top: 10px;
}

.expand-enter-from,
.expand-leave-to {
  max-height: 0;
  opacity: 0;
  overflow: hidden;
  margin-top: 0;
}

.data-content {
  overflow: hidden;
  margin-top: 10px;
}

/* 为每个区块添加渐入动画 */
.animate-section {
  animation: slideIn 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.animate-section:nth-child(1) {
  animation-delay: 0.1s;
}

.animate-section:nth-child(2) {
  animation-delay: 0.2s;
}

.animate-section:nth-child(3) {
  animation-delay: 0.3s;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 淡入淡出动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 优化表单区域的过渡效果 */
.form-section {
  transition: all 0.3s ease;
}

.form-actions {
  transition: all 0.3s ease;
}

/* 确保动画流畅 */
:deep(.el-form-item) {
  transition: all 0.3s ease;
}

/* 优化响应式布局时的动画 */
@media (max-width: 768px) {
  .expand-enter-active,
  .expand-leave-active {
    transition-duration: 0.3s;
  }
  
  .animate-section {
    animation-duration: 0.3s;
  }
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
}

:deep(.el-button) {
  min-width: 120px;  /* 确保按钮宽度一致 */
}

.basic-info-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

:deep(.el-form-item__content) {
  width: 100%;
}

:deep(.el-input__wrapper) {
  width: 100%;
}

:deep(.el-form-item) {
  margin-bottom: 15px;
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 10px;
}

/* 响应式布局调整 */
@media (max-width: 768px) {
  .el-col {
    width: 100% !important;
  }
  
  .basic-info-container {
    padding: 0 10px;
  }
}

:deep(.el-row) {
  margin: 0 -10px;
}

:deep(.el-col) {
  padding: 0 10px;
}

/* 修改消息提示部分的样式 */
.alert-message {
  position: fixed;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  padding: 14px 24px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
  z-index: 9999;
  min-width: 380px;
  max-width: 600px;
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.alert-message.success {
  background: rgba(240, 249, 235, 0.95);
  border-left: 4px solid #67c23a;
}

.alert-message.error {
  background: rgba(254, 240, 240, 0.95);
  border-left: 4px solid #f56c6c;
}

.alert-icon {
  font-size: 22px;
  flex-shrink: 0;
}

.success .alert-icon {
  color: #67c23a;
  animation: bounceIn 0.5s;
}

.error .alert-icon {
  color: #f56c6c;
  animation: shakeX 0.5s;
}

.alert-content {
  flex: 1;
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  line-height: 1.4;
}

.close-icon {
  cursor: pointer;
  color: #909399;
  font-size: 18px;
  transition: all 0.3s;
  opacity: 0.7;
  flex-shrink: 0;
  margin-left: 8px;
}

.close-icon:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* 优化滑入画 */
.slide-down-enter-active {
  animation: slideInDown 0.4s ease-out;
}

.slide-down-leave-active {
  animation: slideOutUp 0.4s ease-in;
}

@keyframes slideInDown {
  from {
    transform: translate(-50%, -120%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, 0);
    opacity: 1;
  }
}

@keyframes slideOutUp {
  from {
    transform: translate(-50%, 0);
    opacity: 1;
  }
  to {
    transform: translate(-50%, -120%);
    opacity: 0;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.1);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shakeX {
  0%, 100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-4px);
  }
  50% {
    transform: translateX(4px);
  }
  75% {
    transform: translateX(-4px);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .alert-message {
    min-width: 300px;
    max-width: 90%;
    padding: 12px 16px;
  }
  
  .alert-icon {
    font-size: 20px;
  }
  
  .alert-content {
    font-size: 13px;
  }
}

.ml-2 {
  margin-left: 8px;
}

.scissors-icon {
  margin-right: 4px;
  font-size: 14px;
  vertical-align: middle;
}
</style> 