<template>
  <div class="lazy-brother">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon class="icon-title">
                <MoonNight />
              </el-icon>
              <span>{{ gameConfigs[selectedGame].label }}</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 数据输入区域 -->
          <div class="data-input-section card-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon class="icon-document">
                  <Document />
                </el-icon>
                <span>游戏数据</span>
              </div>
              <el-button type="danger" size="small" class="clear-button" @click="handleClearData" :icon="Delete">
                清空数据
              </el-button>
            </div>

            <!-- 玩家ID输入框 -->
            <el-form :model="formData" label-position="top">
              <el-form-item label="游戏选择">
                <el-select v-model="selectedGame" placeholder="请选择游戏" style="width: auto; min-width: 180px;"
                  :disabled="loading.download || loading.upload">
                  <el-option v-for="(config, key) in gameConfigs" :key="key" :label="config.label" :value="key" />
                </el-select>
              </el-form-item>
              <el-form-item label="玩家UUID">
                <el-input v-model="formData.playerId" placeholder="请输入玩家UUID" clearable
                  :disabled="loading.download || loading.upload" />
              </el-form-item>
            </el-form>

            <!-- 操作按钮 -->
            <div class="action-section">
              <el-button type="primary" :loading="loading.download" @click="handleDownload" :icon="Download"
                class="action-button">
                下载数据
              </el-button>
              <el-button type="success" :loading="loading.upload" @click="handleUpload" :icon="Upload"
                :disabled="!hasData" class="action-button">
                上传数据
              </el-button>
            </div>
          </div>

          <!-- 游戏数据编辑区域 -->
          <div v-if="hasData" class="game-data-section">
            <div class="section-title">
              <el-icon class="icon-document">
                <Document />
              </el-icon>
              <span>游戏数据</span>
            </div>

            <el-tabs type="border-card" class="custom-tabs">
              <!-- 基础属性 -->
              <el-tab-pane label="基础属性">
                <el-form :model="gameData" label-position="top" class="custom-form">
                  <div class="attribute-section">
                    <div class="attribute-header">
                      <el-icon class="icon-resource">
                        <Coin />
                      </el-icon>
                      <span>资源属性</span>
                    </div>
                    <el-row :gutter="20">
                      <el-col :span="6">
                        <el-form-item label="体力">
                          <el-input v-model="gameData.energy" class="custom-input">
                            <template #prefix>
                              <el-icon class="icon-energy">
                                <Lightning />
                              </el-icon>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item label="金币">
                          <el-input v-model="gameData.coins" class="custom-input">
                            <template #prefix>
                              <el-icon class="icon-coins">
                                <GoldMedal />
                              </el-icon>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item label="钻石">
                          <el-input v-model="gameData.gems" class="custom-input">
                            <template #prefix>
                              <el-icon class="icon-gems">
                                <Star />
                              </el-icon>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                      <el-col :span="6">
                        <el-form-item label="广告券">
                          <el-input v-model="gameData.ads" class="custom-input">
                            <template #prefix>
                              <el-icon class="icon-ads">
                                <Ticket />
                              </el-icon>
                            </template>
                          </el-input>
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </div>

                  <!-- 添加TPDG-Record-MBack数据区域 -->
                  <div class="attribute-section">
                    <div class="attribute-header">
                      <el-icon class="icon-document">
                        <DataAnalysis />
                      </el-icon>
                      <span>游戏主进度记录 (TPDG-Record-MBack)</span>
                    </div>
                    <div class="record-data-content">
                      <div class="record-data-info" v-if="recordMBackParsed">
                        <div class="record-data-summary">
                          <div class="summary-item">
                            <div class="summary-icon icon-calendar">
                              <el-icon>
                                <Calendar />
                              </el-icon>
                            </div>
                            <div class="summary-content">
                              <div class="summary-value">{{ recordMBackParsed.dailyData?.curday || '0' }}</div>
                              <div class="summary-label">游戏天数</div>
                            </div>
                          </div>
                          <div class="summary-item">
                            <div class="summary-icon icon-sweep">
                              <el-icon>
                                <Refresh />
                              </el-icon>
                            </div>
                            <div class="summary-content">
                              <div class="summary-value">{{ recordMBackParsed.dailyData?.sweep_count || '0' }}</div>
                              <div class="summary-label">今日扫荡次数</div>
                            </div>
                          </div>
                          <div class="summary-item">
                            <div class="summary-icon icon-sign">
                              <el-icon>
                                <Check />
                              </el-icon>
                            </div>
                            <div class="summary-content">
                              <div class="summary-value">{{ recordMBackParsed.dailyData?.isSign ? '已签到' : '未签到' }}</div>
                              <div class="summary-label">今日签到状态</div>
                            </div>
                          </div>
                          <div class="summary-item">
                            <div class="summary-icon icon-level">
                              <el-icon>
                                <VideoPlay />
                              </el-icon>
                            </div>
                            <div class="summary-content">
                              <el-input-number v-model="currentMaxLevel" :min="0" :precision="0" size="small"
                                class="level-input-number" />
                              <div class="summary-label">当前最高关卡</div>
                            </div>
                          </div>
                        </div>

                        <div class="detail-section">
                          <div class="detail-title">详细信息</div>
                          <el-descriptions :column="2" border size="small">
                            <el-descriptions-item label="广告签到状态">
                              {{ recordMBackParsed.dailyData?.isAdSign ? '已签到' : '未签到' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="广告扫荡状态">
                              {{ recordMBackParsed.dailyData?.isADSweepNum ? '已使用' : '未使用' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="已解锁关卡" :span="2">
                              <div class="level-edit-container">
                                <el-input v-model="unlockedLevelsInput" class="level-edit-input"
                                  placeholder="输入关卡ID，用逗号分隔，如：1,2,3" />
                                <el-tooltip content="格式: 1,2,3,4 (用逗号分隔)" placement="top">
                                  <el-icon class="help-icon">
                                    <QuestionFilled />
                                  </el-icon>
                                </el-tooltip>
                              </div>
                              <div class="levels-preview" v-if="parsedLevels.length > 0">
                                <div class="preview-title">已解锁关卡预览:</div>
                                <div class="preview-tags">
                                  <el-tag v-for="lvId in parsedLevels" :key="lvId" class="level-tag" type="success">
                                    关卡{{ lvId }}
                                  </el-tag>
                                </div>
                              </div>
                            </el-descriptions-item>
                            <el-descriptions-item label="关卡轮次奖励" :span="2">
                              {{ isEmpty(recordMBackParsed.lvRoundReward) ? '无奖励记录' : '有奖励记录' }}
                            </el-descriptions-item>
                            <el-descriptions-item label="助力列表" :span="2">
                              {{ recordMBackParsed.helplist && recordMBackParsed.helplist.length ?
                                `${recordMBackParsed.helplist.length}个助力` : '无助力记录' }}
                            </el-descriptions-item>
                          </el-descriptions>
                        </div>
                      </div>

                      <div v-if="!recordMBackParsed" class="no-data-tip">
                        <el-empty description="暂无进度数据" />
                      </div>

                      <el-collapse class="raw-data-collapse">
                        <el-collapse-item title="原始JSON数据" name="1">
                          <el-input v-model="gameData.recordMBack" type="textarea" :rows="6" class="code-textarea"
                            readonly />
                        </el-collapse-item>
                      </el-collapse>
                    </div>
                  </div>
                </el-form>
              </el-tab-pane>

              <!-- 碎片信息 -->
              <el-tab-pane label="碎片信息">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="24">
                      <div class="fragment-header">
                        <div class="attribute-header">
                          <el-icon class="icon-fragment">
                            <Collection />
                          </el-icon>
                          <span>碎片列表</span>
                        </div>
                        <div class="button-group">
                          <el-button type="success" size="small" @click="addAllFragments" class="add-all-button">
                            <el-icon>
                              <MagicStick />
                            </el-icon> 一键添加碎片
                          </el-button>
                          <el-button type="primary" size="small" @click="showAddFragmentDialog" class="add-button">
                            <el-icon>
                              <Plus />
                            </el-icon> 添加碎片
                          </el-button>
                        </div>
                      </div>
                      <el-form-item>
                        <el-table :data="fragmentsTableData" border style="width: 100%" class="custom-table">
                          <el-table-column prop="id" label="碎片ID" width="120" />
                          <el-table-column prop="name" label="碎片名称" width="150">
                            <template #default="scope">
                              <div class="fragment-name">
                                <el-icon>
                                  <Collection />
                                </el-icon>
                                {{ getFragmentName(scope.row.id) }}
                              </div>
                            </template>
                          </el-table-column>
                          <el-table-column prop="num" label="数量" width="180">
                            <template #default="scope">
                              <el-input-number v-model="scope.row.num" :min="0" :precision="0" size="small"
                                class="custom-number-input" />
                            </template>
                          </el-table-column>
                          <el-table-column prop="type" label="类型" />
                          <el-table-column label="操作" width="100">
                            <template #default="scope">
                              <el-button type="danger" size="small" @click="removeFragment(scope.row.id)"
                                class="delete-button">
                                <el-icon>
                                  <Delete />
                                </el-icon>
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </el-form-item>
                      <div class="fragment-json-display">
                        <small>原始JSON数据（编辑上方表格会自动更新）：</small>
                        <el-input v-model="gameData.fragments" type="textarea" :rows="3" readonly
                          class="code-textarea" />
                      </div>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 操作历史区域 -->
          <div v-if="operationHistory.length > 0" class="operation-history">
            <div class="section-title">
              <el-icon class="icon-history">
                <Timer />
              </el-icon>
              <span>操作历史</span>
            </div>
            <el-timeline>
              <el-timeline-item v-for="(item, index) in operationHistory.slice(0, 5)" :key="index"
                :timestamp="item.time" :type="item.success ? 'success' : 'danger'">
                {{ item.operation }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 添加碎片对话框 -->
    <el-dialog v-model="addFragmentDialogVisible" title="添加碎片" width="30%" append-to-body>
      <el-form :model="newFragment" label-width="80px">
        <el-form-item label="碎片ID">
          <el-input v-model="newFragment.id" type="number" />
        </el-form-item>
        <el-form-item label="碎片数量">
          <el-input-number v-model="newFragment.num" :min="1" :precision="0" style="width: 100%" />
        </el-form-item>
        <el-form-item label="碎片类型">
          <el-input-number v-model="newFragment.type" :min="1" :precision="0" style="width: 100%" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addFragmentDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="addFragment">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  MoonNight,
  Document,
  Download,
  Upload,
  Delete,
  Timer,
  Plus,
  Coin,
  Lightning,
  GoldMedal,
  Star,
  Ticket,
  UserFilled,
  Trophy,
  DataAnalysis,
  InfoFilled,
  Collection,
  Calendar,
  Refresh,
  Check,
  VideoPlay,
  QuestionFilled,
  MagicStick
} from '@element-plus/icons-vue'

// 游戏配置
const gameConfigs = {
  lazyBrother: {
    key: 'lazyBrother',
    label: '躺平小弟',
    appName: 'tpxddlwx',
    version: '1.0.0',
    dKeys: 'TPDG',
    defaultUUID: 'oScNV5Ex0lzhOIOyzzL82aF_nnNk'
  },
  dragonHero: {
    key: 'dragonHero',
    label: '勇者屠恶龙',
    appName: 'yztelwx',
    version: '1.0.0',
    dKeys: 'TPDG',
    defaultUUID: 'oad9p5XnMR15SQZrVLtvCNpBQBOg'
  }
}

// 当前选中的游戏
const selectedGame = ref('lazyBrother')

// 表单数据
const formData = reactive({
  playerId: gameConfigs.lazyBrother.defaultUUID
})

// 游戏数据
const gameData = reactive({
  coins: '0',      // 金币
  gems: '0',       // 钻石
  energy: '0',     // 体力
  ads: '0',        // 广告券
  level: '1',      // 等级
  exp: '0',        // 经验
  version: '1.0.0', // 版本号
  item1: '0',      // 道具1
  item2: '0',      // 道具2
  item3: '0',      // 道具3
  skills: '[]',    // 技能列表
  skillLevels: '[]', // 技能等级
  achievements: '[]', // 已完成成就
  achievementProgress: '[]', // 成就进度
  fragments: '',    // 碎片数据
  recordMBack: ''  // TPDG-Record-MBack 数据
})

// 加载状态
const loading = reactive({
  download: false,
  upload: false
})

// 是否有数据
const hasData = ref(false)

// 已解锁关卡输入
const unlockedLevelsInput = ref('');

// 当前最高关卡输入
const currentMaxLevel = ref(0);

// 操作历史
const operationHistory = ref([])

// 碎片表格数据
const fragmentsTableData = ref([])

// 碎片名称映射
const fragmentNames = {
  1000: '手枪碎片',
  1001: '机枪碎片',
  1002: '霰弹枪碎片',
  1004: '淬毒之刃碎片',
  1005: '爆裂弓碎片',
  1006: '龙之力碎片',
  1007: '黑洞枪碎片',
  1008: '镭射剑碎片'
}

// 获取碎片名称
const getFragmentName = (id) => {
  return fragmentNames[id] || `碎片${id}`
}

// 根据JSON字符串更新表格数据
const updateFragmentsTable = () => {
  try {
    const fragmentsData = JSON.parse(gameData.fragments || '{}')
    const tableData = []

    for (const [id, data] of Object.entries(fragmentsData)) {
      tableData.push({
        id,
        type: data.type || 1,
        num: data.num || 0
      })
    }

    fragmentsTableData.value = tableData
  } catch (error) {
    console.error('解析碎片数据失败:', error)
    fragmentsTableData.value = []
  }
}

// 监听表格数据变化，更新JSON
watch(fragmentsTableData, (newValue) => {
  const fragmentsData = {}

  for (const item of newValue) {
    fragmentsData[item.id] = {
      type: item.type,
      id: parseInt(item.id),
      num: item.num
    }
  }

  gameData.fragments = JSON.stringify(fragmentsData)
}, { deep: true })

// 监听JSON变化，更新表格
watch(() => gameData.fragments, (newValue) => {
  updateFragmentsTable()
})

// 添加操作历史记录
const addHistory = (operation, success = true) => {
  operationHistory.value.unshift({
    operation,
    success,
    time: new Date().toLocaleString()
  })
}

// 清空数据
const handleClearData = () => {
  if (!hasData.value) return

  // 重置游戏数据
  Object.assign(gameData, {
    coins: '0',
    gems: '0',
    energy: '0',
    ads: '0',
    level: '1',
    exp: '0',
    version: '1.0.0',
    item1: '0',
    item2: '0',
    item3: '0',
    skills: '[]',
    skillLevels: '[]',
    achievements: '[]',
    achievementProgress: '[]',
    fragments: '{}',
    recordMBack: '{}'
  })

  // 清空已解锁关卡输入
  unlockedLevelsInput.value = '';

  // 清空当前最高关卡
  currentMaxLevel.value = 0;

  hasData.value = false
  addHistory('清空数据成功')
  ElMessage.success('数据已清空')
}

// 下载数据
const handleDownload = async () => {
  if (!formData.playerId) {
    ElMessage.warning('请输入玩家UUID')
    return
  }

  loading.download = true
  try {
    // 获取当前选择的游戏配置
    const currentGame = gameConfigs[selectedGame.value]

    // 使用真实API请求
    const params = {
      app_name: currentGame.appName,
      version: currentGame.version,
      uuid: formData.playerId,
      d_keys: currentGame.dKeys
    }

    const response = await fetch(`https://game.zuiqiangyingyu.net/common/game-data/multi-get?${new URLSearchParams(params)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
        'Referer': 'https://servicewechat.com/wx588c1217403a3611/7/page-frame.html'
      }
    })

    const data = await response.json()

    if (data.code === 0 && data.msg === "成功") {
      // 解析TPDG数据为JSON对象
      const tpdgData = JSON.parse(data.data.TPDG)

      // 从userdata中提取基本信息
      const userData = JSON.parse(tpdgData.userdata || '{}')

      // 从MCatGame中提取游戏数据
      const catGameData = JSON.parse(tpdgData.MCatGame || '{}')

      // 获取碎片数据
      const fragmentsData = tpdgData.bph_fragmentspack || '{}'

      // 获取TPDG-Record-MBack数据
      const recordMBackData = tpdgData['TPDG-Record-MBack'] || '{}'

      // 更新游戏数据，根据API响应显示正确的值
      // 保留原始数据的类型（数字或字符串）
      Object.assign(gameData, {
        // 使用MCatGame中的数据，保留原始类型
        energy: catGameData['1'] ? String(catGameData['1'].num || '0') : '0',
        coins: catGameData['2'] ? String(catGameData['2'].num || '0') : '0',
        gems: catGameData['11'] ? String(catGameData['11'].num || '0') : '0',
        ads: catGameData['17'] ? String(catGameData['17'].num || '0') : '0',

        // 其他用户数据
        level: userData.rolelevel ? String(userData.rolelevel) : '1',
        exp: userData.roleexp ? String(userData.roleexp) : '0',
        version: userData.v || '1.0.0',

        // 碎片数据
        fragments: fragmentsData,

        // TPDG-Record-MBack数据
        recordMBack: recordMBackData
      })

      hasData.value = true
      addHistory('下载数据成功')
      ElMessage.success('数据下载成功')

      // 更新碎片表格
      updateFragmentsTable()
    } else {
      throw new Error(data.msg || '数据下载失败')
    }
  } catch (error) {
    console.error('下载数据失败:', error)
    addHistory('下载数据失败', false)
    ElMessage.error('下载数据失败: ' + error.message)
  } finally {
    loading.download = false
  }
}

// 上传数据
const handleUpload = async () => {
  if (!formData.playerId) {
    ElMessage.warning('请输入玩家UUID')
    return
  }

  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.upload = true
  try {
    // 获取当前选择的游戏配置
    const currentGame = gameConfigs[selectedGame.value]

    // 获取原始数据
    const params = {
      app_name: currentGame.appName,
      version: currentGame.version,
      uuid: formData.playerId,
      d_keys: currentGame.dKeys
    }

    const getResponse = await fetch(`https://game.zuiqiangyingyu.net/common/game-data/multi-get?${new URLSearchParams(params)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
        'Referer': 'https://servicewechat.com/wx588c1217403a3611/7/page-frame.html'
      }
    })

    const getData = await getResponse.json()

    if (getData.code === 0 && getData.msg === "成功") {
      // 解析原始数据，保留完整结构
      const originalData = JSON.parse(getData.data.TPDG)

      // 修改用户数据
      if (originalData.userdata) {
        const userData = JSON.parse(originalData.userdata || '{}')

        // 保持数据类型一致
        const levelType = typeof userData.rolelevel
        if (levelType === 'number') {
          userData.rolelevel = parseInt(gameData.level)
        } else if (levelType === 'string') {
          userData.rolelevel = gameData.level
        } else if (gameData.level !== '1') {
          // 如果原数据没有这个字段但用户修改了，添加它
          userData.rolelevel = parseInt(gameData.level)
        }

        const expType = typeof userData.roleexp
        if (expType === 'number') {
          userData.roleexp = parseInt(gameData.exp)
        } else if (expType === 'string') {
          userData.roleexp = gameData.exp
        } else if (gameData.exp !== '0') {
          // 如果原数据没有这个字段但用户修改了，添加它
          userData.roleexp = parseInt(gameData.exp)
        }

        // 更新回原始数据
        originalData.userdata = JSON.stringify(userData)
      }

      // 修改MCatGame数据，保留原始对象的所有属性
      const catGameData = JSON.parse(originalData.MCatGame || '{}')

      // 只更新用户修改的值，保持数据类型一致
      if (catGameData['1']) {
        // 转换为相同类型 - 如果原始是数字就保持数字
        const originalType = typeof catGameData['1'].num
        catGameData['1'].num = originalType === 'number' ?
          parseInt(gameData.energy) : gameData.energy
      }

      if (catGameData['2']) {
        const originalType = typeof catGameData['2'].num
        catGameData['2'].num = originalType === 'number' ?
          parseInt(gameData.coins) : gameData.coins
      }

      if (catGameData['11']) {
        const originalType = typeof catGameData['11'].num
        catGameData['11'].num = originalType === 'number' ?
          parseInt(gameData.gems) : gameData.gems
      }

      // 添加或更新广告券数据
      if (catGameData['17']) {
        const originalType = typeof catGameData['17'].num
        catGameData['17'].num = originalType === 'number' ?
          parseInt(gameData.ads) : gameData.ads
      } else if (parseInt(gameData.ads) > 0) {
        // 只有当用户设置了广告券数值时才添加
        catGameData['17'] = {
          type: 1,
          id: 17,
          num: parseInt(gameData.ads)
        }
      }

      // 更新回原始数据
      originalData.MCatGame = JSON.stringify(catGameData)

      // 更新碎片数据 - 如果界面中有编辑过碎片数据才更新
      if (fragmentsTableData.value.length > 0) {
        originalData.bph_fragmentspack = gameData.fragments
      }

      // 更新TPDG-Record-MBack数据 - 保留原始数据
      if (gameData.recordMBack && gameData.recordMBack !== '{}') {
        // 解析TPDG-Record-MBack数据
        let recordMBackData = JSON.parse(gameData.recordMBack);

        // 处理已解锁关卡数据
        if (unlockedLevelsInput.value.trim()) {
          // 将输入框中的逗号分隔字符串转换为数字数组
          const levels = unlockedLevelsInput.value.split(',')
            .map(item => item.trim())
            .filter(item => item !== '')
            .map(item => parseInt(item))
            .filter(item => !isNaN(item));

          // 更新已解锁关卡数据
          recordMBackData.lvIdUnlock = levels;
        } else {
          // 如果输入框为空，设置为空数组
          recordMBackData.lvIdUnlock = [];
        }

        // 更新当前最高关卡数据
        recordMBackData.curPassLv = parseInt(currentMaxLevel.value) || 0;

        // 更新回原始数据
        originalData['TPDG-Record-MBack'] = JSON.stringify(recordMBackData);
      }

      // 构建保存请求
      const uploadParams = new URLSearchParams()
      uploadParams.append('app_name', currentGame.appName)
      uploadParams.append('version', currentGame.version)
      uploadParams.append('uuid', formData.playerId)
      uploadParams.append('d_key', currentGame.dKeys)
      uploadParams.append('d_data', JSON.stringify(originalData))

      // 发送请求
      const response = await fetch('https://game.zuiqiangyingyu.net/common/game-data/save', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Referer': 'https://servicewechat.com/wx588c1217403a3611/7/page-frame.html'
        },
        body: uploadParams
      })

      const result = await response.json()
      if (result.code === 0) {
        addHistory('上传数据成功')
        ElMessage.success('数据上传成功')
      } else {
        throw new Error(result.msg || '上传失败')
      }
    } else {
      throw new Error(getData.msg || '获取原始数据失败')
    }
  } catch (error) {
    console.error('上传数据失败:', error)
    addHistory('上传数据失败', false)
    ElMessage.error('上传数据失败: ' + error.message)
  } finally {
    loading.upload = false
  }
}

// 监听游戏切换，清除数据并更新默认UUID
watch(selectedGame, (newGameKey) => {
  // 更新默认UUID
  formData.playerId = gameConfigs[newGameKey].defaultUUID

  if (hasData.value) {
    // 清空已有数据
    handleClearData()
    ElMessage.info(`已切换到${gameConfigs[newGameKey].label}，请重新下载数据`)
  }
})

// 组件挂载时初始化
onMounted(() => {
  // 设置默认的碎片数据
  gameData.fragments = '{"1000":{"type":1,"id":1000,"num":48},"1001":{"type":1,"id":1001,"num":36},"1002":{"type":1,"id":1002,"num":42},"1004":{"type":1,"id":1004,"num":4},"1005":{"type":1,"id":1005,"num":1},"1006":{"type":1,"id":1006,"num":4},"1007":{"type":1,"id":1007,"num":5},"1008":{"type":1,"id":1008,"num":1}}'
  updateFragmentsTable()

  // 阻止ResizeObserver错误被记录到控制台
  const originalError = window.console.error;
  window.console.error = (...args) => {
    if (args[0]?.includes?.('ResizeObserver') ||
      args[0]?.message?.includes?.('ResizeObserver') ||
      args.some(arg => String(arg).includes('ResizeObserver'))) {
      // 不记录ResizeObserver相关错误
      return;
    }
    originalError.apply(window.console, args);
  };
})

// 添加碎片对话框
const addFragmentDialogVisible = ref(false)
const newFragment = ref({
  id: '',
  num: 1,
  type: 1
})

const showAddFragmentDialog = () => {
  addFragmentDialogVisible.value = true
}

const addFragment = () => {
  if (newFragment.value.id && newFragment.value.num && newFragment.value.type) {
    const newFragmentData = {
      id: parseInt(newFragment.value.id),
      num: parseInt(newFragment.value.num),
      type: parseInt(newFragment.value.type)
    }
    fragmentsTableData.value.push(newFragmentData)
    addHistory(`添加碎片 ${newFragmentData.id}`)
    ElMessage.success('碎片添加成功')
    addFragmentDialogVisible.value = false
  } else {
    ElMessage.warning('请填写完整的碎片信息')
  }
}

const removeFragment = (id) => {
  fragmentsTableData.value = fragmentsTableData.value.filter(item => item.id !== id)
  addHistory(`删除碎片 ${id}`)
  ElMessage.success('碎片删除成功')
}

// 解析recordMBack数据
const recordMBackParsed = computed(() => {
  try {
    if (!gameData.recordMBack) return null;
    return JSON.parse(gameData.recordMBack);
  } catch (error) {
    console.error('解析recordMBack数据失败:', error);
    return null;
  }
});

// 当recordMBack数据变化时，更新已解锁关卡输入框
watch(() => recordMBackParsed.value, (newVal) => {
  if (newVal && newVal.lvIdUnlock) {
    unlockedLevelsInput.value = newVal.lvIdUnlock.join(',');
  } else {
    unlockedLevelsInput.value = '';
  }

  // 更新当前最高关卡
  if (newVal && newVal.curPassLv !== undefined) {
    currentMaxLevel.value = Number(newVal.curPassLv);
  } else {
    currentMaxLevel.value = 0;
  }
}, { immediate: true });

// 辅助函数
const isEmpty = (value) => {
  return value === null || value === undefined || value === '' || (Array.isArray(value) && value.length === 0) || (typeof value === 'object' && Object.keys(value).length === 0)
}

// 解析已解锁关卡输入
const parsedLevels = computed(() => {
  if (!unlockedLevelsInput.value) return [];
  return unlockedLevelsInput.value.split(',')
    .map(item => item.trim())
    .filter(item => item !== '')
    .map(item => parseInt(item))
    .filter(item => !isNaN(item));
});

// 一键添加所有碎片
const addAllFragments = () => {
  // 碎片ID范围
  const fragmentIds = Array.from({ length: 17 }, (_, i) => 1000 + i);

  // 获取当前已存在的碎片ID
  const existingIds = fragmentsTableData.value.map(item => parseInt(item.id));

  // 添加缺失的碎片
  let addedCount = 0;

  fragmentIds.forEach(id => {
    // 如果碎片ID不存在，则添加
    if (!existingIds.includes(id)) {
      fragmentsTableData.value.push({
        id: id,
        num: 99999999,
        type: 1
      });
      addedCount++;
    }
  });

  if (addedCount > 0) {
    addHistory(`一键添加了${addedCount}个碎片`);
    ElMessage.success(`成功添加了${addedCount}个碎片，数量为99999999`);
  } else {
    ElMessage.info('所有碎片ID(1000-1016)已存在，无需添加');
  }
}
</script>

<style scoped>
.lazy-brother {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
  font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

.main-card {
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: none;
  overflow: hidden;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  padding-bottom: 20px;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px;
  background: linear-gradient(135deg, #42b983 0%, #2c3e50 100%);
  border-radius: 0;
  color: white;
  margin: -20px -20px 30px -20px;
  position: relative;
  overflow: hidden;
}

.header-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1NiIgaGVpZ2h0PSIxMDAiPgo8cmVjdCB3aWR0aD0iNTYiIGhlaWdodD0iMTAwIiBmaWxsPSIjZmZmZmZmMDUiPjwvcmVjdD4KPHBhdGggZD0iTTI4IDY2TDAgNTBMMCAxNkwyOCAwTDU2IDE2TDU2IDUwTDI4IDY2TDI4IDEwMCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmZmZmZmMTAiIHN0cm9rZS13aWR0aD0iMiI+PC9wYXRoPgo8cGF0aCBkPSJNMjggMEwyOCAzNEw1NiA1MEw1NiAxNkwyOCAwWiIgZmlsbD0iI2ZmZmZmZjA1IiBzdHJva2U9IiNmZmZmZmYxMCIgc3Ryb2tlLXdpZHRoPSIyIj48L3BhdGg+Cjwvc3ZnPg==');
  opacity: 0.2;
  z-index: 0;
}

.title {
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  letter-spacing: 0.5px;
  position: relative;
  z-index: 1;
}

.title .el-icon {
  margin-right: 15px;
  font-size: 30px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  padding: 10px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  animation: pulse 2s infinite;
  color: white;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  50% {
    transform: scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  }

  100% {
    transform: scale(1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.status-tag {
  font-size: 12px;
  border-radius: 20px;
  padding: 2px 16px;
  border: none;
  position: relative;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.section-title .el-icon {
  margin-right: 10px;
  font-size: 20px;
  color: #42b983;
}

.data-input-section {
  margin-bottom: 24px;
}

.card-section {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  border: 1px solid rgba(235, 238, 245, 0.5);
  margin-bottom: 30px;
}

.card-section:hover {
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.action-section {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  gap: 12px;
}

.action-button {
  flex: 1;
  height: 44px;
  font-weight: 500;
  letter-spacing: 0.5px;
  border-radius: 8px;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.game-data-section {
  margin-top: 30px;
  margin-bottom: 30px;
}

.operation-history {
  margin-top: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.divider {
  margin-top: 24px;
  margin-bottom: 16px;
}

.fragment-json-display {
  margin-top: 16px;
  opacity: 0.7;
  font-size: 12px;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 8px;
}

.fragment-json-display small {
  display: block;
  margin-bottom: 8px;
  color: #606266;
  font-weight: 500;
}

.fragment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.fragment-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.custom-tabs {
  border: none;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
}

.custom-form {
  padding: 16px;
}

.attribute-section {
  margin-bottom: 24px;
  background: #f9fafc;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid rgba(235, 238, 245, 0.8);
}

.attribute-header {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(235, 238, 245, 0.8);
}

.attribute-header .el-icon {
  margin-right: 8px;
  font-size: 20px;
}

.attribute-header span {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.custom-input {
  border-radius: 8px;
}

.custom-input :deep(.el-input__wrapper) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  padding: 4px 11px;
}

.custom-input :deep(.el-input__prefix) {
  margin-right: 8px;
}

.code-textarea :deep(.el-textarea__inner) {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
  font-size: 12px;
  background: #2c3e50;
  color: #e6e6e6;
  border-radius: 8px;
}

.fragment-name {
  display: flex;
  align-items: center;
}

.fragment-name .el-icon {
  margin-right: 8px;
  color: #67c23a;
}

.add-button {
  background: #42b983;
  border-color: #42b983;
  box-shadow: 0 2px 6px rgba(66, 185, 131, 0.4);
  border-radius: 8px;
  height: 36px;
}

.add-button:hover {
  background: #3aa876;
  border-color: #3aa876;
  box-shadow: 0 4px 12px rgba(66, 185, 131, 0.6);
}

.delete-button {
  background: #f56c6c;
  border-color: #f56c6c;
  padding: 6px 8px;
  width: auto;
}

.delete-button:hover {
  background: #e04c4c;
  border-color: #e04c4c;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  font-weight: 600;
  color: #2c3e50;
  padding: 12px 0;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-tabs__item.is-active) {
  color: #42b983;
  font-weight: 600;
}

:deep(.el-tabs__active-bar) {
  background-color: #42b983;
  height: 3px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number.is-controls-right .el-input-number__decrease),
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
  background-color: #f5f7fa;
  border-radius: 0;
}

:deep(.el-timeline-item__node--normal) {
  left: -2px;
  width: 12px;
  height: 12px;
  background-color: #42b983;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e4e7ed;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 20px;
}

:deep(.el-timeline-item__content) {
  font-size: 14px;
  color: #2c3e50;
  padding-bottom: 20px;
}

:deep(.el-dialog) {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

:deep(.el-dialog__header) {
  background: #42b983;
  color: white;
  padding: 20px;
}

:deep(.el-dialog__title) {
  color: white;
  font-weight: 600;
  font-size: 18px;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
  }

  .action-button {
    margin: 5px 0;
  }

  .header-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .status-tag {
    margin-top: 8px;
  }

  .record-data-summary {
    flex-direction: column;
    align-items: stretch;
  }

  .summary-item {
    width: 100%;
  }

  .detail-section :deep(.el-descriptions) {
    width: 100%;
    table-layout: fixed;
  }

  .detail-section :deep(.el-descriptions__body) {
    display: table;
    width: 100%;
  }

  .detail-section :deep(.el-descriptions__cell) {
    display: table-cell;
    overflow: hidden;
  }
}

.record-data-content {
  margin-top: 16px;
  margin-bottom: 16px;
}

:deep(.el-collapse-item__content) {
  padding-bottom: 16px;
}

:deep(.el-tab-pane) {
  padding-bottom: 16px;
}

:deep(.el-tabs__content) {
  padding-bottom: 16px;
}

.record-data-label {
  font-size: 14px;
  font-weight: 500;
  color: #606266;
  margin-bottom: 8px;
}

.record-data-info {
  margin-top: 20px;
  margin-bottom: 24px;
  background: #f5f7fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e4e7ed;
}

.record-data-detail {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
}

.detail-label {
  font-weight: 500;
  color: #606266;
  min-width: 100px;
  margin-right: 12px;
}

.record-data-summary {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px dashed rgba(235, 238, 245, 0.8);
}

.summary-item {
  flex: 1;
  min-width: 120px;
  display: flex;
  align-items: center;
  background: white;
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.summary-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.summary-icon {
  margin-right: 12px;
  border-radius: 8px;
  padding: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.summary-icon::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%);
  opacity: 0;
  transform: scale(0.5);
  transition: all 0.5s ease;
}

.summary-item:hover .summary-icon::after {
  opacity: 1;
  transform: scale(1);
}

.icon-energy {
  background: #f0f9eb;
  color: #67c23a;
}

.icon-coins {
  background: #fdf6ec;
  color: #e6a23c;
}

.icon-gems {
  background: #f0f2f5;
  color: #909399;
}

.icon-ads {
  background: #fef0f0;
  color: #f56c6c;
}

.icon-calendar {
  background: #ecf5ff;
  color: #409eff;
}

.icon-sweep {
  background: #f4f4f5;
  color: #909399;
}

.icon-sign {
  background: #e1f3d8;
  color: #67c23a;
}

.icon-level {
  background: #fde2e2;
  color: #f56c6c;
}

.summary-content {
  display: flex;
  flex-direction: column;
}

.summary-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.summary-label {
  font-size: 13px;
  color: #606266;
  margin-top: 4px;
}

.detail-section {
  margin-top: 24px;
}

.detail-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.detail-title::before {
  content: '';
  display: inline-block;
  width: 4px;
  height: 18px;
  background: #42b983;
  margin-right: 8px;
  border-radius: 2px;
}

.level-tag {
  margin: 4px;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.no-data-tip {
  text-align: center;
  padding: 30px 20px;
  background: #f9fafc;
  border-radius: 8px;
  border: 1px dashed #e4e7ed;
}

.raw-data-collapse {
  margin-top: 24px;
  padding-top: 8px;
}

.el-descriptions-cell {
  overflow: hidden;
}

:deep(.el-descriptions__cell) {
  overflow: hidden;
}

:deep(.el-descriptions__content) {
  overflow: hidden;
}

.level-edit-container {
  display: flex;
  align-items: center;
}

.level-edit-input {
  width: 100%;
  margin-right: 8px;
}

.help-icon {
  color: #409eff;
  margin-left: 5px;
  cursor: help;
}

.levels-preview {
  margin-top: 16px;
}

.preview-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.level-input-number {
  width: 100%;
}

.level-input-number :deep(.el-input-number__decrease),
.level-input-number :deep(.el-input-number__increase) {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
}

.level-input-number :deep(.el-input__wrapper) {
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.icon-title {
  color: #42b983;
}

.icon-document {
  color: #409eff;
}

.icon-resource {
  color: #e6a23c;
}

.icon-fragment {
  color: #67c23a;
}

.icon-history {
  color: #f56c6c;
}

.detail-section :deep(.el-descriptions__label) {
  position: relative;
}

.level-edit-container .help-icon {
  cursor: pointer;
  color: #909399;
  font-size: 16px;
  transition: all 0.3s ease;
}

.level-edit-container .help-icon:hover {
  color: #409eff;
  transform: scale(1.1);
}

.button-group {
  display: flex;
  gap: 10px;
}

.add-all-button {
  background: #67c23a;
  border-color: #67c23a;
  box-shadow: 0 2px 6px rgba(103, 194, 58, 0.4);
  border-radius: 8px;
  height: 36px;
}

.add-all-button:hover {
  background: #5daf34;
  border-color: #5daf34;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.6);
}

.w-100 {
  width: 100%;
}
</style>