<template>
  <div class="who-beat-packages">
    <!-- 背景装饰 -->
    <div class="bg-decoration">
      <div class="floating-icon icon-1"><el-icon><Trophy /></el-icon></div>
      <div class="floating-icon icon-2"><el-icon><Star /></el-icon></div>
      <div class="floating-icon icon-3"><el-icon><GoldMedal /></el-icon></div>
    </div>

    <!-- 主标题区域 -->
    <div class="header-section">
      <div class="game-title">
        <el-icon class="title-icon"><Trophy /></el-icon>
        <span class="title-text">看谁能打过-根本打不过</span>
      </div>
      <div class="subtitle">超值套餐 · 限时特惠</div>
      <div class="support-badges">
        <div class="badge">
          <el-icon><Check /></el-icon>
          <span>微信小程序</span>
        </div>
        <div class="badge">
          <el-icon><Timer /></el-icon>
          <span>秒冲到账</span>
        </div>
      </div>
    </div>

    <!-- 套餐展示区域 -->
    <div class="packages-section">
      <!-- 套餐一 -->
      <div class="package-card basic-package">
        <div class="package-header">
          <div class="package-icon">
            <el-icon><Money /></el-icon>
          </div>
          <div class="package-title">套餐一</div>
          <div class="package-price">
            <span class="price">¥5</span>
          </div>
        </div>
        <div class="package-content">
          <div class="feature-item">
            <el-icon class="feature-icon"><GoldMedal /></el-icon>
            <span>30亿钻石</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Lightning /></el-icon>
            <span>30亿体力</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Coin /></el-icon>
            <span>30亿金币</span>
          </div>
        </div>
      </div>

      <!-- 套餐二 -->
      <div class="package-card premium-package">
        <div class="hot-badge">限时特惠</div>
        <div class="package-header">
          <div class="package-icon premium">
            <el-icon><Star /></el-icon>
          </div>
          <div class="package-title">套餐二</div>
          <div class="package-price">
            <span class="price">¥5</span>
          </div>
        </div>
        <div class="package-content">
          <div class="feature-item">
            <el-icon class="feature-icon"><GoldMedal /></el-icon>
            <span>无限钻石</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Lightning /></el-icon>
            <span>无限体力</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Coin /></el-icon>
            <span>无限金币</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Box /></el-icon>
            <span>全部装备</span>
          </div>
          <div class="feature-item">
            <el-icon class="feature-icon"><Present /></el-icon>
            <span>无限碎片</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部提示 -->
    <div class="footer-tip">
      <div class="tip-text">
        <el-icon><InfoFilled /></el-icon>
        <span>安全充值 · 官方认证 · 售后保障</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Star,
  Money,
  Lightning,
  Box,
  Present,
  Check,
  Timer,
  Trophy,
  Coin,
  GoldMedal,
  InfoFilled
} from '@element-plus/icons-vue'
</script>

<style scoped>
.who-beat-packages {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 15px;
  box-sizing: border-box;
}

/* 背景装饰 */
.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 0;
}

.floating-icon {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  font-size: 60px;
  animation: float 6s ease-in-out infinite;
}

.icon-1 {
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.icon-2 {
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.icon-3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(10deg); }
}

/* 主标题区域 */
.header-section {
  text-align: center;
  margin-bottom: 25px;
  z-index: 1;
  position: relative;
}

.game-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 8px;
}

.title-icon {
  font-size: 32px;
  color: #FFD700;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5));
}

.title-text {
  font-size: 28px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  margin-bottom: 15px;
  letter-spacing: 1px;
}

.support-badges {
  display: flex;
  justify-content: center;
  gap: 12px;
}

.badge {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  padding: 6px 12px;
  display: flex;
  align-items: center;
  gap: 5px;
  color: white;
  font-size: 12px;
}

/* 套餐展示区域 */
.packages-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
  z-index: 1;
  position: relative;
}

.package-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  transition: all 0.3s ease;
}

.package-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.basic-package {
  border-left: 4px solid #409EFF;
}

.premium-package {
  border-left: 4px solid #E6A23C;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(255, 248, 220, 0.95) 100%);
}

.hot-badge {
  position: absolute;
  top: -8px;
  right: 15px;
  background: linear-gradient(45deg, #FF6B6B, #FF8E53);
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.package-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 15px;
}

.package-icon {
  width: 50px;
  height: 50px;
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #409EFF, #67C23A);
  color: white;
  font-size: 24px;
  box-shadow: 0 4px 15px rgba(64, 158, 255, 0.3);
}

.package-icon.premium {
  background: linear-gradient(135deg, #E6A23C, #F56C6C);
  box-shadow: 0 4px 15px rgba(230, 162, 60, 0.3);
}

.package-title {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
  flex: 1;
  text-align: center;
}

.package-price {
  text-align: right;
}

.price {
  font-size: 24px;
  font-weight: bold;
  color: #E6A23C;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.package-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 10px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 12px;
  color: #303133;
  font-size: 13px;
  font-weight: 500;
}

.feature-icon {
  color: #409EFF;
  font-size: 16px;
}

/* 底部提示 */
.footer-tip {
  margin-top: auto;
  padding-top: 20px;
  z-index: 1;
  position: relative;
}

.tip-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
  text-align: center;
}

/* 移动端适配 */
@media (max-width: 480px) {
  .who-beat-packages {
    padding: 12px;
  }

  .title-text {
    font-size: 24px;
  }

  .package-card {
    padding: 16px;
  }

  .package-content {
    grid-template-columns: 1fr 1fr;
  }

  .feature-item {
    font-size: 12px;
    padding: 6px 10px;
  }
}

/* 超小屏幕适配 */
@media (max-width: 360px) {
  .support-badges {
    flex-direction: column;
    gap: 8px;
  }

  .package-content {
    grid-template-columns: 1fr;
  }
}
</style>
