<template>
  <div class="three-kingdoms-container">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <h2 class="title">三国争霸自走棋</h2>
          <el-tag type="success" effect="dark">数据管理</el-tag>
        </div>
      </template>

      <div class="content">
        <div class="input-section">
          <el-form :model="form" label-position="top">
            <el-form-item label="玩家 OpenID" required>
              <el-input v-model="form.openId" placeholder="请输入玩家OpenID" clearable :prefix-icon="Avatar"
                class="custom-input" />
            </el-form-item>
          </el-form>
        </div>

        <div class="action-section">
          <div class="buttons-wrapper">
            <el-button type="primary" :icon="Download" class="action-button" :disabled="!form.openId"
              @click="handleDownload" :loading="isLoading">
              下载数据
            </el-button>

            <el-button type="success" :icon="Upload" class="action-button" :disabled="!form.openId || !gameData"
              @click="handleUpload">
              上传数据
            </el-button>

            <el-tooltip effect="dark" content="套餐一内容：21亿元宝" placement="top" popper-class="custom-tooltip">
              <el-button type="warning" class="action-button" :disabled="!form.openId || !gameData"
                @click="handlePackageOne">
                套餐一
              </el-button>
            </el-tooltip>

            <el-tooltip effect="dark" content="套餐二内容：21亿功勋" placement="top" popper-class="custom-tooltip">
              <el-button type="warning" class="action-button" :disabled="!form.openId || !gameData"
                @click="handlePackageTwo">
                套餐二
              </el-button>
            </el-tooltip>

            <el-tooltip effect="dark" content="套餐三内容：21亿换一批" placement="top" popper-class="custom-tooltip">
              <el-button type="warning" class="action-button" :disabled="!form.openId || !gameData"
                @click="handlePackageThree">
                套餐三
              </el-button>
            </el-tooltip>

            <el-tooltip effect="dark" placement="top" popper-class="custom-tooltip">
              <template #content>
                <div class="tooltip-content">
                  <div class="tooltip-title">终极套餐内容：</div>
                  <ul class="tooltip-list">
                    <li>✦ 21亿元宝</li>
                    <li>✦ 21亿功勋</li>
                    <li>✦ 21亿换一批</li>
                    <li>✦ 1亿功勋点</li>
                    <li>✦ 4款技能书各1亿</li>
                    <li>✦ VIP12级</li>
                    <li>✦ 解锁8个英雄(1001-1008)</li>
                  </ul>
                </div>
              </template>
              <el-button type="danger" class="action-button" :disabled="!form.openId || !gameData"
                @click="handleUltimatePackage">
                终极套餐
              </el-button>
            </el-tooltip>
          </div>
        </div>

        <div class="status-section" v-if="actionStatus">
          <ElAlert :title="actionStatus.message" :type="actionStatus.type" show-icon :closable="false" />
          <div v-if="gameData" class="view-raw-data">
            <el-button type="info" size="small" @click="displayRawResponse">
              查看原始响应数据
            </el-button>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 游戏数据展示区 -->
    <el-card class="data-card" v-if="gameData">
      <template #header>
        <div class="card-header">
          <h3>游戏数据</h3>
          <el-button type="primary" size="small" @click="saveDataChanges" :disabled="!hasDataChanges">
            保存修改
          </el-button>
        </div>
      </template>

      <el-tabs v-model="activeTab" type="card" class="data-tabs">
        <!-- 主角数据标签页 -->
        <el-tab-pane label="主角数据" name="lead">
          <div class="data-section stabilize-height">
            <div class="section-header">
              <h4>主角信息</h4>
              <el-button type="primary" size="small" @click="uploadSingleModule('LeadModule')" :disabled="!gameData"
                :icon="Upload">
                上传主角数据
              </el-button>
            </div>
            <div v-if="leadData" class="data-form">
              <el-form label-position="left" label-width="120px" class="edit-form">
                <el-form-item label="主角ID">
                  <el-input v-model="leadData.id" type="number" class="short-input" />
                </el-form-item>
                <el-form-item label="拥有的主角">
                  <div class="own-ids-container">
                    <el-input v-model="ownIdsStr" @change="updateOwnIds" placeholder="使用逗号分隔多个ID" class="long-input" />
                    <el-button type="primary" size="small" @click="addMissingCharacters">
                      一键添加缺失角色
                    </el-button>
                  </div>
                </el-form-item>
                <el-form-item label="激活次数">
                  <div v-for="(activate, index) in leadData.activate_times" :key="index" class="activate-item">
                    <div class="activate-row">
                      <el-input-number v-model="activate.id" :min="1000" :max="9999" size="small" />
                      <span class="activate-label">激活次数：</span>
                      <el-input-number v-model="activate.times" :min="0" size="small" />
                      <el-button type="danger" size="small" @click="removeActivateTime(index)">删除</el-button>
                    </div>
                  </div>
                  <el-button type="primary" size="small" @click="addActivateTime">添加激活记录</el-button>
                </el-form-item>
              </el-form>
            </div>
          </div>
        </el-tab-pane>

        <!-- 故事模块标签页 -->
        <el-tab-pane label="故事数据" name="story">
          <div class="data-section stabilize-height">
            <div v-if="storyData" class="module-container">
              <ElCollapse v-model="storyActiveNames">
                <ElCollapseItem title="主角数据" name="lead_data">
                  <el-form label-position="left" label-width="120px" class="edit-form">
                    <el-form-item label="配置ID">
                      <el-input v-model.number="storyData.lead_data.cfg_id" type="number" />
                    </el-form-item>
                    <el-form-item label="血量">
                      <el-input-number v-model="storyData.lead_data.hp" :min="0" />
                    </el-form-item>
                    <el-form-item label="最大血量">
                      <el-input-number v-model="storyData.lead_data.max_hp" :min="0" />
                    </el-form-item>
                    <el-form-item label="金币">
                      <el-input-number v-model="storyData.lead_data.gold" :min="0" :step="100" />
                    </el-form-item>
                    <el-form-item label="人口">
                      <el-input-number v-model="storyData.lead_data.people" :min="0" />
                    </el-form-item>
                    <el-form-item label="等级">
                      <el-input-number v-model="storyData.lead_data.level" :min="1" />
                    </el-form-item>
                    <el-form-item label="经验">
                      <el-input-number v-model="storyData.lead_data.exp" :min="0" :step="100" />
                    </el-form-item>
                    <el-form-item label="复活次数">
                      <el-input-number v-model="storyData.lead_data.revive_times" :min="0" />
                    </el-form-item>
                  </el-form>
                </ElCollapseItem>

                <ElCollapseItem title="技能列表" name="skills">
                  <div v-for="(skill, index) in storyData.lead_data.skills" :key="index" class="skill-item">
                    <el-form label-position="left" label-width="80px" class="edit-form">
                      <el-form-item label="技能ID">
                        <el-input v-model.number="skill.id" type="number" />
                      </el-form-item>
                      <el-form-item label="等级">
                        <el-input-number v-model="skill.level" :min="1" />
                      </el-form-item>
                    </el-form>
                  </div>
                </ElCollapseItem>
              </ElCollapse>
            </div>
          </div>
        </el-tab-pane>

        <!-- 英雄数据标签页 -->
        <el-tab-pane label="英雄数据" name="hero">
          <div class="data-section stabilize-height" v-if="storyData && storyData.hero_data">
            <!-- 替换动态表格为预渲染容器 -->
            <div class="static-table-container">
              <div class="table-header">
                <div class="th uuid-col">英雄UUID</div>
                <div class="th id-col">配置ID</div>
                <div class="th hp-col">生命值</div>
                <div class="th status-col">参战状态</div>
                <div class="th position-col">位置</div>
                <div class="th kill-col">击杀数</div>
              </div>
              <div class="table-body">
                <div v-for="hero in storyData.hero_data.hero_list" :key="hero.uuid" class="table-row">
                  <div class="td uuid-col">{{ hero.uuid }}</div>
                  <div class="td id-col">
                    <el-input-number v-model="hero.cfg_id" :min="1000" :max="9999" size="small" />
                  </div>
                  <div class="td hp-col">
                    <el-input-number v-model="hero.hp" :min="0" size="small" />
                  </div>
                  <div class="td status-col">
                    <el-switch v-model="hero.is_war" active-text="参战" inactive-text="空闲" />
                  </div>
                  <div class="td position-col">
                    <div v-if="hero.in_cell">
                      X: <el-input-number v-model="hero.in_cell.x" :min="0" :max="10" size="small"
                        controls-position="right" />
                      Y: <el-input-number v-model="hero.in_cell.y" :min="0" :max="10" size="small"
                        controls-position="right" />
                    </div>
                    <div v-else>未放置</div>
                  </div>
                  <div class="td kill-col">
                    <div v-if="'kill_1' in hero">
                      小兵: <el-input-number v-model="hero.kill_1" :min="0" size="small" controls-position="right" />
                    </div>
                    <div v-if="'kill_2' in hero">
                      精英: <el-input-number v-model="hero.kill_2" :min="0" size="small" controls-position="right" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 章节数据标签页 -->
        <el-tab-pane label="章节数据" name="chapter">
          <div class="data-section stabilize-height" v-if="chpData">
            <div class="section-header">
              <h4>章节配置</h4>
              <el-button type="primary" size="small" @click="uploadSingleModule('ChpModule')" :disabled="!gameData"
                :icon="Upload">
                上传章节数据
              </el-button>
            </div>
            <el-form label-position="left" label-width="150px" class="edit-form">
              <el-form-item label="当前章节ID">
                <el-input-number v-model="chpData.record['1']" :min="0" />
              </el-form-item>
              <el-form-item label="已通过章节ID">
                <el-input-number v-model="chpData.pass_chp_id" :min="0" />
              </el-form-item>
              <el-form-item label="是否首次">
                <el-switch v-model="chpData.is_first" />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 背包物品标签页 -->
        <el-tab-pane label="背包物品" name="knapsack">
          <div class="data-section stabilize-height" v-if="knapsackData">
            <div class="section-header">
              <h4>物品列表</h4>
              <div class="button-group">
                <el-button type="primary" size="small" @click="uploadSingleModule('KnapsackModule')"
                  :disabled="!gameData" :icon="Upload">
                  上传背包数据
                </el-button>
                <el-button type="success" size="small" @click="showAddItemDialog">
                  添加物品
                </el-button>
              </div>
            </div>
            <div class="static-table-container">
              <div class="table-header">
                <div class="th uuid-col">物品UUID</div>
                <div class="th id-col">物品ID</div>
                <div class="th name-col">物品名称</div>
                <div class="th count-col">数量</div>
                <div class="th action-col">操作</div>
              </div>
              <div class="table-body">
                <div v-for="item in knapsackData.list" :key="item.uuid" class="table-row">
                  <div class="td uuid-col">{{ item.uuid }}</div>
                  <div class="td id-col">{{ item.id }}</div>
                  <div class="td name-col">
                    <el-tag :type="getItemTagType(item.id)">
                      {{ getItemName(item.id) }}
                    </el-tag>
                  </div>
                  <div class="td count-col">
                    <el-input-number v-model="item.count" :min="0" :step="100" size="small" />
                  </div>
                  <div class="td action-col">
                    <div class="button-group">
                      <el-button type="primary" size="small" @click="modifyItemCount(item, 1000)">+1000</el-button>
                      <el-button type="danger" size="small" @click="modifyItemCount(item, -1000)"
                        :disabled="item.count < 1000">-1000</el-button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 体力数据标签页 -->
        <el-tab-pane label="体力数据" name="stamina">
          <div class="data-section stabilize-height" v-if="staminaData">
            <div class="section-header">
              <h4>体力配置</h4>
              <el-button type="primary" size="small" @click="uploadSingleModule('StaminaModule')" :disabled="!gameData"
                :icon="Upload">
                上传体力数据
              </el-button>
            </div>
            <el-form label-position="left" label-width="150px" class="edit-form">
              <el-form-item label="当前体力">
                <el-input-number v-model="staminaData.stamina" :min="0" />
              </el-form-item>
              <el-form-item label="剩余恢复次数">
                <el-input-number v-model="staminaData.remain_times" :min="0" :max="10" />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 任务/研究/图鉴标签页 -->
        <el-tab-pane label="其他模块" name="other">
          <div class="data-section stabilize-height">
            <ElCollapse v-model="otherActiveNames">
              <ElCollapseItem title="任务模块" name="task">
                <div v-if="taskData">
                  <el-form label-position="left" label-width="150px" class="edit-form">
                    <el-form-item label="任务ID">
                      <el-input-number v-model="taskData.id" :min="1" />
                    </el-form-item>
                    <el-form-item label="任务状态">
                      <el-select v-model="taskData.state">
                        <el-option :value="1" label="未接受" />
                        <el-option :value="2" label="进行中" />
                        <el-option :value="3" label="已完成" />
                      </el-select>
                    </el-form-item>
                    <el-form-item label="任务进度">
                      <el-input-number v-model="taskData.progress" :min="0" />
                    </el-form-item>
                  </el-form>
                </div>
              </ElCollapseItem>

              <ElCollapseItem title="图鉴模块" name="book">
                <div v-if="bookData">
                  <div class="static-table-container">
                    <div class="table-header">
                      <div class="th hero-id-col">英雄ID</div>
                      <div class="th hero-state-col">状态</div>
                    </div>
                    <div class="table-body">
                      <div v-for="hero in bookData.heros" :key="hero.id" class="table-row">
                        <div class="td hero-id-col">{{ hero.id }}</div>
                        <div class="td hero-state-col">
                          <el-select v-model="hero.state" size="small">
                            <el-option :value="1" label="未解锁" />
                            <el-option :value="2" label="已看到" />
                            <el-option :value="3" label="已解锁" />
                          </el-select>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ElCollapseItem>
            </ElCollapse>
          </div>
        </el-tab-pane>

        <!-- VIP数据标签页 -->
        <el-tab-pane label="VIP数据" name="vip">
          <div class="data-section stabilize-height" v-if="vipData">
            <div class="section-header">
              <h4>VIP配置</h4>
              <el-button type="primary" size="small" @click="uploadSingleModule('VipModule')" :disabled="!gameData"
                :icon="Upload">
                上传VIP数据
              </el-button>
            </div>
            <el-form label-position="left" label-width="150px" class="edit-form">
              <el-form-item label="当前VIP等级">
                <el-input-number v-model="vipData.level" :min="0" />
              </el-form-item>
              <el-form-item label="VIP经验">
                <el-input-number v-model="vipData.exp" :min="0" />
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <!-- 原始JSON数据标签页 -->
        <el-tab-pane label="原始数据" name="json">
          <div class="data-section stabilize-height">
            <div class="section-header">
              <h4>原始JSON数据</h4>
              <el-tag type="warning">修改此处会影响所有模块数据</el-tag>
            </div>
            <div class="data-parse-guide">
              <ElAlert title="数据异常提示" type="info" description="原始API返回的某些字段可能包含null或无效值，系统已自动修复。编辑原始JSON时请小心操作。"
                show-icon :closable="false" class="parse-alert" />
            </div>
            <el-input type="textarea" v-model="rawJsonStr" :rows="20" placeholder="原始JSON数据" @change="updateRawJson" />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <el-card class="info-card" v-if="!gameData">
      <template #header>
        <div class="card-header">
          <h3>数据说明</h3>
        </div>
      </template>
      <div class="info-content">
        <el-timeline>
          <el-timeline-item timestamp="下载数据" type="primary" size="large">
            <p>获取指定玩家的游戏数据，包括角色信息、装备、战绩等</p>
          </el-timeline-item>
          <el-timeline-item timestamp="上传数据" type="success" size="large">
            <p>将修改后的数据上传到服务器，更新玩家游戏状态</p>
          </el-timeline-item>
          <el-timeline-item timestamp="注意事项" type="warning" size="large">
            <p>请确保OpenID正确无误，错误的OpenID可能导致数据混乱</p>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
  </div>

  <!-- 添加物品对话框 -->
  <el-dialog v-model="addItemDialogVisible" title="添加物品" width="30%" :close-on-click-modal="false">
    <el-form :model="newItem" label-width="80px">
      <el-form-item label="物品ID" required>
        <el-input v-model="newItem.id" type="number" placeholder="请输入物品ID"></el-input>
      </el-form-item>
      <el-form-item label="物品名称">
        <el-input v-model="newItem.name" placeholder="请输入物品名称（可选）"></el-input>
      </el-form-item>
      <el-form-item label="数量">
        <el-input-number v-model="newItem.count" :min="1" :step="10"></el-input-number>
      </el-form-item>
      <el-form-item label="标签颜色">
        <el-select v-model="newItem.tagType" placeholder="选择标签颜色">
          <el-option label="绿色" value="success" />
          <el-option label="蓝色" value="info" />
          <el-option label="黄色" value="warning" />
          <el-option label="红色" value="danger" />
          <el-option label="灰色" value="" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="addItemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addItem">确认添加</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElAlert, ElCollapse, ElCollapseItem } from 'element-plus'
import { Download, Upload, Avatar, Plus } from '@element-plus/icons-vue'
import axios from 'axios'
import CryptoJS from 'crypto-js'

// 游戏密钥
const KEY_GAME = '0a980xjfap80szrz'

// 加密函数
function encrypt(data, key) {
  try {
    // 如果输入是对象，转换为JSON字符串
    const dataStr = typeof data === 'object' ? JSON.stringify(data) : data.toString()

    // 解析密钥
    const keyParsed = CryptoJS.enc.Utf8.parse(key)

    // 加密过程
    const encrypted = CryptoJS.AES.encrypt(dataStr, keyParsed, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    })

    // 返回十六进制格式的密文
    return encrypted.ciphertext.toString(CryptoJS.enc.Hex)
  } catch (error) {
    console.error('加密失败:', error.message)
    return null
  }
}

// 解密函数
function decrypt(encryptedHex, key) {
  try {
    // 解析密钥
    const keyParsed = CryptoJS.enc.Utf8.parse(key)

    // 解密过程
    const decrypted = CryptoJS.AES.decrypt({
      ciphertext: CryptoJS.enc.Hex.parse(encryptedHex)
    }, keyParsed, {
      mode: CryptoJS.mode.ECB,
      padding: CryptoJS.pad.Pkcs7
    })

    // 转换为UTF8字符串
    const decryptedStr = decrypted.toString(CryptoJS.enc.Utf8)

    // 尝试解析JSON
    return JSON.parse(decryptedStr)
  } catch (error) {
    console.error('解密失败:', error.message)
    return null
  }
}

const form = reactive({
  openId: 'oASBs7WIzu8lknIGWBe-zFwBxvus'
})

const isLoading = ref(false)
const actionStatus = ref(null)
const gameData = ref(null)
const rawJsonStr = ref('')
const activeTab = ref('lead')
const storyActiveNames = ref(['lead_data', 'skills'])
const otherActiveNames = ref(['task', 'book'])
const hasDataChanges = ref(false)

// 添加物品相关的状态
const addItemDialogVisible = ref(false)
const newItem = reactive({
  id: '',
  name: '',
  count: 1,
  tagType: 'info'
})

// 自定义物品名称映射
const customItemNames = ref({})
const customItemTags = ref({})

// 模块数据计算属性
const leadData = computed(() => {
  if (!gameData.value) return null
  try {
    return JSON.parse(gameData.value.LeadModule)
  } catch (e) {
    return null
  }
})

const storyData = computed(() => {
  if (!gameData.value) return null
  try {
    const data = JSON.parse(gameData.value.StoryModule)

    // 确保必要的对象结构存在
    if (!data.lead_data) data.lead_data = {}
    if (!data.hero_data) data.hero_data = {}
    if (!data.chp_data) data.chp_data = {}

    // 确保各子对象中的属性也具有默认值
    if (!data.lead_data.skills) data.lead_data.skills = []
    if (!data.lead_data.events) data.lead_data.events = []
    if (!data.lead_data.special_effects) data.lead_data.special_effects = []

    if (!data.hero_data.hero_list) data.hero_data.hero_list = []
    if (!data.hero_data.free_hero_uuids) data.hero_data.free_hero_uuids = []
    if (!data.hero_data.equip_list) data.hero_data.equip_list = []

    if (data.chp_data.cur_chp_id === null) data.chp_data.cur_chp_id = 1
    if (!data.chp_data.cur_list) data.chp_data.cur_list = []
    if (!data.chp_data.event_list) data.chp_data.event_list = []

    return data
  } catch (e) {
    console.error('解析StoryModule失败:', e)
    return {
      lead_data: { skills: [], events: [], special_effects: [] },
      hero_data: { hero_list: [], free_hero_uuids: [], equip_list: [] },
      chp_data: { cur_chp_id: 1, lit_chp_id: 1, cur_id: 0, cur_list: [], event_list: [] }
    }
  }
})

const chpData = computed(() => {
  if (!gameData.value) return null
  try {
    return JSON.parse(gameData.value.ChpModule)
  } catch (e) {
    return null
  }
})

const taskData = computed(() => {
  if (!gameData.value) return null
  try {
    return JSON.parse(gameData.value.TaskModule)
  } catch (e) {
    return null
  }
})

const bookData = computed(() => {
  if (!gameData.value) return null
  try {
    return JSON.parse(gameData.value.BookModule)
  } catch (e) {
    return null
  }
})

const knapsackData = computed(() => {
  if (!gameData.value) return null
  try {
    return JSON.parse(gameData.value.KnapsackModule)
  } catch (e) {
    return null
  }
})

const staminaData = computed(() => {
  if (!gameData.value) return null
  try {
    return JSON.parse(gameData.value.StaminaModule)
  } catch (e) {
    return null
  }
})

const vipData = computed(() => {
  if (!gameData.value) return null
  try {
    return JSON.parse(gameData.value.VipModule)
  } catch (e) {
    return null
  }
})

const ownIdsStr = computed({
  get: () => {
    if (!leadData.value || !leadData.value.own_ids) return ''
    return leadData.value.own_ids.join(',')
  },
  set: (val) => {
    // 在updateOwnIds处理实际更新
  }
})

// 方法
const handleDownload = async () => {
  if (!form.openId) {
    showMessage('请先输入玩家OpenID', 'warning')
    return
  }

  isLoading.value = true
  actionStatus.value = {
    message: `正在获取玩家 ${form.openId} 的数据...`,
    type: 'info'
  }

  try {
    const params = new URLSearchParams()
    params.append('game_id', '1448')
    params.append('open_id', form.openId)
    params.append('type', 'all')

    const response = await axios.post('https://api.zqygame.com/api/game/get_data', params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
      }
    })

    if (response.data && response.data.code === 0) {
      // 对数据进行预处理
      const processedData = {}

      // 处理每个模块的数据
      for (const [key, value] of Object.entries(response.data.data)) {
        try {
          // 解析JSON数据
          const parsedData = JSON.parse(value)

          // 对特定模块进行数据修复
          if (key === 'StoryModule' && parsedData.chp_data && parsedData.chp_data.cur_chp_id === null) {
            parsedData.chp_data.cur_chp_id = 1 // 将null值替换为默认值1
          }

          // 保存处理后的JSON字符串
          processedData[key] = JSON.stringify(parsedData)
        } catch (e) {
          // 如果解析失败，保留原始字符串
          processedData[key] = value
          console.warn(`无法解析模块 ${key} 的数据:`, e)
        }
      }

      // 更新游戏数据
      gameData.value = processedData
      rawJsonStr.value = JSON.stringify({ code: 0, data: processedData, message: "successful" }, null, 2)

      actionStatus.value = {
        message: `✅ 玩家 ${form.openId} 的数据已成功下载并处理`,
        type: 'success'
      }

      // 默认显示主角数据标签页
      activeTab.value = 'lead'
      hasDataChanges.value = false
    } else {
      actionStatus.value = {
        message: `❌ 下载失败: ${response.data?.message || '未知错误'}`,
        type: 'error'
      }
    }
  } catch (error) {
    console.error('下载数据出错:', error)
    actionStatus.value = {
      message: `❌ 下载出错: ${error.message || '网络错误'}`,
      type: 'error'
    }
  } finally {
    isLoading.value = false
  }
}

const handleUpload = async () => {
  if (!form.openId || !gameData.value) {
    ElMessage.warning('请先下载玩家数据')
    return
  }

  isLoading.value = true
  actionStatus.value = {
    message: `正在上传玩家 ${form.openId} 的数据...`,
    type: 'info'
  }

  try {
    // 收集所有更新的模块数据
    const moduleUpdates = []
    const moduleNames = [
      'LeadModule', 'StoryModule', 'ChpModule', 'TaskModule',
      'BookModule', 'KnapsackModule', 'StaminaModule', 'VipModule'
    ]

    // 检查每个模块是否需要更新并构建请求数据
    for (const moduleName of moduleNames) {
      const moduleData = gameData.value[moduleName]
      if (moduleData) {
        const requestData = {
          game_id: '1448',
          open_id: form.openId,
          key: moduleName,
          value: moduleData
        }

        // 加密数据
        const encryptedData = encrypt(requestData, KEY_GAME)
        if (encryptedData) {
          moduleUpdates.push({ moduleName, encryptedData })
        }
      }
    }

    // 逐个上传每个模块数据
    const uploadResults = []
    for (const { moduleName, encryptedData } of moduleUpdates) {
      try {
        const formData = new URLSearchParams()
        formData.append('data', encryptedData)

        const response = await axios.post('https://api.zqygame.com/api/game/save_data', formData, {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
            'xweb_xhr': '1',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
            'Referer': 'https://servicewechat.com/wxa31df10decf52417/7/page-frame.html'
          }
        })

        uploadResults.push({
          moduleName,
          success: response.data && response.data.code === 0,
          message: response.data ? response.data.message : '未知错误'
        })
      } catch (error) {
        uploadResults.push({
          moduleName,
          success: false,
          message: error.message || '网络错误'
        })
      }
    }

    // 统计上传结果
    const successCount = uploadResults.filter(r => r.success).length
    const totalCount = uploadResults.length

    if (successCount === totalCount) {
      actionStatus.value = {
        message: `成功上传所有数据模块 (${successCount}/${totalCount})`,
        type: 'success'
      }
      hasDataChanges.value = false
    } else if (successCount > 0) {
      actionStatus.value = {
        message: `部分数据模块上传成功 (${successCount}/${totalCount})`,
        type: 'warning'
      }
    } else {
      actionStatus.value = {
        message: `上传失败: 所有数据模块均上传失败`,
        type: 'error'
      }
    }
  } catch (error) {
    console.error('上传数据出错:', error)
    actionStatus.value = {
      message: `上传出错: ${error.message || '未知错误'}`,
      type: 'error'
    }
  } finally {
    isLoading.value = false
  }
}

const updateOwnIds = () => {
  if (!leadData.value) return

  try {
    const ids = ownIdsStr.value.split(',').map(id => parseInt(id.trim()))
    leadData.value.own_ids = ids.filter(id => !isNaN(id))
    hasDataChanges.value = true
  } catch (e) {
    ElMessage.error('格式不正确，请使用逗号分隔的数字')
  }
}

const updateRawJson = () => {
  try {
    const parsed = JSON.parse(rawJsonStr.value)
    if (parsed && parsed.data) {
      // 对数据进行处理，类似handleDownload中的逻辑
      const processedData = {}

      // 处理每个模块的数据
      for (const [key, value] of Object.entries(parsed.data)) {
        try {
          // 先尝试解析JSON字符串
          const parsedValue = typeof value === 'string' ? JSON.parse(value) : value

          // 对特定模块进行数据修复
          if (key === 'StoryModule' && parsedValue.chp_data && parsedValue.chp_data.cur_chp_id === null) {
            parsedValue.chp_data.cur_chp_id = 1 // 将null值替换为默认值1
          }

          // 将处理后的数据转回字符串
          processedData[key] = typeof value === 'string' ?
            JSON.stringify(parsedValue) : parsedValue
        } catch (e) {
          // 如果解析失败，保留原始值
          processedData[key] = value
          console.warn(`无法处理模块 ${key} 的数据:`, e)
        }
      }

      gameData.value = processedData
      hasDataChanges.value = true
      showMessage('✅ JSON数据已更新并自动修复', 'success')
    }
  } catch (e) {
    console.error('JSON格式错误:', e)
    showMessage('❌ JSON格式不正确，请检查格式', 'error')
  }
}

const saveDataChanges = () => {
  // 收集所有修改后的数据
  if (leadData.value) {
    gameData.value.LeadModule = JSON.stringify(leadData.value)
  }

  if (storyData.value) {
    gameData.value.StoryModule = JSON.stringify(storyData.value)
  }

  if (chpData.value) {
    gameData.value.ChpModule = JSON.stringify(chpData.value)
  }

  if (taskData.value) {
    gameData.value.TaskModule = JSON.stringify(taskData.value)
  }

  if (bookData.value) {
    gameData.value.BookModule = JSON.stringify(bookData.value)
  }

  if (knapsackData.value) {
    gameData.value.KnapsackModule = JSON.stringify(knapsackData.value)
  }

  if (staminaData.value) {
    gameData.value.StaminaModule = JSON.stringify(staminaData.value)
  }

  if (vipData.value) {
    gameData.value.VipModule = JSON.stringify(vipData.value)
  }

  // 更新原始JSON字符串
  rawJsonStr.value = JSON.stringify({ code: 0, data: gameData.value, message: "successful" }, null, 2)

  showMessage('✅ 数据修改已保存', 'success')
  hasDataChanges.value = false
}

const getItemName = (id) => {
  // 首先检查自定义名称
  if (customItemNames.value[id]) {
    return customItemNames.value[id]
  }

  // 然后检查预定义的映射
  const itemMap = {
    1001: '功勋',
    10: '元宝',
    1032: '换一批',
    1002: '功勋点',
    1005: '蜀国技能书',
    1006: '魏国技能书',
    1007: '吴国技能书',
    1008: '全军技能书'
  }
  return itemMap[id] || `未知物品(${id})`
}

const getItemTagType = (id) => {
  // 首先检查自定义标签
  if (customItemTags.value[id]) {
    return customItemTags.value[id]
  }

  // 然后检查预定义的映射
  const typeMap = {
    1001: 'success',  // 功勋用绿色标签
    10: 'warning',    // 元宝用黄色标签
    1032: 'info',     // 换一批用蓝色标签
    1002: 'danger',   // 功勋点用红色标签
    1005: 'success',  // 蜀国技能书用绿色标签
    1006: 'info',     // 魏国技能书用蓝色标签
    1007: 'warning',  // 吴国技能书用黄色标签
    1008: 'danger'    // 全军技能书用红色标签
  }
  return typeMap[id] || 'info'
}

const modifyItemCount = (item, amount) => {
  item.count = Math.max(0, item.count + amount)
  hasDataChanges.value = true
}

// 添加单个模块上传方法
const uploadSingleModule = async (moduleName) => {
  if (!form.openId || !gameData.value || !gameData.value[moduleName]) {
    ElMessage.warning(`${moduleName} 数据不存在`)
    return
  }

  // 保存当前模块的修改
  saveDataChanges()

  const startLoading = () => {
    actionStatus.value = {
      message: `正在上传 ${moduleName} 数据...`,
      type: 'info'
    }
  }

  const finishLoading = (success, message) => {
    actionStatus.value = {
      message: message,
      type: success ? 'success' : 'error'
    }
  }

  startLoading()

  try {
    // 构建请求数据
    const requestData = {
      game_id: '1448',
      open_id: form.openId,
      key: moduleName,
      value: gameData.value[moduleName]
    }

    // 加密数据
    const encryptedData = encrypt(requestData, KEY_GAME)
    if (!encryptedData) {
      finishLoading(false, `${moduleName} 数据加密失败`)
      return
    }

    // 发送请求
    const formData = new URLSearchParams()
    formData.append('data', encryptedData)

    const response = await axios.post('https://api.zqygame.com/api/game/save_data', formData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'xweb_xhr': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
        'Referer': 'https://servicewechat.com/wxa31df10decf52417/7/page-frame.html'
      }
    })

    if (response.data && response.data.code === 0) {
      finishLoading(true, `${moduleName} 数据上传成功`)
    } else {
      finishLoading(false, `${moduleName} 数据上传失败: ${response.data?.message || '未知错误'}`)
    }
  } catch (error) {
    console.error(`上传 ${moduleName} 出错:`, error)
    finishLoading(false, `上传 ${moduleName} 出错: ${error.message || '网络错误'}`)
  }
}

// 添加一个辅助函数来显示原始响应数据
const displayRawResponse = () => {
  // 如果有原始响应数据，显示在原始数据标签页
  if (rawJsonStr.value) {
    activeTab.value = 'json'
    showMessage('已切换到原始数据标签页，您可以查看完整的响应数据', 'info')
  }
}

// 显示添加物品对话框
const showAddItemDialog = () => {
  newItem.id = ''
  newItem.name = ''
  newItem.count = 1
  newItem.tagType = 'info'
  addItemDialogVisible.value = true
}

// 添加物品到背包
const addItem = () => {
  if (!newItem.id) {
    showMessage('请输入物品ID', 'warning')
    return
  }

  if (!knapsackData.value) {
    showMessage('背包数据不存在', 'error')
    return
  }

  // 如果提供了名称，添加到自定义名称映射
  if (newItem.name) {
    customItemNames.value[parseInt(newItem.id)] = newItem.name
    // 如果提供了标签类型，添加到自定义标签映射
    if (newItem.tagType) {
      customItemTags.value[parseInt(newItem.id)] = newItem.tagType
    }
  }

  // 生成UUID - 使用物品ID作为UUID的基础
  const uuid = newItem.id.toString()

  // 检查物品是否已存在
  const existingItem = knapsackData.value.list.find(item => item.id === parseInt(newItem.id))

  if (existingItem) {
    // 如果已存在，增加数量
    existingItem.count += parseInt(newItem.count)
    showMessage(`✅ 已增加 ${newItem.count} 个物品(ID: ${newItem.id})`)
  } else {
    // 如果不存在，添加新物品
    knapsackData.value.list.push({
      uuid: uuid,
      id: parseInt(newItem.id),
      count: parseInt(newItem.count)
    })
    showMessage(`✅ 已添加 ${newItem.count} 个物品(ID: ${newItem.id})`)
  }

  hasDataChanges.value = true
  addItemDialogVisible.value = false
}

// 添加激活次数相关的方法
const addActivateTime = () => {
  if (!leadData.value.activate_times) {
    leadData.value.activate_times = []
  }
  leadData.value.activate_times.push({
    id: 1001,
    times: 1
  })
  hasDataChanges.value = true
}

const removeActivateTime = (index) => {
  leadData.value.activate_times.splice(index, 1)
  hasDataChanges.value = true
}

// 添加缺失角色方法
const addMissingCharacters = () => {
  if (!leadData.value) return

  // 确保own_ids数组存在
  if (!leadData.value.own_ids) {
    leadData.value.own_ids = []
  }

  // 需要添加的角色ID范围
  const requiredIds = [1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008]
  let addedCount = 0

  // 遍历检查并添加缺失的角色
  requiredIds.forEach(id => {
    if (!leadData.value.own_ids.includes(id)) {
      leadData.value.own_ids.push(id)
      addedCount++
    }
  })

  // 更新ownIdsStr
  ownIdsStr.value = leadData.value.own_ids.join(',')
  hasDataChanges.value = true

  if (addedCount > 0) {
    showMessage(`✅ 成功添加 ${addedCount} 个缺失角色`)
  } else {
    showMessage('所有角色已存在，无需添加', 'info')
  }
}

// 美化提示信息的函数
const showMessage = (message, type = 'success', duration = 3000) => {
  ElMessage({
    message: message,
    type: type,
    duration: duration,
    showClose: true,
    grouping: true,
    customClass: 'custom-message'
  })
}

// 添加套餐一处理方法
const handlePackageOne = () => {
  if (!gameData.value) {
    showMessage('请先下载玩家数据', 'warning')
    return
  }

  try {
    // 确保获取最新的背包数据
    const latestKnapsackData = JSON.parse(gameData.value.KnapsackModule)

    // 查找元宝（ID: 10）
    const yuanbao = latestKnapsackData.list.find(item => item.id === 10)
    if (yuanbao) {
      yuanbao.count = 2100000000
      // 更新gameData中的背包数据
      gameData.value.KnapsackModule = JSON.stringify(latestKnapsackData)
      hasDataChanges.value = true
      showMessage('✨ 恭喜！已将元宝数量修改为21亿')
      // 自动切换到背包物品标签页
      activeTab.value = 'knapsack'
    } else {
      // 如果没有找到元宝，则添加元宝
      const newYuanbao = {
        uuid: "10",
        id: 10,
        count: 2100000000
      }
      latestKnapsackData.list.push(newYuanbao)
      gameData.value.KnapsackModule = JSON.stringify(latestKnapsackData)
      hasDataChanges.value = true
      showMessage('✨ 恭喜！已添加21亿元宝')
      activeTab.value = 'knapsack'
    }
  } catch (error) {
    console.error('处理背包数据出错:', error)
    showMessage('处理背包数据时出错，请重试', 'error')
  }
}

// 添加套餐二处理方法（21亿功勋）
const handlePackageTwo = () => {
  if (!gameData.value) {
    showMessage('请先下载玩家数据', 'warning')
    return
  }

  try {
    const latestKnapsackData = JSON.parse(gameData.value.KnapsackModule)
    const gongxun = latestKnapsackData.list.find(item => item.id === 1001)
    if (gongxun) {
      gongxun.count = 2100000000
    } else {
      latestKnapsackData.list.push({
        uuid: "1001",
        id: 1001,
        count: 2100000000
      })
    }
    gameData.value.KnapsackModule = JSON.stringify(latestKnapsackData)
    hasDataChanges.value = true
    showMessage('✨ 恭喜！已将功勋数量修改为21亿')
    activeTab.value = 'knapsack'
  } catch (error) {
    console.error('处理背包数据出错:', error)
    showMessage('处理背包数据时出错，请重试', 'error')
  }
}

// 添加套餐三处理方法（21亿换一批）
const handlePackageThree = () => {
  if (!gameData.value) {
    showMessage('请先下载玩家数据', 'warning')
    return
  }

  try {
    const latestKnapsackData = JSON.parse(gameData.value.KnapsackModule)
    const huanyipi = latestKnapsackData.list.find(item => item.id === 1032)
    if (huanyipi) {
      huanyipi.count = 2100000000
    } else {
      latestKnapsackData.list.push({
        uuid: "1032",
        id: 1032,
        count: 2100000000
      })
    }
    gameData.value.KnapsackModule = JSON.stringify(latestKnapsackData)
    hasDataChanges.value = true
    showMessage('✨ 恭喜！已将换一批数量修改为21亿')
    activeTab.value = 'knapsack'
  } catch (error) {
    console.error('处理背包数据出错:', error)
    showMessage('处理背包数据时出错，请重试', 'error')
  }
}

// 添加终极套餐处理方法
const handleUltimatePackage = async () => {
  if (!gameData.value) {
    showMessage('请先下载玩家数据', 'warning')
    return
  }

  try {
    // 1. 修改背包物品
    const latestKnapsackData = JSON.parse(gameData.value.KnapsackModule)
    const items = [
      { id: 10, count: 2100000000 },    // 21亿元宝
      { id: 1001, count: 2100000000 },  // 21亿功勋
      { id: 1032, count: 2100000000 },  // 21亿换一批
      { id: 1002, count: 100000000 },   // 1亿功勋点
      { id: 1005, count: 100000000 },   // 1亿蜀国技能书
      { id: 1006, count: 100000000 },   // 1亿魏国技能书
      { id: 1007, count: 100000000 },   // 1亿吴国技能书
      { id: 1008, count: 100000000 }    // 1亿全军技能书
    ]

    items.forEach(item => {
      const existingItem = latestKnapsackData.list.find(i => i.id === item.id)
      if (existingItem) {
        existingItem.count = item.count
      } else {
        latestKnapsackData.list.push({
          uuid: item.id.toString(),
          id: item.id,
          count: item.count
        })
      }
    })
    gameData.value.KnapsackModule = JSON.stringify(latestKnapsackData)

    // 2. 修改VIP等级
    const latestVipData = JSON.parse(gameData.value.VipModule)
    latestVipData.level = 12
    latestVipData.exp = 999999
    gameData.value.VipModule = JSON.stringify(latestVipData)

    // 3. 解锁所有主角
    const latestLeadData = JSON.parse(gameData.value.LeadModule)
    if (!latestLeadData.own_ids) {
      latestLeadData.own_ids = []
    }
    const requiredIds = [1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008]
    requiredIds.forEach(id => {
      if (!latestLeadData.own_ids.includes(id)) {
        latestLeadData.own_ids.push(id)
      }
    })
    gameData.value.LeadModule = JSON.stringify(latestLeadData)

    hasDataChanges.value = true
    showMessage('🎉 恭喜！终极套餐已应用成功！', 'success', 5000)
    activeTab.value = 'knapsack'
  } catch (error) {
    console.error('处理终极套餐数据出错:', error)
    showMessage('处理终极套餐数据时出错，请重试', 'error')
  }
}
</script>

<style scoped>
.three-kingdoms-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  min-height: calc(100vh - 120px);
}

.main-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.main-card:hover {
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.data-card,
.info-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  transform: translateZ(0);
  min-height: 300px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title {
  margin: 0;
  font-size: 1.5rem;
  color: #303133;
  font-weight: 600;
}

.content {
  padding: 10px 0;
}

.input-section {
  margin-bottom: 20px;
}

.custom-input {
  width: 100%;
  font-size: 1rem;
}

.action-section {
  margin-bottom: 20px;
}

.buttons-wrapper {
  display: flex;
  gap: 15px;
}

.action-button {
  min-width: 120px;
  padding: 12px 20px;
  font-size: 1rem;
  border-radius: 6px;
}

.status-section {
  margin-top: 20px;
}

.view-raw-data {
  margin-top: 10px;
  display: flex;
  justify-content: flex-end;
}

.info-content {
  padding: 10px 0;
}

.data-tabs {
  margin-top: 15px;
}

.data-section {
  padding: 10px;
  min-height: 200px;
}

.edit-form {
  max-width: 600px;
}

.skill-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.activate-item {
  margin-bottom: 10px;
  padding: 10px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}

.activate-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.activate-label {
  color: #606266;
  font-size: 14px;
}

.module-container {
  margin-bottom: 20px;
}

.stabilize-height {
  min-height: 300px;
  overflow-y: auto;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  -ms-overflow-style: none;
  /* 添加平滑滚动 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

.stabilize-height::-webkit-scrollbar {
  display: none;
}

.el-table {
  height: auto !important;
  max-height: 600px;
}

/* 自定义表格样式，防止ResizeObserver错误 */
.static-table-container {
  width: 100%;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  overflow: hidden;
  background-color: #fff;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
  /* 隐藏滚动条 */
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
  /* 添加平滑滚动 */
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
  /* iOS滚动惯性 */
}

/* Chrome, Safari和Opera的滚动条隐藏 */
.table-body::-webkit-scrollbar {
  display: none;
}

.table-header {
  display: flex;
  background-color: #f5f7fa;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #ebeef5;
}

.th,
.td {
  padding: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.uuid-col {
  width: 120px;
}

.id-col {
  width: 100px;
}

.name-col {
  width: 120px;
}

.count-col {
  width: 150px;
}

.action-col {
  flex: 1;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #ebeef5;
  transition: background-color 0.25s ease;
}

.table-row:hover {
  background-color: #f5f7fa;
}

.table-row:last-child {
  border-bottom: none;
}

.button-group {
  display: flex;
  gap: 8px;
}

/* 添加英雄表格的特定列宽 */
.hp-col {
  width: 120px;
}

.status-col {
  width: 120px;
}

.position-col {
  width: 180px;
}

.kill-col {
  width: 160px;
}

@media (max-width: 768px) {
  .three-kingdoms-container {
    padding: 10px;
  }

  .buttons-wrapper {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
  }

  .edit-form {
    max-width: 100%;
  }

  .static-table-container {
    overflow-x: auto;
    /* 在移动端隐藏水平滚动条 */
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  /* Chrome, Safari和Opera的水平滚动条隐藏 */
  .static-table-container::-webkit-scrollbar {
    display: none;
  }

  .table-header,
  .table-row {
    min-width: 600px;
  }
}

/* 添加图鉴表格的列宽样式 */
.hero-id-col {
  width: 120px;
}

.hero-state-col {
  width: 200px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.data-parse-guide {
  margin-bottom: 10px;
}

.parse-alert {
  margin-bottom: 10px;
}

.own-ids-container {
  display: flex;
  gap: 10px;
  align-items: center;
}

.own-ids-container .el-input {
  flex: 1;
}

/* 添加自定义输入框宽度样式 */
.short-input {
  width: 180px !important;
}

.long-input {
  min-width: 300px !important;
}

.tooltip-popper {
  max-width: 300px;
  font-size: 14px;
  line-height: 1.5;
  padding: 10px 12px;
  word-break: break-all;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.85);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  color: #fff;
}

.custom-tooltip {
  padding: 12px !important;
  border-radius: 8px !important;
  background: rgba(40, 44, 52, 0.9) !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25) !important;
  backdrop-filter: blur(5px) !important;
}

.tooltip-content {
  min-width: 200px;
  max-width: 280px;
}

.tooltip-title {
  font-weight: bold;
  margin-bottom: 8px;
  color: #f8bb86;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  padding-bottom: 5px;
}

.tooltip-list {
  margin: 0;
  padding-left: 20px;
}

.tooltip-list li {
  margin-bottom: 5px;
  list-style-type: none;
  position: relative;
}

.custom-message {
  min-width: 280px;
  padding: 12px 15px;
  font-size: 14px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>