<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="tower-defense">
      <el-card class="game-card">
        <!-- 头部标题 -->
        <div class="card-header-wrapper">
          <div class="card-header">
            <el-icon class="header-icon"><House /></el-icon>
            <span>塔塔守卫战数据修改</span>
          </div>
        </div>

        <div class="card-content">
          <!-- OpenID 输入框和下载按钮始终显示 -->
          <div class="openid-section">
            <el-input 
              v-model="form.openId" 
              placeholder="请输入游戏用户OpenID"
              size="large"
              clearable
              class="openid-input"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
            
            <div class="button-group">
              <el-button 
                type="primary" 
                @click="handleDownload" 
                :loading="loading.download"
                :icon="Download"
                class="action-button"
              >下载数据</el-button>
              <el-button 
                type="success" 
                @click="handleUpload" 
                :loading="loading.upload"
                :icon="Upload"
                class="action-button"
                :disabled="!hasData"
              >上传数据</el-button>
              <el-button
                type="warning"
                @click="handlePackage1"
                :loading="loading.package1"
                :icon="Present"
                class="action-button"
                :disabled="!hasData"
              >套餐1</el-button>
              <el-button
                type="info"
                @click="handlePackage2"
                :loading="loading.package2"
                :icon="Present"
                class="action-button"
                :disabled="!hasData"
              >套餐2</el-button>
            </div>
          </div>

          <!-- 使用 v-if 控制资源区域的显示 -->
          <div v-if="hasData" class="resources-grid">
            <!-- 资源卡片容器 -->
            <div class="resource-cards">
              <!-- 基础资源卡片 -->
              <div class="resource-section">
                <div class="section-title">
                  <el-icon><Money /></el-icon>
                  <span>基础资源</span>
                </div>
                <div class="resource-inputs">
                  <el-form-item label="金币">
                    <el-input-number 
                      v-model="form.coins" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.coins }"
                    />
                  </el-form-item>
                  <el-form-item label="钻石">
                    <el-input-number 
                      v-model="form.diamonds" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.diamonds }"
                    />
                  </el-form-item>
                  <el-form-item label="主塔等级">
                    <el-input-number 
                      v-model="form.towerLevel" 
                      :min="0" 
                      controls-position="right"
                    />
                  </el-form-item>
                </div>
              </div>

              <!-- 特殊资源卡片 -->
              <div class="resource-section">
                <div class="section-title">
                  <el-icon><Star /></el-icon>
                  <span>特殊资源</span>
                </div>
                <div class="resource-inputs">
                  <el-form-item label="巅峰灵石">
                    <el-input-number 
                      v-model="form.peakStone" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.peakStone }"
                    />
                  </el-form-item>
                  <el-form-item label="万能碎片">
                    <el-input-number 
                      v-model="form.fragments" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.fragments }"
                    />
                  </el-form-item>
                  <el-form-item label="副本门票">
                    <el-input-number 
                      v-model="form.tickets" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.tickets }"
                    />
                  </el-form-item>
                  <el-form-item label="逾越之石">
                    <el-input-number 
                      v-model="form.transcendStone" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.transcendStone }"
                    />
                  </el-form-item>
                </div>
              </div>

              <!-- 稀有资源卡片 -->
              <div class="resource-section">
                <div class="section-title">
                  <el-icon><MagicStick /></el-icon>
                  <span>稀有资源</span>
                </div>
                <div class="resource-inputs">
                  <el-form-item label="绯红宝石">
                    <el-input-number 
                      v-model="form.crimsonGems" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.crimsonGems }"
                    />
                  </el-form-item>
                  <el-form-item label="熔岩火晶">
                    <el-input-number 
                      v-model="form.lavacrystal" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.lavacrystal }"
                    />
                  </el-form-item>
                  <el-form-item label="征龙翼爪">
                    <el-input-number 
                      v-model="form.dragonClaw" 
                      :min="0" 
                      controls-position="right"
                    />
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>

          <!-- 未下载数据时显示的提示 -->
          <el-empty 
            v-else
            description="请先下载数据" 
            :image-size="200"
          >
            <template #description>
              <p class="empty-text">请输入OpenID并下载数据以查看和修改游戏资源</p>
            </template>
          </el-empty>
        </div>
      </el-card>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import ZipUtil from '@/utils/zipUtil'
import { fetchGameData, updateGameData, getOriginalData } from '@/api/gameService'
import { 
  House, 
  User, 
  Money, 
  Star, 
  MagicStick,
  Trophy,
  Download,
  Upload,
  Present
} from '@element-plus/icons-vue'

const form = reactive({
  openId: 'oaOqv5O4a20yfIsoGlq33euC9gEQ',
  coins: 0,
  diamonds: 0,
  peakStone: 0,
  fragments: 0,
  tickets: 0,
  transcendStone: 0,
  crimsonGems: 0,
  lavacrystal: 0,
  dragonClaw: 0,
  towerLevel: 0,
  remarks: ''
})

const loading = reactive({
  download: false,
  upload: false,
  package1: false,
  package2: false
})

const hasData = ref(false)

// 在 script setup 部分添加新的响应式变量
const modifiedFields = reactive({
  coins: false,
  diamonds: false,
  peakStone: false,
  fragments: false,
  tickets: false,
  transcendStone: false,
  crimsonGems: false,
  lavacrystal: false
})

// 下载数据
const handleDownload = async () => {
  if (!form.openId) {
    ElMessage.warning('请输入OpenID')
    return
  }

  loading.download = true
  try {
    // 使用专门的服务函数获取数据
    const data = await fetchGameData(form.openId)
    
    // 解析数据
    const gameData = parseGameData(data)
    
    // 更新表单
    updateFormData(gameData)

    ElMessage.success('数据下载成功')
    hasData.value = true

  } catch (error) {
    console.error('下载数据失败:', error)
    ElMessage.error(error.message || '数据下载失败')
  } finally {
    loading.download = false
  }
}

// 解析游戏数据
const parseGameData = (data) => {
  try {
    // 解密数据
    const unzipdata = ZipUtil.unzip(data.slice(5))
    const obj = JSON.parse(unzipdata)
    const obj1 = JSON.parse(obj["content"])
    return obj1
  } catch (error) {
    throw new Error('数据解析失败')
  }
}

// 更新表单数据
const updateFormData = (gameData) => {
  const items = gameData["game166_items_data_v1.0"]
  const tower = gameData['game166_tower_v1.0']

  // 使用对象映射简化赋值
  const itemMapping = {
    '1001': 'coins',
    '1000': 'diamonds', 
    '1007': 'peakStone',
    '1006': 'fragments',
    '1011': 'tickets',
    '1014': 'transcendStone',
    '1017': 'crimsonGems',
    '1016': 'lavacrystal',
    '1020': 'dragonClaw'
  }

  // 遍历映射更新表单
  Object.entries(itemMapping).forEach(([key, field]) => {
    form[field] = items[key] || 0
  })

  // 更新塔等级
  form.towerLevel = tower["towerId2lv"]['0'] || 0
}

// 上传数据
const handleUpload = async () => {
  if (!form.openId) {
    ElMessage.warning('请先输入OpenID')
    return
  }

  loading.upload = true
  try {
    // 获取原始数据
    const originalData = await getOriginalData(form.openId)
    const gameData = JSON.parse(originalData["content"])

    // 更新游戏数据
    gameData["game166_items_data_v1.0"] = {
      ...gameData["game166_items_data_v1.0"],
      "1001": form.coins,
      "1000": form.diamonds,
      "1007": form.peakStone,
      "1006": form.fragments,
      "1011": form.tickets,
      "1014": form.transcendStone,
      "1017": form.crimsonGems,
      "1016": form.lavacrystal,
      "1020": form.dragonClaw
    }

    gameData['game166_tower_v1.0']["towerId2lv"]['0'] = form.towerLevel

    // 更新content和version
    originalData["content"] = JSON.stringify(gameData)
    originalData["version"]++

    // 上传更新后的数据
    await updateGameData(form.openId, originalData["version"], originalData)

    ElMessage.success('数据上传成功')

  } catch (error) {
    console.error('上传数据失败:', error)
    ElMessage.error(error.message || '数据上传失败')
  } finally {
    loading.upload = false
  }
}

// 套餐1处理函数
const handlePackage1 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package1 = true
  try {
    // 设置套餐1的资源数量为20亿
    const twoBillion = 2000000000
    
    // 更新这些字段的值
    const fieldsToUpdate = [
      'diamonds',
      'peakStone',
      'fragments',
      'tickets',
      'transcendStone',
      'crimsonGems',
      'lavacrystal'
    ]

    fieldsToUpdate.forEach(field => {
      form[field] = twoBillion
      modifiedFields[field] = true // 标记字段已被修改
    })
    
    // 自动上传修改后的数据
    await handleUpload()
    
    ElMessage.success('套餐1应用成功')
  } catch (error) {
    console.error('套餐1应用失败:', error)
    ElMessage.error(error.message || '套餐1应用失败')
  } finally {
    loading.package1 = false
  }
}

// 套餐2处理函数
const handlePackage2 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package2 = true
  try {
    // 设置套餐2的资源数量
    const twoBillion = 2000000000
    
    // 更新字段值
    const updates = {
      diamonds: twoBillion,
      coins: twoBillion,
      peakStone: 300,
      fragments: 20000,
      tickets: 300,
      crimsonGems: 300,
      lavacrystal: 300
    }

    // 应用更新并标记修改的字段
    Object.entries(updates).forEach(([field, value]) => {
      form[field] = value
      modifiedFields[field] = true // 移除了对 coins 的特殊处理
    })
    
    // 自动上传修改后的数据
    await handleUpload()
    
    ElMessage.success('套餐2应用成功')
  } catch (error) {
    console.error('套餐2应用失败:', error)
    ElMessage.error(error.message || '套餐2应用失败')
  } finally {
    loading.package2 = false
  }
}
</script>

<style scoped>
.tower-defense {
  padding: 15px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header-wrapper {
  background: linear-gradient(135deg, #67C23A 0%, #95D475 100%);
  padding: 12px 20px;
  border-radius: 8px 8px 0 0;
  margin-bottom: 15px;
  box-shadow: 0 2px 12px rgba(103, 194, 58, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.header-icon {
  font-size: 20px;
  animation: bounce 1s infinite;
}

.card-content {
  padding: 15px;
}

.openid-section {
  margin-bottom: 20px;
}

.openid-input {
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.openid-input:hover {
  transform: translateY(-2px);
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.action-button {
  flex: 1;
  max-width: 200px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.resources-grid {
  padding: 15px;
}

.resource-cards {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.resource-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
  height: 100%;
}

.resource-inputs {
  display: grid;
  gap: 15px;
  grid-template-columns: 1fr;
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #ebeef5;
  font-weight: 600;
}

.section-title .el-icon {
  font-size: 18px;
}

/* Element Plus 组件样式调整 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
  margin-bottom: 20px;
}

.action-button {
  flex: 1;
  min-width: 120px;
  max-width: 200px;
}

/* 卡片悬浮效果 */
.resource-section:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* 动画效果 */
@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tower-defense {
    padding: 10px;
  }

  .button-group {
    flex-direction: column;
    align-items: stretch;
  }

  .action-button {
    width: 100%;
    max-width: none;
  }

  .openid-section {
    margin-bottom: 15px;
  }
}

@media (min-width: 769px) {
  .resource-section {
    min-height: 200px;
  }
}

/* 滚动条样式 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
}

:deep(.el-scrollbar__bar.is-horizontal) {
  display: none !important;
}

.tower-defense::-webkit-scrollbar {
  display: none;
}

.tower-defense {
  scrollbar-width: none;
}

/* 添加空状示的样式 */
.empty-text {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 修改 style 部分的输入框样式 */
:deep(.modified-input) .el-input__wrapper {
  box-shadow: 0 0 0 1px #f56c6c inset !important;
}

:deep(.modified-input:hover) .el-input__wrapper {
  box-shadow: 0 0 0 1px #f56c6c inset !important;
}

:deep(.modified-input) .el-input-number__decrease,
:deep(.modified-input) .el-input-number__increase {
  border-color: #f56c6c !important;
}
</style> 