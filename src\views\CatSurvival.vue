<template>
  <div class="cat-survival">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon><MagicStick /></el-icon>
              <span>游戏工具</span>
            </div>
            <div class="game-selector">
              <el-select 
                v-model="selectedGame" 
                placeholder="请选择游戏"
                size="large"
                @change="handleGameChange"
              >
                <el-option
                  v-for="item in gameOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <el-form 
              ref="formRef"
              :model="form"
              :rules="rules"
              label-position="top"
            >
              <!-- 游戏ID -->
              <el-form-item prop="gameId">
                <template #label>
                  <div class="form-label">
                    <el-icon><Key /></el-icon>
                    <span>游戏ID</span>
                    <div class="form-actions">
                      <el-button 
                        type="danger" 
                        link 
                        :icon="Delete" 
                        @click="clearFormData"
                      >
                        清空
                      </el-button>
                      <el-divider direction="vertical" />
                      <el-button 
                        type="primary" 
                        link 
                        :icon="RefreshRight"
                        @click="restoreDefaults"
                      >
                        恢复默认值
                      </el-button>
                    </div>
                  </div>
                </template>
                <el-input
                  v-model="form.gameId"
                  placeholder="请输入游戏ID"
                  :disabled="loading.download"
                  clearable
                />
              </el-form-item>

              <!-- UserToken -->
              <el-form-item prop="userToken">
                <template #label>
                  <div class="form-label">
                    <el-icon><Lock /></el-icon>
                    <span>UserToken</span>
                  </div>
                </template>
                <el-input
                  v-model="form.userToken"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入或粘贴UserToken"
                  :disabled="loading.download"
                  resize="none"
                />
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮 -->
          <div class="action-section">
            <el-button
              type="primary"
              :loading="loading.download"
              @click="handleDownload"
              :icon="Download"
              class="action-button"
            >
              下载存档
            </el-button>
            <el-button
              type="success"
              :loading="loading.update"
              @click="handleUpdate"
              :icon="Upload"
              :disabled="!hasData"
              class="action-button"
            >
              更新游戏
            </el-button>
            <el-button
              type="danger"
              :loading="loading.package"
              @click="handleRedPackage"
              :icon="Present"
              :disabled="!hasData"
              class="action-button"
            >
              红色套餐
            </el-button>
            <el-button
              type="warning"
              :loading="loading.excellent"
              @click="handleExcellentPackage"
              :icon="Star"
              :disabled="!hasData"
              class="action-button"
            >
              卓越套装
            </el-button>
            <el-button 
              type="info" 
              @click="showSkillComponentDialog"
              :icon="MagicStick"
              :disabled="!hasData"
              class="action-button"
            >
              批量添加技能
            </el-button>
            <el-button 
              type="warning"
              @click="handlePartnerUpgrade"
              :icon="UserFilled"
              :disabled="!hasData"
              class="action-button"
            >
              伙伴强化
            </el-button>
          </div>

          <!-- 游戏���据展示 -->
          <div v-if="hasData" class="game-data-section">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              <span>游戏数据</span>
            </div>
            
            <!-- 修改解密前缀显示 -->
            <div class="prefix-section">
              <el-row :gutter="20" align="middle">
                <el-col :span="4">
                  <div class="prefix-title">解密前缀</div>
                </el-col>
                <el-col :span="20">
                  <div class="prefix-content">
                    <el-tag 
                      type="info" 
                      effect="plain" 
                      class="prefix-tag"
                    >
                      {{ archiveData.game.prefix }}
                    </el-tag>
                  </div>
                </el-col>
              </el-row>
            </div>

            <!-- JSON数据展示 -->
            <div class="json-data">
              <el-tabs type="border-card">
                <!-- 基础属性 tab -->
                <el-tab-pane label="基础属性">
                  <el-form :model="archiveData.game.data" label-position="top">
                    <el-row :gutter="20">
                      <el-col :span="8">
                        <el-form-item label="金币">
                          <el-input-number 
                            v-model="archiveData.game.data.g" 
                            :min="0"
                            :max="999999999"
                            controls-position="right"
                            style="width: 100%"
                            @change="() => recordModification('game', 'g')"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="体力">
                          <el-input-number 
                            v-model="archiveData.game.data.e" 
                            :min="0"
                            :max="999999"
                            controls-position="right"
                            style="width: 100%"
                            @change="() => recordModification('game', 'e')"
                          />
                        </el-form-item>
                      </el-col>
                      <el-col :span="8">
                        <el-form-item label="战斗次数">
                          <el-input-number 
                            v-model="archiveData.game.data.fightTimes" 
                            :min="0"
                            controls-position="right"
                            @change="() => recordModification('game', 'fightTimes')"
                          />
                        </el-form-item>
                      </el-col>
                    </el-row>
                  </el-form>
                </el-tab-pane>

                <!-- 资源道具 tab -->
                <el-tab-pane label="资源道具">
                  <div class="resource-table-container">
                    <el-table 
                      :data="resourceItemsList" 
                      border 
                      stripe
                      height="400"
                    >
                      <el-table-column label="道具" prop="name" min-width="120" />
                      <el-table-column label="当前数量" width="200" fixed="right">
                        <template #default="scope">
                          <el-input-number 
                            v-model="scope.row.value"
                            :min="0"
                            :max="999999999"
                            controls-position="right"
                            style="width: 100%"
                            @change="(val) => {
                              updateItemValue(scope.row.id, val);
                              recordModification('game', 'items');
                            }"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column label="说明" prop="description" min-width="150" show-overflow-tooltip />
                    </el-table>
                  </div>
                </el-tab-pane>

                <!-- 任务系统 tab -->
                <el-tab-pane label="任务系统">
                  <el-descriptions :column="2" border>
                    <el-descriptions-item 
                      v-for="(mission, id) in archiveData.mission.data" 
                      :key="id"
                      :label="getMissionName(id)"
                    >
                      <el-switch
                        v-model="archiveData.mission.data[id].completed"
                        active-text="已完成"
                        inactive-text="未完成"
                      />
                    </el-descriptions-item>
                  </el-descriptions>
                </el-tab-pane>

                <!-- 武器片 tab -->
                <el-tab-pane label="武器碎片">
                  <div class="table-container">
                    <el-table 
                      :data="weaponChips" 
                      border 
                      stripe
                      height="auto"
                      :max-height="500"
                    >
                      <el-table-column label="碎片ID" prop="id" width="100" />
                      <el-table-column label="碎片名称" prop="name" min-width="120" />
                      <el-table-column label="当前数量" width="200" fixed="right">
                        <template #default="scope">
                          <el-input-number 
                            v-model="archiveData.weapon.data.chip[scope.row.id]"
                            :min="0"
                            :max="999999999"
                            controls-position="right"
                            style="width: 100%"
                            @change="() => {
                              recordModification('weapon', 'chip');
                              updateWeaponData();
                            }"
                          />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <!-- 我的装备 tab -->
                <el-tab-pane label="我的装备">
                  <div class="equipment-section">
                    <!-- 已装备 -->
                    <div class="section-block">
                      <div class="block-title">
                        <el-icon><Star /></el-icon>
                        <span>已装备</span>
                      </div>
                      <div class="table-wrapper">
                        <el-table 
                          :data="equippedItems" 
                          border 
                          stripe
                          height="250"
                        >
                          <el-table-column label="装备位置" prop="position" width="120" />
                          <el-table-column label="装备ID" prop="id" width="120" />
                          <el-table-column label="装备名称" prop="name" min-width="150" />
                          <el-table-column label="操作" width="120" fixed="right">
                            <template #default="scope">
                              <el-button 
                                type="danger" 
                                link
                                @click="unequipItem(scope.row.position, scope.row.index)"
                              >
                                卸下
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>

                    <!-- 背包装备 -->
                    <div class="section-block">
                      <div class="block-title">
                        <el-icon><Box /></el-icon>
                        <span>背包装备 (总计: {{ inventoryItems.length }}件</span>
                        <el-tag 
                          type="danger" 
                          effect="dark" 
                          class="red-equipment-count"
                        >
                          红色: {{ redEquipmentCount }}件
                        </el-tag>
                        <el-tag 
                          type="warning" 
                          effect="dark" 
                          class="excellent-equipment-count"
                        >
                          卓越: {{ excellentEquipmentCount }}件
                        </el-tag>
                        <span>)</span>
                      </div>
                      <div class="table-wrapper inventory-table">
                        <el-table 
                          :data="inventoryItems" 
                          border 
                          stripe
                          height="400"
                          style="width: 100%"
                        >
                          <el-table-column label="背包位置" prop="position" width="100" fixed="left" />
                          <el-table-column label="装备ID" prop="id" width="120" />
                          <el-table-column label="装备名称" prop="name" min-width="150" />
                          <el-table-column label="等级" prop="level" width="80" />
                          <el-table-column label="金币" prop="gold" width="100" />
                          <el-table-column label="碎片" prop="chip" width="80" />
                          <el-table-column label="操作" width="180" fixed="right">
                            <template #default="scope">
                              <el-button 
                                type="primary" 
                                link
                                @click="equipItem(scope.row.position, scope.row.id)"
                              >
                                装备
                              </el-button>
                              <el-button 
                                type="danger" 
                                link
                                @click="deleteInventoryItem(scope.row.position)"
                              >
                                删除
                              </el-button>
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>

                    <!-- 碎片信息 -->
                    <div class="section-block">
                      <div class="block-title">
                        <el-icon><Collection /></el-icon>
                        <span>碎片信息</span>
                      </div>
                      <div class="table-wrapper">
                        <el-table 
                          :data="weaponChips" 
                          border 
                          stripe
                          height="250"
                        >
                          <el-table-column label="碎片ID" prop="id" width="100" />
                          <el-table-column label="碎片信息" prop="name" min-width="120" />
                          <el-table-column label="当前数量" width="200" fixed="right">
                            <template #default="scope">
                              <el-input-number 
                                v-model="archiveData.weapon.data.chip[scope.row.id]"
                                :min="0"
                                :max="999999999"
                                controls-position="right"
                                style="width: 100%"
                                @change="() => {
                                  recordModification('weapon', 'chip');
                                  updateWeaponData();
                                }"
                              />
                            </template>
                          </el-table-column>
                        </el-table>
                      </div>
                    </div>
                  </div>

                  <!-- 添加技能组件弹窗 -->
                  <el-dialog
                    v-model="skillComponentDialog.visible"
                    title="添加技能组件"
                    width="40%"
                    class="skill-dialog"
                    :append-to-body="true"
                    :lock-scroll="true"
                    :modal="true"
                  >
                    <div class="skill-component-list">
                      <el-table
                        ref="skillTable"
                        :data="skillComponents"
                        border
                        stripe
                        height="400"
                        @selection-change="handleSelectionChange"
                        @row-click="handleRowClick"
                        :row-key="row => row.id"
                        style="width: 100%"
                      >
                        <el-table-column 
                          type="selection" 
                          width="45" 
                          fixed="left"
                        />
                        <el-table-column 
                          label="技能ID" 
                          prop="id" 
                          width="90"
                        />
                        <el-table-column 
                          label="技能名称" 
                          prop="name" 
                          width="100"
                        />
                        <el-table-column 
                          label="技能描述" 
                          prop="description" 
                          min-width="90" 
                          show-overflow-tooltip 
                        />
                      </el-table>
                    </div>
                    <template #footer>
                      <span class="dialog-footer">
                        <el-button @click="skillComponentDialog.visible = false">取消</el-button>
                        <el-button type="primary" @click="addSelectedSkillComponents">
                          添加选中技能
                        </el-button>
                        <el-button type="success" @click="addAllSkillComponents">
                          添加全部技能
                        </el-button>
                      </span>
                    </template>
                  </el-dialog>
                </el-tab-pane>

                <!-- 伙伴强化 tab -->
                <el-tab-pane label="伙伴强化">
                  <div class="table-container">
                    <el-table 
                      :data="partnersList" 
                      border 
                      stripe
                      height="400"
                      style="width: 100%"
                    >
                      <el-table-column label="伙伴ID" prop="id" width="100" />
                      <el-table-column label="等级" width="150">
                        <template #default="scope">
                          <el-input-number 
                            v-model="archiveData.partner.data.partners[scope.row.id].lv"
                            :min="1"
                            :max="999"
                            controls-position="right"
                            style="width: 100%"
                            @change="() => {
                              recordModification('partner', 'partners');
                              updatePartnerData();
                            }"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column label="碎片" width="150">
                        <template #default="scope">
                          <el-input-number 
                            v-model="archiveData.partner.data.partners[scope.row.id].chips"
                            :min="0"
                            :max="999"
                            controls-position="right"
                            style="width: 100%"
                            @change="() => {
                              recordModification('partner', 'partners');
                              updatePartnerData();
                            }"
                          />
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" width="120">
                        <template #default="scope">
                          <el-tag 
                            :type="scope.row.unlocked ? 'success' : 'info'"
                          >
                            {{ scope.row.unlocked ? '已解锁' : '未解锁' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>

                <!-- 技能组件 tab -->
                <el-tab-pane label="技能组件">
                  <div class="skill-part-container">
                    <el-table 
                      :data="skillPartList" 
                      border 
                      stripe
                      height="400"
                    >
                      <el-table-column label="位置" prop="position" width="100" />
                      <el-table-column label="技能ID" prop="id" width="120" />
                      <el-table-column label="技能名称" prop="name" min-width="150" show-overflow-tooltip />
                      <el-table-column label="操作" width="120" fixed="right">
                        <template #default="scope">
                          <el-button 
                            type="danger" 
                            link
                            @click="removeSkillPart(scope.row.position)"
                          >
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>

            <!-- 操作历史 -->
            <div v-if="operationHistory.length" class="history-section">
              <div class="section-title">
                <el-icon><Timer /></el-icon>
                <span>操作记录</span>
              </div>
              <el-timeline>
                <el-timeline-item
                  v-for="(history, index) in operationHistory.slice(0, 5)"
                  :key="index"
                  :type="history.success ? 'success' : 'danger'"
                  :timestamp="history.time"
                  :hollow="true"
                  size="normal"
                >
                  {{ history.operation }}
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, computed, nextTick } from 'vue'
import { 
  ElMessage, 
  ElAlert,
  ElDivider,
  ElButton,
  ElInput,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElCard,
  ElRow,
  ElCol,
  ElTag,
  ElTabs,
  ElTabPane,
  ElTable,
  ElTableColumn,
  ElInputNumber,
  ElDescriptions,
  ElDescriptionsItem,
  ElTimeline,
  ElTimelineItem,
  ElDialog
} from 'element-plus'
import { 
  MagicStick, 
  Key, 
  Timer,
  Download,
  Upload,
  Lock,
  Document,
  Delete,
  RefreshRight,
  Star,
  Box,
  Collection,
  Present,
  UserFilled,
} from '@element-plus/icons-vue'
import { CatGameCrypto } from '@/utils/CatGameCrypto'

// 添加响应式判断
const isMobile = ref(false)
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 修改 onMounted 钩子
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);

  // 重写 ResizeObserver 构造函数
  const OriginalResizeObserver = window.ResizeObserver;
  window.ResizeObserver = class ResizeObserver extends OriginalResizeObserver {
    constructor(callback) {
      super((entries, observer) => {
        // 使用 requestAnimationFrame 包装回调
        window.requestAnimationFrame(() => {
          try {
            callback(entries, observer);
          } catch (e) {
            // 吞掉所有错误
          }
        });
      });
    }
  };

  // 重写 console.error 来过滤掉所有 ResizeObserver 相关的错误
  const originalConsoleError = console.error;
  console.error = (...args) => {
    if (args.some(arg => 
      String(arg).includes('ResizeObserver') || 
      (arg instanceof Error && arg.message.includes('ResizeObserver'))
    )) {
      return;
    }
    originalConsoleError.apply(console, args);
  };

  // 添加全局错误处理
  const errorHandler = (event) => {
    if (event?.message?.includes('ResizeObserver') || 
        event?.error?.message?.includes('ResizeObserver')) {
      event.preventDefault();
      event.stopImmediatePropagation();
      return false;
    }
  };

  // 添加错误监听
  window.addEventListener('error', errorHandler, true);
  window.addEventListener('unhandledrejection', errorHandler, true);

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('resize', checkMobile);
    window.removeEventListener('error', errorHandler, true);
    window.removeEventListener('unhandledrejection', errorHandler, true);
    window.ResizeObserver = OriginalResizeObserver;
    console.error = originalConsoleError;
  });
});

// 添加 formRef 定义
const formRef = ref(null)

// 修改默认值常量,添加不同游戏的默认值
const DEFAULT_FORM_DATA = {
  cat: {
    gameId: 'o49cE44zt0x6Mkt5EOYTZi8KSFdc',
    userToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************************************************************************************************************.ElqcE6iEwmQIY-ZWdwN2HNJGtivffxed84tSdZnH5ww',
  },
  warrior: {
    gameId: 'oFfPP5MTYjvAtUMjjGUbbMHrOAaM',
    userToken: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************.7koq3Jd8yvhxBo2Zfby6T3NuumkDjmA5y6HD8NKqKSE',
  }
};

// 清空表单数据
const clearFormData = () => {
  form.gameId = ''
  form.userToken = ''
  form.gameData = {
    prefix: '',
    data: null
  }
  hasData.value = false
}

// 恢复默认
const restoreDefaults = () => {
  const defaultData = DEFAULT_FORM_DATA[selectedGame.value];
  form.gameId = defaultData.gameId;
  form.userToken = defaultData.userToken;
  form.gameData = {
    prefix: '',
    data: null
  };
  hasData.value = false;
}

// 表单数据 - 添加默认值
const form = reactive({
  gameId: DEFAULT_FORM_DATA.cat.gameId,
  userToken: DEFAULT_FORM_DATA.cat.userToken,
  gameData: {
    prefix: '',
    data: null
  }
});

// 表单则 - 修改gameId的验证规则以适应新的��式
const rules = {
  gameId: [
    { required: true, message: '请输入游戏ID', trigger: 'blur' }
  ],
  userToken: [
    { required: true, message: '请输入UserToken', trigger: 'blur' }
  ]
}

// 加载状态
const loading = reactive({
  download: false,
  update: false,
  package: false,
  excellent: false  // 添加卓越套装的加载状态
});

// 是否已获取数据
const hasData = ref(false)

// 最后一次作结果
const lastOperation = reactive({
  title: '',
  type: 'info',
  description: ''
})

// 操作历史
const operationHistory = ref([])

// 获取当前格林威治时间
const getGMTTime = () => {
  return new Date().toUTCString()
}

// 修改公共配置中的 userid
const COMMON_CONFIG = {
  device_id: "jjrV0box6-eTxJyKAFI-U",
  os: "Windows",
  version: "**************",
  userid: "****************",
  account_subtype: "wx"
};

// 添加一个响应式引用来存储从服务器获取的用户信息
const serverUserInfo = ref({
  user_id: '',
  device_id: '',
  version: ''
});

// 添加游戏选择相关的变量
const gameOptions = [
  {
    label: '喵桑活下去',
    value: 'cat',
    host: 'ms.vipwzswl.com',
    appid: 10002
  },
  {
    label: '欢乐勇士',
    value: 'warrior',
    host: 'ys.vipwzswl.com',
    appid: 10004
  }
];

const selectedGame = ref('cat'); // 默认选择喵桑活下去

// 获取当前选择的游戏配置
const getCurrentGameConfig = computed(() => {
  return gameOptions.find(game => game.value === selectedGame.value);
});

// 处理游戏切换
const handleGameChange = (value) => {
  // 如果有未保存的修改，提示用户
  if (hasData.value) {
    ElMessage.warning('切换游戏会清空当前数据，请注意保存');
    clearFormData();
  }
  
  // 根��选择的游戏设置对应的默认值
  const defaultData = DEFAULT_FORM_DATA[value];
  form.gameId = defaultData.gameId;
  form.userToken = defaultData.userToken;
};

// 修改 handleDownload 和 handleUpdate 方法中的请求URL
const getApiBaseUrl = () => {
  const gameConfig = getCurrentGameConfig.value;
  return `https://${gameConfig.host}`;
};

// 修改 handleDownload 方法中的请求URL
const handleDownload = async () => {
  loading.download = true;
  try {
    await formRef.value.validate();
    
    // 使用动态的API基础URL和appid
    const baseUrl = `${getApiBaseUrl()}/api/archive/user`;
    const currentGame = getCurrentGameConfig.value;
    
    const params = new URLSearchParams({
      appid: currentGame.appid,  // 使用当前游���的 appid
      device_id: COMMON_CONFIG.device_id,
      os: COMMON_CONFIG.os,
      version: COMMON_CONFIG.version,
      userid: COMMON_CONFIG.userid,
      account_id: form.gameId.trim(),
      account_subtype: COMMON_CONFIG.account_subtype,
      v: Date.now().toString(),
      n: '2'
    }).toString();

    // 发送请求
    const response = await fetch(`${baseUrl}?${params}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${form.userToken.trim()}`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 6.1; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/81.0.4044.138 Safari/537.36 MicroMessenger/6.5.2.501 NetType/WIFI MiniGame WindowsWechat',
        'Referer': 'https://servicewechat.com/wx5d2cae3c27348d78/16/page-frame.html',
        'Origin': 'https://servicewechat.com',
        'Accept': '*/*',
        'Content-Type': 'application/json;charset=UTF-8'
      },
      mode: 'cors',
      credentials: 'omit'
    });

    if (!response.ok) {
      const errorText = await response.text()
      console.error('Response:', errorText)
      
      if (response.status === 401) {
        throw new Error('授权失败：请检查 UserToken 是否确')
      }
      
      throw new Error(`请求失败: ${response.status} - ${errorText}`)
    }

    const data = await response.json();
    if (data.status === 0 && data.data) {
      // 保存服务器返回的用户信息
      serverUserInfo.value = {
        user_id: data.data.user_id || '',
        device_id: data.data.device_id || '',
        version: data.data.version || ''
      };

      if (data.data.archive) {
        try {
          // 解所有存档数据
          const decryptedArchive = CatGameCrypto.decryptArchiveData(data.data.archive);
          
          // 添加调试信息输出
          console.group('游戏数据解密信息');
          console.log('原始加密数据:', data.data.archive);
          console.log('解密后的数据:', decryptedArchive);
          
          // 特别输出道具28的值
          if (decryptedArchive.game?.data?.items?.[28]) {
            console.log('道具28的值:', decryptedArchive.game.data.items[28]);
          }
          
          // 处理解密后的数据
          Object.entries(decryptedArchive).forEach(([key, value]) => {
            if (archiveData.value[key]) {
              console.log(`解析 ${key} 数据:`, value);
              archiveData.value[key] = {
                prefix: value.prefix || '',
                data: value.data || {},
                jsonStr: value.data ? JSON.stringify(value.data, null, 2) : '',
                originalValue: data.data.archive[key]
              };
            }
          });
          
          console.log('最终处理后的数据:', archiveData.value);
          console.groupEnd();

          hasData.value = true;
          lastOperation.title = '存档下载成功';
          lastOperation.type = 'success';
          lastOperation.description = '游戏数据已成功获取并解密';
          
          operationHistory.value.unshift({
            time: new Date().toLocaleString(),
            operation: '下载存档成功',
            success: true
          });
          
          ElMessage.success('游戏数据获取成功');
        } catch (error) {
          throw new Error(`游戏数据解密失败: ${error.message}`);
        }
      }
    }
  } catch (error) {
    console.error('获取游戏���据失败:', error)
    
    lastOperation.title = '存档下载失败'
    lastOperation.type = 'error'
    lastOperation.description = error.message || '获取游戏数据失败'
    
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `下载存档失败: ${error.message}`,
      success: false
    });
    
    ElMessage.error(error.message || '获取游戏数据失败')
  } finally {
    loading.download = false
  }
}

// ���始化 modifiedFields
const modifiedFields = ref({
  game: new Set(),
  mission: new Set(),
  weapon: new Set(),
  partner: new Set(),  // 移除 shop
  skillPart: new Set()  // 添加 skillPart 的修改
});

// 修改记录修改的方法
const recordModification = (category, field) => {
  if (!modifiedFields.value[category]) {
    modifiedFields.value[category] = new Set();
  }
  modifiedFields.value[category].add(field);
};

// 修改新游戏数据的法
const handleUpdate = async () => {
  loading.update = true;
  try {
    // 只构建存在的属性到 delta_archive 对象
    const deltaArchive = {};
    
    // 遍历 archiveData,只添加存在的属性
    for (const [key, value] of Object.entries(archiveData.value)) {
      if (value?.originalValue) {
        // 检查是否有修改
        if (modifiedFields.value[key]?.size > 0) {
          // 如果有修改,使用新的加密数据
          deltaArchive[key] = CatGameCrypto.encryptGameData(
            value.data,
            value.prefix
          );
        } else {
          // 如果没有修改,使用原始值
          deltaArchive[key] = value.originalValue;
        }
      }
    }

    // 获取当前游戏配置
    const currentGame = getCurrentGameConfig.value;

    // 构建请求体
    const requestBody = {
      baseInfo: {
        appid: currentGame.appid,  // 使用当前游戏的 appid
        device_id: serverUserInfo.value.device_id || COMMON_CONFIG.device_id,
        os: COMMON_CONFIG.os,
        version: serverUserInfo.value.version || COMMON_CONFIG.version,
        userid: serverUserInfo.value.user_id || COMMON_CONFIG.userid,
        account_id: form.gameId.trim(),
        account_subtype: COMMON_CONFIG.account_subtype
      },
      data: {
        delta_archive: deltaArchive,
        type: "force",
        n: 2
      }
    };

    // 发送更新请求
    const response = await fetch(`${getApiBaseUrl()}/api/archive/user?v=${Date.now()}`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${form.userToken.trim()}`,
        'Content-Type': 'application/json',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781 NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF XWEB/50241',
        'Referer': 'https://servicewechat.com/wx5d2cae3c27348d78/67/page-frame.html',
        'Accept': '*/*',
        'xweb_xhr': '1'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`更新失败: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    if (result.status !== 0) {
      throw new Error(result.message || '更新失败');
    }

    // 更新成功
    ElMessage.success('游戏数据更新成功');
    
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '更新游戏数据成功',
      success: true
    });

  } catch (error) {
    console.error('更新游戏数据失败:', error);
    ElMessage.error(error.message || '更新游戏数据失败');
    
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `更新游戏失败: ${error.message}`,
      success: false
    });
  } finally {
    loading.update = false;
  }
};

// 道具名称映射表
const itemNames = {
  '3': '钻石',
  '5': '天赋',
  '6': '体力',
  '7': '经验',
  '8': '等级',
  '9': '技能点',
  '10': '成就点',
  '11': '抽奖券',
  '13': '荣誉点',
  '14': '竞技点',
  '15': '公会贡献',
  '134': '特殊道具'
};

// 计算属性：转换道具数据为数组格式
const itemsArray = computed(() => {
  if (!form.gameData?.data?.items) return [];
  return Object.entries(form.gameData.data.items).map(([id, count]) => ({
    id,
    count,
    name: getItemName(id)
  }));
});

// 获取道具名称的���
const getItemName = (id) => {
  return itemNames[id] || `未知道具(${id})`;
};

// 存档数据相关
const archiveData = ref({
  dataVersion: {
    prefix: '',
    data: null,
    jsonStr: '',
    originalValue: null
  },
  game: {
    prefix: '',
    data: {
      g: 0,
      e: 1,
      fightTimes: 0,
      items: {}
    },
    jsonStr: '',
    originalValue: null
  },
  mission: {
    prefix: '',
    data: {},
    jsonStr: '',
    originalValue: null
  },
  checkIn: {
    prefix: '',
    data: {},
    jsonStr: '',
    originalValue: null
  },
  guide: {
    prefix: '',
    data: {},
    jsonStr: '',
    originalValue: null
  },
  offline: {
    prefix: '',
    data: {},
    jsonStr: '',
    originalValue: null
  },
  practice: {
    prefix: '',
    data: {},
    jsonStr: '',
    originalValue: null
  },
  weapon: {
    prefix: '',
    data: {},
    jsonStr: '',
    originalValue: null
  },
  ad: {
    prefix: '',
    data: {},
    jsonStr: '',
    originalValue: null
  },
  partner: {
    prefix: '',
    data: {
      partners: {}
    },
    jsonStr: '',
    originalValue: null
  },
  skillPart: {
    prefix: '',
    data: {
      instance: {
        "1": { id: 5000301 },
        "2": { id: 5000201 },
        "3": { id: 5000602 }
      }
    },
    jsonStr: '',
    originalValue: null
  }
});
const activeArchives = ref([]);

// 存档标题映射
const archiveTitles = {
  dataVersion: '数据版本',
  game: '游戏数据',
  mission: '任务数据',
  checkIn: '签到数据',
  guide: '引导数据',
  offline: '离线数据',
  practice: '练习数据',
  weapon: '武器数据',
  ad: '广告数据'
};

// 获取存档标题
const getArchiveTitle = (key) => {
  return archiveTitles[key] || key;
};

// 更新存档数据
const updateArchiveData = (key) => {
  try {
    archiveData.value[key].data = JSON.parse(archiveData.value[key].jsonStr);
  } catch (error) {
    console.warn(`Invalid JSON for ${key}:`, error);
  }
};

// 资源道配置
const resourceItems = [
  { id: '3', name: '钻石', description: '游戏高级货币' },
  { id: '5', name: '天赋', description: '角色天赋点数' },
  { id: '6', name: '体力', description: '行动力' },
  { id: '7', name: '经验', description: '升级经验' },
  { id: '9', name: '技能点', description: '技能升级材料' },
  { id: '10', name: '成就点', description: '成就系统积分' },
  { id: '11', name: '抽奖券', description: '抽奖道具' },
  { id: '13', name: '荣誉点', description: '竞技场货币' },
  { id: '14', name: '竞技点', description: 'PVP积分' },
  { id: '15', name: '公会贡献', description: '公会活动积分' },
  // 添加新的道具配置
  { id: '18', name: '道具18', description: '道具18描述' },
  { id: '19', name: '道具19', description: '道具19描述' },
  { id: '20', name: '道具20', description: '道具20描述' },
  { id: '28', name: '冒险币', description: '冒险币货币' }, // 修改道具28的名称
  { id: '29', name: '道具29', description: '道具29描述' }
];

// 任务名称映射
const missionNames = {
  '1001': '新手任务',
  '1002': '主线任务',
  '1003': '支线任务',
  // ... 添加更多任务
};

// 技能名称映射
const skillNames = {
  '2001': '基础攻击',
  '2002': '防御技',
  '2003': '特殊技能',
  // ... 添加更多技
};

// 获取任务名称
const getMissionName = (id) => {
  return missionNames[id] || `任务${id}`;
};

// 获取技能名称
const getSkillName = (id) => {
  return skillNames[id] || `技能${id}`;
};

// 活动的技能面板
const activeSkills = ref([]);

// 商店物品列表
const shopItems = computed(() => {
  const shopData = archiveData.value?.shop?.data || {};
  return Object.entries(shopData).map(([id, data]) => ({
    id,
    name: getShopItemName(id),
    ...data
  }));
});

// 获商店物品名称
const getShopItemName = (id) => {
  const shopItemNames = {
    '3001': '体力药水',
    '3002': '经验药水',
    '3003': '技能书',
    // ... 添加更多商品
  };
  return shopItemNames[id] || `商品${id}`;
};

// 修改资源道具列表的计算属性
const resourceItemsList = computed(() => {
  // 确保 archiveData 和其内部属性存在
  if (!archiveData.value?.game?.data?.items) {
    return resourceItems.map(item => ({
      ...item,
      value: 0
    }));
  }

  return resourceItems.map(item => ({
    ...item,
    value: archiveData.value.game.data.items[item.id] || 0
  }));
});

// 修改更新道具值的方法
const updateItemValue = (id, value) => {
  if (!archiveData.value.game.data.items) {
    archiveData.value.game.data.items = {};
  }
  archiveData.value.game.data.items[id] = value;
  
  // 记录修改
  recordModification('game', 'items');
  
  // 更新 jsonStr
  archiveData.value.game.jsonStr = JSON.stringify(archiveData.value.game.data, null, 2);
};

// 添加武器碎片名称映射
const weaponChipNames = {
  '1': '普通碎片',
  '2': '稀有碎片',
  '3': '史诗碎片',
  '4': '传说碎片',
  // 可以根据需要添加更多碎片名称
};

// 添加武器碎片列表的计算属性
const weaponChips = computed(() => {
  const chipData = archiveData.value?.weapon?.data?.chip || {};
  return Object.entries(chipData).map(([id, count]) => ({
    id,
    name: getWeaponChipName(id),
    count
  }));
});

// 添加获取武器碎片名称的方法
const getWeaponChipName = (id) => {
  return weaponChipNames[id] || `碎片${id}`;
};

// 添加更新武器数据的方法
const updateWeaponData = () => {
  if (archiveData.value.weapon) {
    archiveData.value.weapon.jsonStr = JSON.stringify(archiveData.value.weapon.data, null, 2);
  }
};

// 添加备名称映射
const equipmentNames = {
  '3010102': '新手剑',
  '3010303': '精钢剑',
  '3010304': '寒冰剑',
  '3020102': '皮甲',
  '3020403': '精钢甲',
  '3020502': '寒冰甲',
  '3020503': '魔法甲',
  '3030103': '精钢指',
  '3040102': '皮靴',
  '3040203': '精钢靴',
  '3040402': '寒冰靴',
  '3050102': '初级项链',
  // 可以继续添加更多装备名称
  '4012010': '卓越武器',
  '4022010': '卓越护甲',
  '4032010': '卓越戒指',
  '4042010': '卓越项链',
  '4052010': '卓越护符',
  '4062010': '卓越宝物',
};

// 已装备物品列表
const equippedItems = computed(() => {
  const selectData = archiveData.value?.weapon?.data?.select || {};
  return Object.entries(selectData).map(([position, data]) => ({
    position: `装备位 ${position}`,
    id: data.id,
    index: data.index,
    name: equipmentNames[data.id] || `装备${data.id}`
  }));
});

// 背包物品列表
const inventoryItems = computed(() => {
  const instanceData = archiveData.value?.weapon?.data?.instance || {};
  return Object.entries(instanceData).map(([position, data]) => ({
    position: `位置 ${position}`,
    id: data.id,
    name: equipmentNames[data.id] || `备${data.id}`,
    level: data.lv || 0,
    gold: data.gold || 0,
    chip: data.chip || 0
  }));
});

// 卸下装备
const unequipItem = (position, index) => {
  if (archiveData.value?.weapon?.data) {
    const positionId = position.split(' ')[1];
    delete archiveData.value.weapon.data.select[positionId];
    recordModification('weapon', 'select');
    updateWeaponData();
    ElMessage.success('装备卸下');
  }
};

// 装备物品
const equipItem = (position, itemId) => {
  if (archiveData.value?.weapon?.data) {
    const positionId = position.split(' ')[1];
    // 默认装备到位置1，实际应用中可能需要更杂的逻
    archiveData.value.weapon.data.select['1'] = {
      id: itemId,
      index: parseInt(positionId)
    };
    recordModification('weapon', 'select');
    updateWeaponData();
    ElMessage.success('装备已穿戴');
  }
};

// 添加红色套餐状态检查
const hasRedPackage = computed(() => {
  if (!archiveData.value?.weapon?.data?.instance) return false;
  
  // 检查是否已经色装备
  const instances = archiveData.value.weapon.data.instance;
  return Object.values(instances).some(item => {
    const id = String(item.id);
    return id.endsWith('10'); // 检查装备ID是否以10结尾（红色装备的特征）
  });
});

// 修改红色餐处理���数
const handleRedPackage = async () => {
  loading.package = true;
  try {
    if (!archiveData.value?.weapon?.data) {
      throw new Error('武器数据不存在');
    }

    // 确保 instance 对象存在
    if (!archiveData.value.weapon.data.instance) {
      archiveData.value.weapon.data.instance = {};
    }

    // 获取当前最大的位置索引
    const currentPositions = Object.keys(archiveData.value.weapon.data.instance)
      .map(Number)
      .filter(n => !isNaN(n));
    let startIndex = currentPositions.length ? Math.max(...currentPositions) + 1 : 1;

    // 生成30件装备
    for (let i = 0; i < 30; i++) {
      const typeNum = (i % 6) + 1;  // 1-6循环
      const groupNum = Math.floor(i / 6) + 1;  // 每6件增加1
      const equipId = parseInt(`30${typeNum}0${groupNum}10`);

      archiveData.value.weapon.data.instance[startIndex + i] = {
        id: equipId,
        lv: 100,  // 装备等级
        gold: 999999,  // 装备金币
        chip: 999  // 装备碎片
      };
    }

    // 记录修改
    recordModification('weapon', 'instance');
    
    // 更新武器数据
    updateWeaponData();

    // 自动更新游戏
    await handleUpdate();

    ElMessage.success('红色套餐添加成功');
  } catch (error) {
    console.error('红色套餐添加失败:', error);
    ElMessage.error(error.message || '红色套餐添加失败');
  } finally {
    loading.package = false;
  }
};

// 添加红色装备数量的计算属性
const redEquipmentCount = computed(() => {
  if (!archiveData.value?.weapon?.data?.instance) return 0;
  
  return Object.values(archiveData.value.weapon.data.instance)
    .filter(item => String(item.id).endsWith('10'))
    .length;
});

// 修改卓越套装处理函数
const handleExcellentPackage = async () => {
  loading.excellent = true;
  try {
    if (!archiveData.value?.weapon?.data) {
      throw new Error('武器数据不存在');
    }

    // 确保 instance 对象存在
    if (!archiveData.value.weapon.data.instance) {
      archiveData.value.weapon.data.instance = {};
    }

    // 获取当前最大的位置索引
    const currentPositions = Object.keys(archiveData.value.weapon.data.instance)
      .map(Number)
      .filter(n => !isNaN(n));
    let startIndex = currentPositions.length ? Math.max(...currentPositions) + 1 : 1;

    // 生成6件卓越装备
    for (let i = 0; i < 6; i++) {
      const equipId = parseInt(`40${i + 1}2010`);  // 生成装备ID：40 + (1-6) + 2010

      archiveData.value.weapon.data.instance[startIndex + i] = {
        id: equipId,
        index: startIndex + i,  // 添加 index 属性
        lv: 120,    // 装备等级设为120
        gold: 888888,  // 装备金币
        chip: 888    // 装备碎片
      };
    }

    // 记录修改
    recordModification('weapon', 'instance');
    
    // 更新武器数据
    updateWeaponData();

    // 自动更新游戏
    await handleUpdate();

    ElMessage.success('卓越套装添加成功');
  } catch (error) {
    console.error('卓越套装添加失败:', error);
    ElMessage.error(error.message || '卓越套装添加失败');
  } finally {
    loading.excellent = false;
  }
};

// 修改卓越装备检测逻辑
const hasExcellentPackage = computed(() => {
  if (!archiveData.value?.weapon?.data?.instance) return false;
  
  // 检查是否已经有卓越装备
  const instances = archiveData.value.weapon.data.instance;
  return Object.values(instances).some(item => {
    const id = String(item.id);
    // 检查装备ID是否为卓越装备（以40开头且以2010结尾）
    return id.startsWith('40') && id.endsWith('2010');
  });
});

// 添加卓越装备数量计算
const excellentEquipmentCount = computed(() => {
  if (!archiveData.value?.weapon?.data?.instance) return 0;
  
  return Object.values(archiveData.value.weapon.data.instance)
    .filter(item => {
      const id = String(item.id);
      return id.startsWith('40') && id.endsWith('2010');
    })
    .length;
});

// 技能组件弹窗状态
const skillComponentDialog = reactive({
  visible: false,
  selection: []  // 存选中的技能
});

// 技能组件列表
const skillComponents = [
  { id: '5112110', name: '弹射树杈', description: '方块系列技能，可以发射多个树杈' },
  { id: '5104210', name: '火墙', description: '梅花系列技能，创造一道火焰墙' },
  { id: '5113310', name: '鱼骨飞刀', description: '皇后系列技能，发射锋利的飞刀' },
  { id: '5102410', name: '风力场', description: '红心系列技能，产生强大的风场' },
  { id: '5109510', name: '水柱', description: '黑桃系列技能，喷射高压水柱' },
  { id: '5202610', name: '邪能光环', description: '国王系列技能，释放邪恶的光环' },
  { id: '5107110', name: '光弹球', description: '方块系技能，发射能量弹球' },
  { id: '5105110', name: '雷电术', description: '方块系列技能，召唤闪电攻击' },
  { id: '5103110', name: '火球术', description: '方块系列技能，投掷火球' },
  { id: '5101210', name: '风刃', description: '梅花系列技能，释放锋利的风刃' },
  { id: '5110210', name: '冰锥', description: '梅花系列技能，发射尖锐的冰锥' },
  { id: '5204210', name: '爪击', description: '梅花系列技能，进行爪子攻击' },
  { id: '5105310', name: '雷电术', description: '皇后系列技能，强化版雷电攻击' },
  { id: '5114510', name: '丢石头', description: '黑桃系列技能，投掷巨石' },
  { id: '5111610', name: '冰锥环绕', description: '国王系列技能，冰锥环绕保护' }
];

// 显示技能组件弹窗
const showSkillComponentDialog = () => {
  skillComponentDialog.visible = true;
  skillComponentDialog.selection = [];
};

// 添加选中的技能组件
const addSelectedSkillComponents = async () => {
  if (!skillComponentDialog.selection.length) {
    ElMessage.warning('请先选择要添加的技能');
    return;
  }

  try {
    await addSkillComponents(skillComponentDialog.selection);
    skillComponentDialog.visible = false;
    ElMessage.success('选中技能添加成功');
  } catch (error) {
    ElMessage.error(error.message || '技能添加失败');
  }
};

// 添加全部技能件
const addAllSkillComponents = async () => {
  try {
    await addSkillComponents(skillComponents);
    skillComponentDialog.visible = false;
    ElMessage.success('全部技能添加成功');
  } catch (error) {
    ElMessage.error(error.message || '技能添加失败');
  }
};

// 添加技能组件的核心方法
const addSkillComponents = async (components) => {
  if (!archiveData.value?.skillPart?.data) {
    archiveData.value.skillPart = {
      prefix: '',
      data: {
        instance: {}
      },
      jsonStr: '',
      originalValue: null
    };
  }

  // 确保 instance 对象存在
  if (!archiveData.value.skillPart.data.instance) {
    archiveData.value.skillPart.data.instance = {};
  }

  // 获取当前最大的位置索引
  const currentPositions = Object.keys(archiveData.value.skillPart.data.instance)
    .map(Number)
    .filter(n => !isNaN(n));
  let startIndex = currentPositions.length ? Math.max(...currentPositions) + 1 : 1;

  // 添加技能组件
  components.forEach((component, index) => {
    archiveData.value.skillPart.data.instance[startIndex + index] = {
      id: parseInt(component.id)
    };
  });

  // 记录修改
  recordModification('skillPart', 'instance');
  
  // 更新 jsonStr
  archiveData.value.skillPart.jsonStr = JSON.stringify(archiveData.value.skillPart.data, null, 2);

  // 自动更新游戏
  await handleUpdate();
};

// 处理表格选择
const handleSelectionChange = (selection) => {
  skillComponentDialog.selection = selection;
};

// 添加表格引用
const skillTable = ref(null);

// 修改行点击处理方法
const handleRowClick = (row) => {
  if (skillTable.value) {
    skillTable.value.toggleRowSelection(row);
  }
};

// 添加伙伴强化按钮
const handlePartnerUpgrade = async () => {
  try {
    if (!archiveData.value?.partner?.data) {
      // 如果不存在 partner 数据，创建它
      archiveData.value.partner = {
        prefix: '',
        data: {
          partners: {}
        },
        jsonStr: '',
        originalValue: null
      };
    }

    // 确保 partners 对象存在
    if (!archiveData.value.partner.data.partners) {
      archiveData.value.partner.data.partners = {};
    }

    // 为每个伙伴ID设置属性
    partnerIds.forEach(id => {
      archiveData.value.partner.data.partners[id] = {
        id: id.toString(),
        lv: "100",
        chips: "1"
      };
    });

    // 记录修改
    recordModification('partner', 'partners');
    
    // 更新伙伴数据
    archiveData.value.partner.jsonStr = JSON.stringify(archiveData.value.partner.data, null, 2);

    // 自动更新游戏
    await handleUpdate();

    ElMessage.success('伙伴强化成功');
  } catch (error) {
    console.error('伙伴强化失败:', error);
    ElMessage.error(error.message || '伙伴强化失败');
  }
};

// 添加伙伴ID列表
const partnerIds = [
  // 1001-1011
  ...Array.from({length: 11}, (_, i) => (1001 + i)),
  // 1013-1019
  ...Array.from({length: 7}, (_, i) => (1013 + i)),
  // 1021-1023
  ...Array.from({length: 3}, (_, i) => (1021 + i))
];

// 添加伙伴列表的计算属性
const partnersList = computed(() => {
  if (!archiveData.value?.partner?.data?.partners) return [];
  
  return Object.entries(archiveData.value.partner.data.partners).map(([id, data]) => ({
    id,
    ...data,
    unlocked: true // 默认为已解锁状态
  }));
});

// 添加更新伙伴数据的方法
const updatePartnerData = () => {
  if (archiveData.value.partner) {
    archiveData.value.partner.jsonStr = JSON.stringify(archiveData.value.partner.data, null, 2);
  }
};

// 添加技能组件相关的计算属性
const skillPartList = computed(() => {
  const skillData = archiveData.value?.skillPart?.data?.instance || {};
  return Object.entries(skillData).map(([position, data]) => ({
    position,
    id: data.id,
    name: getSkillPartName(data.id)
  }));
});

// 添加获取技能名称的方法
const getSkillPartName = (id) => {
  const skill = skillComponents.find(s => parseInt(s.id) === id);
  return skill ? `${skill.name} (${skill.description})` : `技能${id}`;
};

// 添加技能组件相关的方法
const editSkillPart = (skill) => {
  // TODO: 实现编辑功能
  ElMessage.info('编辑功能开发中');
};

const removeSkillPart = async (position) => {
  if (archiveData.value?.skillPart?.data?.instance) {
    delete archiveData.value.skillPart.data.instance[position];
    recordModification('skillPart', 'instance');
    
    // 更新 jsonStr
    archiveData.value.skillPart.jsonStr = JSON.stringify(archiveData.value.skillPart.data, null, 2);
    
    // 自动更新游戏
    await handleUpdate();
    
    ElMessage.success('技能组件已移除');
  }
};

const addSkillPart = () => {
  if (!archiveData.value?.skillPart?.data?.instance) {
    archiveData.value.skillPart.data = {
      instance: {}
    };
  }
  
  // 获取当前最大位置
  const positions = Object.keys(archiveData.value.skillPart.data.instance)
    .map(Number)
    .filter(n => !isNaN(n));
  const newPosition = positions.length ? Math.max(...positions) + 1 : 1;
  
  // 添加新技能组件
  archiveData.value.skillPart.data.instance[newPosition] = {
    id: 5000301 // 默认技能ID
  };
  
  recordModification('skillPart', 'instance');
  ElMessage.success('技能组件已添加');
};

// 添加删除背包装备的方法
const deleteInventoryItem = async (position) => {
  try {
    if (!archiveData.value?.weapon?.data?.instance) {
      throw new Error('装备数据不存在');
    }

    // 从背包中删除装备
    delete archiveData.value.weapon.data.instance[position.split(' ')[1]];

    // 记录修改
    recordModification('weapon', 'instance');
    
    // 更新武器数据
    updateWeaponData();

    // 自动更新游戏
    await handleUpdate();

    ElMessage.success('装备已删除');
  } catch (error) {
    console.error('删除装备失败:', error);
    ElMessage.error(error.message || '删除装备失败');
  }
};
</script>

<style scoped>
.cat-survival {
  padding: 20px;
  height: 100vh;
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
  box-sizing: border-box;
}

.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  height: auto;
  min-height: calc(100vh - 40px);
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: -20px -20px 24px -20px;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: white;
  z-index: 1;
}

.title .el-icon {
  font-size: 24px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 8px;
  backdrop-filter: blur(4px);
}

.status-tag {
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  font-weight: 500;
  z-index: 1;
}

.input-section {
  margin-bottom: 24px;
}

.form-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--el-text-color-regular);
  margin-bottom: 8px;
  width: 100%;
}

.form-label .el-icon {
  font-size: 16px;
}

.form-actions {
  margin-left: auto;
  display: flex;
  align-items: center;
  gap: 4px;
}

:deep(.el-divider--vertical) {
  height: 16px;
  margin: 0 4px;
}

:deep(.el-button--link) {
  height: 20px;
  padding: 0 4px;
}

:deep(.el-button--link .el-icon) {
  font-size: 14px;
}

.action-section {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  flex-wrap: wrap; /* 允许按钮换行 */
}

.action-button {
  flex: 1;
  min-width: 120px; /* 设置最小宽度 */
  max-width: 200px; /* 设置最大宽度 */
  height: 40px;
  font-weight: 500;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .action-section {
    gap: 8px; /* 减小按钮间距 */
  }
  
  .action-button {
    flex: 1 1 calc(50% - 4px); /* 在移动端每行显示两个按钮 */
    min-width: 0; /* 移除最小宽度限制 */
    max-width: none; /* 移除最大宽度限制 */
  }
}

/* 超小屏幕适配 */
@media screen and (max-width: 480px) {
  .action-button {
    flex: 1 1 100%; /* 在超小屏幕上每行显示一个按钮 */
  }
}

.history-section {
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.game-data-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid var(--el-border-color-lighter);
  height: auto;
  overflow: visible;
}

.json-data {
  margin-top: 16px;
  height: auto;
  overflow: visible;
}

.el-descriptions {
  margin: 16px 0;
}

:deep(.el-tabs__content) {
  padding: 20px;
  height: auto !important;
  max-height: 70vh;
  overflow-y: auto !important;
}

:deep(.el-descriptions__cell) {
  padding: 12px 16px;
}

:deep(.el-tag) {
  font-family: monospace;
  word-break: break-all;
  white-space: pre-wrap;
  height: auto;
  padding: 4px 8px;
}

/* 适配移动端 */
@media screen and (max-width: 768px) {
  :deep(.el-descriptions) :deep(.el-descriptions__cell) {
    padding: 8px;
  }
  
  :deep(.el-tabs__content) {
    padding: 12px;
  }
}

.archive-content {
  padding: 16px 0;
  height: auto;
  overflow: visible;
}

.json-editor {
  margin-top: 16px;
  max-height: none;
}

:deep(.el-collapse-item__header) {
  font-size: 14px;
  font-weight: 500;
}

:deep(.el-tag) {
  margin-left: 8px;
}

:deep(.el-textarea__inner) {
  font-family: monospace;
  font-size: 12px;
  line-height: 1.5;
  height: auto !important;
  max-height: none !important;
}

.cat-survival::-webkit-scrollbar,
:deep(.el-tabs__content)::-webkit-scrollbar,
:deep(.el-textarea__inner)::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.cat-survival::-webkit-scrollbar-thumb,
:deep(.el-tabs__content)::-webkit-scrollbar-thumb,
:deep(.el-textarea__inner)::-webkit-scrollbar-thumb {
  background: var(--el-scrollbar-bg-color);
  border-radius: 4px;
}

.cat-survival::-webkit-scrollbar-track,
:deep(.el-tabs__content)::-webkit-scrollbar-track,
:deep(.el-textarea__inner)::-webkit-scrollbar-track {
  background: transparent;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .cat-survival {
    padding: 12px;
  }

  :deep(.el-tabs__content) {
    max-height: 60vh;
  }
}

.el-form-item {
  margin-bottom: 20px;
}

.el-input-number {
  width: 100%;
}

.el-descriptions {
  margin: 16px 0;
}

.el-collapse-item {
  margin-bottom: 8px;
}

:deep(.el-table) {
  margin: 16px 0;
}

/* 修改表格容器样式 */
.table-wrapper {
  position: relative;
  margin: 16px 0;
  overflow: hidden; /* 防止滚动条闪烁 */
}

/* 优化表格渲染 */
:deep(.el-table__inner-wrapper) {
  height: auto !important;
  transform: translateZ(0); /* 开启硬件加速 */
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  height: auto !important;
  position: static !important;
}

/* 确保表格内容稳定 */
:deep(.el-table__body) {
  width: 100% !important;
}

/* 移除可能导致问题的样式 */
:deep(.el-table),
:deep(.el-table__header-wrapper),
:deep(.el-table__fixed),
:deep(.el-table__fixed-right) {
  height: auto !important;
  position: static !important;
}

.prefix-section {
  margin: 16px 0;
  padding: 12px 16px;
  background-color: var(--el-fill-color-light);
  border-radius: 4px;
}

.prefix-title {
  font-size: 14px;
  color: var(--el-text-color-secondary);
  line-height: 32px;
  font-weight: 500;
}

.prefix-content {
  font-family: monospace;
}

:deep(.prefix-tag) {
  width: 100%;
  font-family: monospace;
  font-size: 12px;
  padding: 8px 12px;
  height: 32px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1;
  text-align: left;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .prefix-section {
    padding: 8px 12px;
  }
  
  .prefix-title {
    margin-bottom: 8px;
  }
  
  :deep(.el-row) {
    margin: 0 !important;
  }
  
  :deep(.el-col) {
    padding: 0 !important;
  }
}

.equipment-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
  height: calc(100vh - 300px);
  overflow-y: auto;
  padding-right: 8px;
}

.section-block {
  border: 1px solid var(--el-border-color-lighter);
  border-radius: 4px;
  padding: 16px;
  background: white;
}

.block-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.block-title .el-icon {
  font-size: 18px;
  color: var(--el-color-primary);
}

.table-wrapper {
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 16px;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  max-height: none !important;
}

.equipment-section::-webkit-scrollbar {
  width: 6px;
}

.equipment-section::-webkit-scrollbar-thumb {
  background-color: var(--el-border-color);
  border-radius: 3px;
}

.equipment-section::-webkit-scrollbar-track {
  background-color: var(--el-fill-color-lighter);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: var(--el-border-color);
  border-radius: 3px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 在 style 部添加或修改 */
.inventory-table {
  /* 设置固高度 */
  height: 400px;
  /* 允许内容溢出时显示滚动条 */
  overflow: auto;
}

.inventory-table :deep(.el-table) {
  /* 确保表格占满容器 */
  height: 100%;
}

.inventory-table :deep(.el-table__body-wrapper) {
  /* 允许垂直滚动 */
  overflow-y: scroll !important;
  /* 设置最大高度 */
  max-height: 400px;
}

/* 自定义滚动条样式 */
.inventory-table::-webkit-scrollbar,
.inventory-table :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

.inventory-table::-webkit-scrollbar-thumb,
.inventory-table :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: var(--el-border-color);
  border-radius: 3px;
}

.inventory-table::-webkit-scrollbar-track,
.inventory-table :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 确保固定列正常显示 */
.inventory-table :deep(.el-table__fixed) {
  height: 100% !important;
}

.inventory-table :deep(.el-table__fixed-right) {
  height: 100% !important;
  right: 6px; /* 为滚动条留出间 */
}

/* 添加红色装备数量标签的样式 */
.red-equipment-count {
  margin: 0 4px;
  font-weight: bold;
  font-size: 14px;
}

/* 修改 block-title 样式以适新的局 */
.block-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  flex-wrap: wrap;
}

/* 添加卓越装备数量标签样式 */
.excellent-equipment-count {
  margin: 0 4px;
  font-weight: bold;
  font-size: 14px;
}

/* 添加技能组件相关样式 */
.skill-component-list {
  margin: 16px 0;
  /* 设置固定高度 */
  height: 400px;
  /* 允许内容溢出时显示滚动条 */
  overflow: auto;
}

/* 自定义滚动条样式 */
.skill-component-list::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.skill-component-list::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.skill-component-list::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 确保表格内容可以正常滚动 */
.skill-component-list :deep(.el-table__body-wrapper) {
  overflow-y: scroll !important;
  max-height: 400px;
}

/* 表格滚动条样式 */
.skill-component-list :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

.skill-component-list :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: var(--el-border-color);
  border-radius: 3px;
}

.skill-component-list :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 确保固定列正常显示 */
.skill-component-list :deep(.el-table__fixed) {
  height: 100% !important;
}

.skill-component-list :deep(.el-table__fixed-right) {
  height: 100% !important;
  right: 6px; /* 为滚动条留出空间 */
}

/* 优化弹窗样式 */
:deep(.skill-dialog .el-dialog__body) {
  padding: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* 修改弹窗相关样式 */
:deep(.skill-dialog) {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  margin: 0;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  :deep(.skill-dialog) {
    width: 90% !important; /* 在移动端占据更大宽度 */
  }
}

.table-container {
  margin: 16px 0;
  height: 400px;
  overflow: auto;
}

/* 自������滚动���样式 */
.table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.table-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.table-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 添加游戏选择器样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: -20px -20px 24px -20px;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.game-selector {
  margin: 0 20px;
  flex: 1;
  max-width: 200px;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-select .el-input__wrapper) {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  backdrop-filter: blur(4px);
}

:deep(.el-select .el-input__inner) {
  color: white;
}

:deep(.el-select .el-input .el-select__caret) {
  color: white;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .header-section {
    flex-direction: column;
    gap: 12px;
    padding: 16px;
  }

  .game-selector {
    max-width: 100%;
    width: 100%;
    margin: 8px 0;
  }
}

/* 修改资源道具表格容器样式 */
.resource-table-container {
  height: 400px;
  overflow: auto;
}

/* 确保表格内容可以正常滚动 */
.resource-table-container :deep(.el-table__body-wrapper) {
  overflow-y: scroll !important;
  height: auto !important;
}

/* 自定��滚动条样式 */
.resource-table-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.resource-table-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.resource-table-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 修改 tabs 内容区域的样式 */
:deep(.el-tabs__content) {
  height: auto !important;
  overflow: visible !important;
}

/* 确保表格固定列正常显示 */
.resource-table-container :deep(.el-table__fixed) {
  height: 100% !important;
}

.resource-table-container :deep(.el-table__fixed-right) {
  height: 100% !important;
  right: 6px; /* 为滚动条留出空间 */
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .resource-table-container {
    height: 300px; /* 在移动端减小高度 */
  }
}

.skill-part-container {
  height: 500px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  overflow-y: auto; /* 添加垂直滚动 */
}

.action-bar {
  padding: 16px 0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 自定义滚动条样式 */
.skill-part-container::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.skill-part-container::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.skill-part-container::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 确保表格可以正常滚动 */
.skill-part-container :deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  max-height: 400px !important; /* 设置表格最大高度 */
}

/* 表格滚动条样式 */
.skill-part-container :deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 6px;
  height: 6px;
}

.skill-part-container :deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: var(--el-border-color);
  border-radius: 3px;
}

.skill-part-container :deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: var(--el-fill-color-lighter);
  border-radius: 3px;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .skill-part-container {
    height: 400px; /* 在移动端减小高度 */
  }
  
  .skill-part-container :deep(.el-table__body-wrapper) {
    max-height: 300px !important; /* 在移动端减小表格最大高度 */
  }
}
</style> 