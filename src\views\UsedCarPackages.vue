<template>
  <div class="used-car-packages">
    <div class="header-section">
      <div class="title-wrapper">
        <h2 class="page-title">
          <el-icon><Van /></el-icon>
          二手车模拟器套餐
        </h2>
        <div class="title-decoration"></div>
      </div>
      <div class="notice">
        <div class="tags-container">
          <el-tag type="success" effect="dark" class="support-tag glow">
            <el-icon><Check /></el-icon>
            V小程序支持
          </el-tag>
          <el-tag type="warning" effect="dark" class="support-tag glow">
            <el-icon><DataLine /></el-icon>
            保留游戏进度
          </el-tag>
        </div>
      </div>
    </div>

    <div class="packages-container">
      <div v-for="(pkg, index) in packages" 
           :key="index" 
           class="package-card"
           :class="{ 'popular': pkg.isPopular }">
        <div class="package-header">
          <span class="package-name">
            <el-icon class="rotating"><Present /></el-icon>
            {{ pkg.name }}
          </span>
          <el-tag v-if="pkg.isPopular" 
                  type="danger" 
                  effect="dark" 
                  size="small"
                  class="popular-tag shine">
            <el-icon><Star /></el-icon>
            热门
          </el-tag>
        </div>
        
        <div class="package-price">
          <small>¥</small>
          <span class="amount">{{ pkg.price }}</span>
        </div>
        
        <div class="benefits">
          <div class="benefit-item">
            <div class="benefit-content">
              <el-icon class="pulse"><Check /></el-icon>
              <span class="benefit-text">{{ pkg.benefits.join(' + ') }}</span>
            </div>
          </div>
        </div>
        
        <div class="decoration-corner top-left"></div>
        <div class="decoration-corner top-right"></div>
        <div class="decoration-corner bottom-left"></div>
        <div class="decoration-corner bottom-right"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { 
  Check, 
  DataLine, 
  Van, 
  Present,
  Star 
} from '@element-plus/icons-vue'

const packages = ref([
  {
    name: '基础套餐',
    price: 5,
    benefits: ['6万钻石'],
    isPopular: false
  },
  {
    name: '超值套餐',
    price: 15,
    benefits: ['无限绿钞', '6万钻石'],
    isPopular: true
  },
  {
    name: 'VIP套餐',
    price: 20,
    benefits: ['无限绿钞', '6万钻石', 'VIP特权'],
    isPopular: false
  }
])
</script>

<style scoped>
.used-car-packages {
  padding: 10px;
  max-width: 100%;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.title-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 15px;
  width: 100%;
}

.title-decoration {
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: linear-gradient(90deg, transparent, #409EFF, transparent);
}

.page-title {
  font-size: 20px;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.1);
  width: 100%;
  text-align: center;
}

.page-title .el-icon {
  font-size: 28px;
  color: #409EFF;
  filter: drop-shadow(0 2px 4px rgba(64,158,255,0.2));
}

.support-tag.glow {
  box-shadow: 0 0 10px rgba(64,158,255,0.2);
  transition: all 0.3s ease;
}

.support-tag.glow:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64,158,255,0.3);
}

.package-card {
  background: white;
  border-radius: 16px;
  padding: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.05);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1px solid rgba(255,255,255,0.1);
  backdrop-filter: blur(10px);
  margin-bottom: 8px;
}

.package-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0,0,0,0.1);
}

.package-card.popular {
  background: linear-gradient(135deg, #fff5f5 0%, #fff 100%);
  border: 2px solid #ff9999;
}

.decoration-corner {
  position: absolute;
  width: 12px;
  height: 12px;
  border: 2px solid #409EFF;
  opacity: 0.3;
}

.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.rotating {
  animation: rotate 6s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); }
  100% { transform: scale(1); }
}

.shine {
  position: relative;
  overflow: hidden;
}

.shine::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to right,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.3) 50%,
    rgba(255,255,255,0) 100%
  );
  transform: rotate(45deg);
  animation: shine 3s infinite;
}

@keyframes shine {
  0% { transform: translateX(-100%) rotate(45deg); }
  100% { transform: translateX(100%) rotate(45deg); }
}

.package-price {
  text-align: center;
  margin: 8px 0;
  position: relative;
}

.amount {
  font-size: 32px;
  font-weight: bold;
  background: linear-gradient(45deg, #f56c6c, #ff9999);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-shadow: 0 2px 4px rgba(0,0,0,0.15);
}

.package-price small {
  font-size: 16px;
  font-weight: bold;
}

.benefits {
  margin: 8px 0;
  padding: 5px 0;
}

.benefit-item {
  padding: 10px;
  border-radius: 10px;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: all 0.3s ease;
}

.benefit-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.benefit-text {
  font-size: 22px;
  font-weight: 600;
  color: #303133;
  text-shadow: 0 1px 1px rgba(0,0,0,0.1);
  white-space: nowrap;
}

.benefit-item .el-icon {
  color: #67c23a;
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(103,194,58,0.3));
}

.benefit-item:hover {
  background: rgba(64,158,255,0.08);
  transform: translateX(5px);
}

.package-name {
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  display: flex;
  align-items: center;
  gap: 8px;
  text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.notice {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 10px;
}

.tags-container {
  display: inline-flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

.support-tag {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
}

.support-tag .el-icon {
  margin-right: 2px;
}

/* 专门针对手机端的优化 */
@media (max-width: 767px) {
  .used-car-packages {
    padding: 8px 8px 0 8px;
    height: calc(100vh - 60px); /* 减去头部导航的高度 */
    overflow-y: auto;
  }
  
  .header-section {
    text-align: center;
    margin-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: sticky;
    top: 0;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%);
    z-index: 10;
    padding: 5px 0;
  }

  .packages-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    gap: 8px;
  }

  .package-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
    margin-bottom: 5px;
    max-height: 30vh;
  }

  .benefit-item {
    margin: 3px 0;
    padding: 5px 8px;
  }

  .support-tag {
    padding: 2px 6px;
    font-size: 12px;
  }

  .title-decoration {
    height: 2px;
    width: 60px;
  }

  /* 确保最后一个卡片底部有足够空间 */
  .package-card:last-child {
    margin-bottom: 10px;
  }

  .benefit-text {
    font-size: 20px;
  }

  .benefit-item .el-icon {
    font-size: 20px;
  }

  .tags-container {
    gap: 6px;
  }
}

/* 针对特别小的屏幕 */
@media (max-height: 600px) {
  .package-card {
    padding: 8px;
    max-height: 28vh;
  }

  .benefit-text {
    font-size: 18px;
  }

  .amount {
    font-size: 28px;
  }

  .package-name {
    font-size: 18px;
  }

  .benefit-text {
    font-size: 18px;
  }
  
  .benefit-content {
    gap: 8px;
  }
}
</style> 