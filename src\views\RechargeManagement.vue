<template>
  <div class="recharge-management">
    <router-view></router-view>  <!-- 确保这行存在 -->
  </div>
</template>

<script>
import { ref, onMounted, nextTick, markRaw } from 'vue'
import { User, Timer } from '@element-plus/icons-vue'
import OperationHistory from '@/components/RechargeHistory.vue'
import GameIdForm from '@/components/GameIdForm.vue'

export default {
  name: 'RechargeManagement',
  components: {
    User, Timer,
    OperationHistory, GameIdForm
  },
  setup() {
    const cards = ref([
      {
        id: 0,
        title: '游戏 ID',
        icon: markRaw(User),
        component: markRaw(GameIdForm),
        data: {},
        visible: true
      },
      {
        id: 1,
        title: '操作历史',
        icon: markRaw(Timer),
        component: markRaw(OperationHistory),
        data: [],
        visible: true
      }
    ])

    const cardHeight = ref('auto')

    const updateOperationHistory = (newHistory) => {
      console.log('Updating operation history:', newHistory);
      const historyCard = cards.value.find(c => c.id === 1)
      if (historyCard) {
        historyCard.data = [...newHistory, ...historyCard.data].slice(0, 50)  // 限制历史记录数量
      }
      nextTick(() => {
        updateCardHeight()
      })
    }

    const updateCardHeight = () => {
      const gameIdCard = document.querySelector('.card-wrapper:first-child .el-card__body')
      if (gameIdCard) {
        cardHeight.value = `${gameIdCard.offsetHeight}px`
      }
    }

    onMounted(() => {
      window.addEventListener('resize', updateCardHeight)
    })

    return {
      cards,
      cardHeight,
      updateOperationHistory
    }
  }
}
</script>

<style scoped>
.recharge-management {
  padding: 20px;
}

.card-container {
  display: flex;
  gap: 20px;
  width: 100%;
}

.card-wrapper {
  flex: 1;
}

.box-card {
  width: 100%;
  height: 100%; /* 确保卡片占满整个高度 */
}

.history-card {
  width: 45%; /* 增加操作历史卡片的宽度 */
  flex-shrink: 0; /* 防止操作历史卡片被压缩 */
}

.card-header {
  display: flex;
  align-items: center;
}

.card-header .el-icon {
  margin-right: 8px;
}

@media (max-width: 768px) {
  .card-container {
    flex-direction: column;
  }
  
  .card-wrapper,
  .history-card {
    width: 100%;
  }
}
</style>
