<template>
  <div class="gold-recharge">
    <h2>金币充值</h2>
    <RechargeForm @recharge="handleRecharge" :rechargeType="'gold'" />
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import RechargeForm from '@/components/RechargeForm.vue'

export default {
  name: 'GoldRecharge',
  components: {
    RechargeForm
  },
  methods: {
    ...mapActions('recharge', ['recharge']),
    async handleRecharge(rechargeInfo) {
      try {
        await this.recharge(rechargeInfo)
        console.log('金币充值成功:', rechargeInfo)
      } catch (error) {
        console.error('金币充值失败:', error)
      }
    }
  }
}
</script>
