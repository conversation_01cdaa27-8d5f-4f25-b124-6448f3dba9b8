<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="cultivator-simulator-packages">
      <div class="page-header">
        <h2>散修生活模拟器套餐</h2>
        <div class="subtitle">WX小程序 | 保留进度 | 正常联网</div>
      </div>

      <div class="package-list">
        <!-- 套餐1 -->
        <div class="package-card">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon icon-starter">
                <Collection />
              </el-icon>
              <span class="package-name">套餐一</span>
            </div>
            <div class="price-tag">¥10</div>
          </div>

          <div class="package-content">
            <div class="benefits-section">
              <div class="benefit-item">
                <span class="highlight-amount">无限钻石+金币</span>
                <span class="separator">+</span>
                <span class="other-benefits">锻造石 | 龙蛋 | VIP | 攻速满级</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 套餐2 -->
        <div class="package-card">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon icon-advanced">
                <Suitcase />
              </el-icon>
              <span class="package-name">套餐二</span>
            </div>
            <div class="price-tag">¥15</div>
          </div>

          <div class="package-content">
            <div class="benefits-section">
              <div class="benefit-item">
                <span class="highlight-amount">包含套餐一</span>
                <span class="separator">+</span>
                <span class="other-benefits">全部装备 | 宠物碎片 | 锻造满级</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 套餐3 -->
        <div class="package-card highlight">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon icon-supreme">
                <Avatar />
              </el-icon>
              <span class="package-name">套餐三</span>
              <span class="recommend-badge">推荐</span>
            </div>
            <div class="price-tag">¥20</div>
          </div>

          <div class="package-content">
            <div class="benefits-section">
              <div class="benefit-item">
                <span class="highlight-amount">包含套餐一+二</span>
                <span class="separator">+</span>
                <span class="other-benefits">火冰符文 | 宠物装备 | 邀请满级</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-notice">
        无需扫码，保留进度
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup>
import {
  Avatar, Star, InfoFilled, Collection, Timer, Trophy, Medal,
  Coin, GoldMedal, User, PictureRounded, Sunny, Suitcase,
  Check, Monitor, Opportunity, MagicStick
} from '@element-plus/icons-vue'
</script>

<style scoped>
.cultivator-simulator-packages {
  padding: 10px;
  background: #f8f9ff;
  min-height: 100%;
  max-height: 100vh;
  display: flex;
  flex-direction: column;
}

.page-header {
  text-align: center;
  margin-bottom: 8px;
  color: #303133;
}

.page-header h2 {
  font-size: 18px;
  margin: 0 0 4px 0;
  background: linear-gradient(45deg, #0D7377, #14BDAC);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  color: #606266;
  font-size: 12px;
  margin-bottom: 6px;
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 8px;
}

.package-card {
  background: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.package-card.highlight {
  border: 1px solid #FF9500;
  background: linear-gradient(to right, #ffffff, #fff8f0);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.name-with-icon {
  display: flex;
  align-items: center;
  gap: 5px;
  position: relative;
}

.package-icon {
  font-size: 16px;
  padding: 4px;
  border-radius: 6px;
}

.icon-starter {
  background: #e1f3ef;
  color: #2EB086;
}

.icon-advanced {
  background: #e1f5f7;
  color: #0D7377;
}

.icon-supreme {
  background: linear-gradient(135deg, #FF9500, #FFBB5C);
  color: white;
}

.package-name {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
}

.price-tag {
  font-size: 16px;
  font-weight: bold;
  color: #0D7377;
  background: rgba(13, 115, 119, 0.1);
  padding: 2px 6px;
  border-radius: 4px;
}

.recommend-badge {
  background: #E74646;
  color: white;
  padding: 1px 4px;
  border-radius: 4px;
  font-size: 10px;
  margin-left: 5px;
}

.package-content {
  display: flex;
  flex-direction: column;
}

.benefits-section {
  flex: 1;
}

.benefit-item {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 12px;
  white-space: nowrap;
  overflow: hidden;
}

.highlight-amount {
  color: #FF9500;
  font-weight: bold;
}

.separator {
  margin: 0 4px;
  color: #dcdfe6;
}

.other-benefits {
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
}

.package-card.highlight .highlight-amount {
  background: linear-gradient(45deg, #FF9500, #FFBB5C);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.bottom-notice {
  text-align: center;
  font-size: 12px;
  color: #606266;
  padding: 8px 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  font-weight: bold;
}

/* 添加滚动条样式 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
}

@media (max-height: 600px) {
  .page-header {
    margin-bottom: 4px;
  }

  .package-list {
    gap: 6px;
    margin-bottom: 6px;
  }

  .package-card {
    padding: 8px;
  }

  .package-header {
    margin-bottom: 4px;
  }

  .package-name {
    font-size: 13px;
  }

  .price-tag {
    font-size: 14px;
  }

  .benefit-item {
    font-size: 11px;
  }
}
</style>
