<template>
  <div class="game-id-form">
    <el-form :model="formData" label-width="100px">
      <!-- 修改游戏ID输入区域，添加折叠功能 -->
      <el-form-item label="游戏ID">
        <div class="input-group" :class="{ 'collapsed': isCollapsed }">
          <el-input v-model="formData.gameId" type="textarea" :rows="isCollapsed ? 1 : 8" placeholder="请输入游戏ID"
            class="game-id-input" style="width: 600px;" resize="none" />
          <el-button type="danger" :icon="Delete" circle class="clear-button" @click="clearGameId"
            :disabled="!formData.gameId" title="清空输入" />
        </div>
      </el-form-item>

      <el-form-item>
        <el-button type="primary" :disabled="!formData.gameId" @click="handleDecode">
          <el-icon>
            <Document />
          </el-icon>
          解释数据
        </el-button>
        <el-button type="success" :disabled="!formData.gameId" @click="handleUpdate">
          <el-icon>
            <RefreshRight />
          </el-icon>
          更新数据
        </el-button>
        <div class="help-text" style="margin-top: 10px; padding: 8px 12px; background-color: #fef0f0; border-radius: 4px; border: 1px solid #fde2e2;">
          <i class="el-icon-warning" style="color: #f56c6c; margin-right: 8px;"></i>
          <span style="color: #f56c6c; font-size: 14px;">
            需要进游戏 打一关 查看抓包数据
            <el-link type="danger" href="https://mini.fattoy.cn:8087/jsfs" target="_blank" style="margin: 0 4px;">
              https://mini.fattoy.cn:8087/jsfs
            </el-link>
            中的 <code style="background: #fff3f3; padding: 2px 4px; border-radius: 3px;">NMYKch2txExkAkbtxWxkQTxs</code> 这个开头的数据
          </span>
        </div>
      </el-form-item>

      <!-- 修改标签页结构 -->
      <el-tabs v-if="decodedData" v-model="activeTab" type="border-card" class="data-tabs">
        <!-- 基本信息标签页 -->
        <el-tab-pane name="基本信息" label="基本信息">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="项目代码">{{ decodedData.pj }}</el-descriptions-item>
                <el-descriptions-item label="平台类">{{ decodedData.pt }}</el-descriptions-item>
                <el-descriptions-item label="登录令牌">{{ decodedData.lt }}</el-descriptions-item>
                <el-descriptions-item label="版本">{{ decodedData.v }}</el-descriptions-item>
                <el-descriptions-item label="状态">{{ decodedData.st }}</el-descriptions-item>
                <el-descriptions-item label="登录平台">{{ decodedData.lpt }}</el-descriptions-item>
              </el-descriptions>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 能量管理标签页 -->
        <el-tab-pane name="能量管理" label="能量管理" v-if="decodedData?.d?.EnergyMgr">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <el-descriptions :column="1" border class="energy-descriptions">
                <el-descriptions-item v-for="(value, key) in decodedData.d.EnergyMgr" :key="key"
                  :label="key === '4' ? '体力' : `能量${key}`">
                  <template v-if="value && typeof value === 'object' && value.isDecoded">
                    <div class="value-container">
                      <el-input-number v-model="value.displayValue" :min="0" :max="key === '4' ? 200 : 999999"
                        controls-position="right" size="small" class="decoded-input" />
                      <div class="value-info">
                        <span class="key-label">密钥: {{ value.k }}</span>
                        <el-tag type="danger" size="small" effect="dark">已解码</el-tag>
                        <el-tag v-if="value.displayValue !== value.originalValue" type="warning" size="small"
                          effect="dark">已修改</el-tag>
                      </div>
                    </div>
                  </template>
                  <span v-else>{{ value }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 经济管理标签页 -->
        <el-tab-pane name="经济数据" label="经济数据" v-if="decodedData.d?.EconomicMgr">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <!-- 添加手动添加英雄碎片按钮 -->
              <div class="section-title">
                <span>操作</span>
                <el-button type="primary" size="small" @click="handleAddHeroFragments" class="add-button">
                  <el-icon><Plus /></el-icon>
                  添加英雄碎片
                </el-button>
              </div>

              <!-- 主要资源区域 -->
              <div class="section-title">主要资源</div>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="金币" v-if="decodedData.d.EconomicMgr['4']">
                  <div class="value-container">
                    <el-input-number v-model="decodedData.d.EconomicMgr['4'].displayValue" :min="0" :max="999999"
                      controls-position="right" size="small" class="decoded-input" />
                    <div class="value-info">
                      <span class="key-label">密钥: {{ decodedData.d.EconomicMgr['4'].k }}</span>
                      <el-tag type="danger" size="small" effect="dark"
                        v-if="decodedData.d.EconomicMgr['4'].isDecoded">已解码</el-tag>
                      <el-tag type="warning" size="small" effect="dark"
                        v-if="decodedData.d.EconomicMgr['4'].displayValue !== decodedData.d.EconomicMgr['4'].originalValue">已修改</el-tag>
                    </div>
                  </div>
                </el-descriptions-item>

                <el-descriptions-item label="钻石" v-if="decodedData.d.EconomicMgr['5']">
                  <div class="value-container">
                    <el-input-number v-model="decodedData.d.EconomicMgr['5'].displayValue" :min="0" :max="999999"
                      controls-position="right" size="small" class="decoded-input" />
                    <div class="value-info">
                      <span class="key-label">密钥: {{ decodedData.d.EconomicMgr['5'].k }}</span>
                      <el-tag type="danger" size="small" effect="dark"
                        v-if="decodedData.d.EconomicMgr['5'].isDecoded">已解码</el-tag>
                      <el-tag type="warning" size="small" effect="dark"
                        v-if="decodedData.d.EconomicMgr['5'].displayValue !== decodedData.d.EconomicMgr['5'].originalValue">已修改</el-tag>
                    </div>
                  </div>
                </el-descriptions-item>
              </el-descriptions>

              <!-- 其他资源区域 -->
              <div class="section-title">其他资源</div>
              <el-descriptions :column="2" border>
                <el-descriptions-item label="免广卷礼盒" v-if="decodedData.d.EconomicMgr['6']">
                  <div class="value-container">
                    <el-input-number v-model="decodedData.d.EconomicMgr['6'].displayValue" :min="0" :max="999999"
                      controls-position="right" size="small" class="decoded-input" />
                    <div class="value-info">
                      <span class="key-label">密钥: {{ decodedData.d.EconomicMgr['6'].k }}</span>
                      <el-tag type="danger" size="small" effect="dark"
                        v-if="decodedData.d.EconomicMgr['6'].isDecoded">已解码</el-tag>
                      <el-tag type="warning" size="small" effect="dark"
                        v-if="decodedData.d.EconomicMgr['6'].displayValue !== decodedData.d.EconomicMgr['6'].originalValue">已修改</el-tag>
                    </div>
                  </div>
                </el-descriptions-item>

                <el-descriptions-item v-for="(value, key) in getOtherEconomicData()" :key="key"
                  :label="getEconomyLabel(key)">
                  <div class="value-container">
                    <el-input-number v-model="value.displayValue" :min="0" :max="999999" controls-position="right"
                      size="small" class="decoded-input" />
                    <div class="value-info">
                      <span class="key-label">密钥: {{ value.k }}</span>
                      <el-tag type="danger" size="small" effect="dark" v-if="value.isDecoded">已解码</el-tag>
                      <el-tag type="warning" size="small" effect="dark"
                        v-if="value.displayValue !== value.originalValue">已修改</el-tag>
                    </div>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 卡牌管理标签页 -->
        <el-tab-pane name="卡牌数据" label="卡牌数据" v-if="decodedData.d?.CardMgr">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <el-table :data="getCardList(decodedData.d.CardMgr[4])" border>
                <el-table-column prop="cardId" label="卡牌ID" width="180" />
                <el-table-column prop="level" label="等级" width="120">
                  <template #default="scope">
                    <el-input-number v-model="scope.row.level" :min="1" :max="99" size="small" />
                  </template>
                </el-table-column>
                <el-table-column prop="state" label="状态" width="120">
                  <template #default="scope">
                    <el-select v-model="scope.row.state" size="small">
                      <el-option :value="0" label="未获得" />
                      <el-option :value="1" label="已获得" />
                      <el-option :value="2" label="已装备" />
                    </el-select>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 关卡管理标签页 -->
        <el-tab-pane name="关卡数据" label="关卡数据" v-if="decodedData?.d?.StageMgr">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <el-descriptions :column="2" border class="stage-descriptions">
                <el-descriptions-item v-for="(value, key) in decodedData.d.StageMgr" :key="key" :label="`关卡${key}`">
                  <template v-if="value && typeof value === 'object' && value.isDecoded">
                    <div class="value-container">
                      <el-input-number v-model="value.displayValue" :min="0" :max="999999" controls-position="right"
                        size="small" class="decoded-input" />
                      <div class="value-info">
                        <span class="key-label">密钥: {{ value.k }}</span>
                        <el-tag type="danger" size="small" effect="dark">已解码</el-tag>
                        <el-tag v-if="value.displayValue !== value.originalValue" type="warning" size="small"
                          effect="dark">已修改</el-tag>
                      </div>
                    </div>
                  </template>
                  <span v-else>{{ value }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 活动数据标签页 -->
        <el-tab-pane name="活动数据" label="活动数据" v-if="decodedData?.d?.Activities">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <el-descriptions :column="2" border class="activities-descriptions">
                <el-descriptions-item v-for="(value, key) in decodedData.d.Activities" :key="key" :label="`活动${key}`">
                  <template v-if="value && typeof value === 'object' && value.isDecoded">
                    <div class="value-container">
                      <el-input-number v-model="value.displayValue" :min="0" :max="999999" controls-position="right"
                        size="small" class="decoded-input" />
                      <div class="value-info">
                        <span class="key-label">密钥: {{ value.k }}</span>
                        <el-tag type="danger" size="small" effect="dark">已解码</el-tag>
                        <el-tag v-if="value.displayValue !== value.originalValue" type="warning" size="small"
                          effect="dark">已修改</el-tag>
                      </div>
                    </div>
                  </template>
                  <span v-else>{{ value }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 商店数据标签页 -->
        <el-tab-pane name="商店数据" label="商店数据" v-if="decodedData.d?.ShopMgr">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <!-- 基础数据部 -->
              <div class="shop-section">
                <div class="section-title">基础数据</div>
                <div class="shop-basic-data">
                  <div class="shop-data-row" v-for="(keys, index) in getBasicShopDataPairs()" :key="index">
                    <!-- 左侧数据 -->
                    <el-form-item :label="getShopLabel(keys[0])" class="shop-form-item">
                      <el-input-number v-model="decodedData.d.ShopMgr[keys[0]].displayValue" :min="0" :max="999999"
                        :controls-position="'right'" v-if="typeof decodedData.d.ShopMgr[keys[0]] === 'object'" />
                      <span v-else class="data-value">{{ decodedData.d.ShopMgr[keys[0]] }}</span>
                    </el-form-item>

                    <!-- 右侧数据 -->
                    <el-form-item v-if="keys[1]" :label="getShopLabel(keys[1])" class="shop-form-item">
                      <el-input-number v-model="decodedData.d.ShopMgr[keys[1]].displayValue" :min="0" :max="999999"
                        :controls-position="'right'" v-if="typeof decodedData.d.ShopMgr[keys[1]] === 'object'" />
                      <span v-else class="data-value">{{ decodedData.d.ShopMgr[keys[1]] }}</span>
                    </el-form-item>
                  </div>
                </div>
              </div>

              <!-- 商品列表部分 -->
              <div class="shop-section" v-if="decodedData.d.ShopMgr[5]">
                <div class="section-title">
                  <span>商品列表</span>
                </div>
                <!-- 修改商品列表部分的表格列 -->
                <el-table :data="decodedData.d.ShopMgr[5]" border stripe class="shop-table">
                  <el-table-column label="商品ID" width="180">
                    <template #default="scope">
                      <el-input v-model="scope.row.id" placeholder="输入商品ID" size="small" />
                    </template>
                  </el-table-column>
                  <el-table-column label="折扣" width="150">
                    <template #default="scope">
                      <div class="discount-input">
                        <el-input-number v-model="scope.row.discount" :min="-999999" :max="999999" :step="10"
                          size="small" controls-position="right" style="width: 110px" />
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80" align="center">
                    <template #default="scope">
                      <el-button type="danger" circle size="small" @click="removeShopItem(scope.$index)">
                        <el-icon>
                          <Delete />
                        </el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 购买记录部分 -->
              <div class="shop-section" v-if="decodedData.d.ShopMgr[11]">
                <div class="section-title">
                  <span>购买记录</span>
                  <el-button type="primary" size="small" @click="addPurchaseRecord" class="add-button">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    添加记录
                  </el-button>
                </div>

                <!-- 添加记录的对话框 -->
                <el-dialog v-model="purchaseDialogVisible" title="添加购买记录" width="400px">
                  <el-form :model="newPurchaseRecord" label-width="100px">
                    <el-form-item label="商品ID">
                      <el-input v-model="newPurchaseRecord.id" placeholder="请输入商品ID" />
                    </el-form-item>
                    <el-form-item label="购买次数">
                      <el-input-number v-model="newPurchaseRecord.count" :min="-999999" :max="999999"
                        controls-position="right" />
                    </el-form-item>
                  </el-form>
                  <template #footer>
                    <span class="dialog-footer">
                      <el-button @click="purchaseDialogVisible = false">取消</el-button>
                      <el-button type="primary" @click="confirmAddPurchaseRecord">确定</el-button>
                    </span>
                  </template>
                </el-dialog>

                <!-- 购买记录表格 -->
                <el-table :data="getPurchaseHistory(decodedData.d.ShopMgr[11])" border stripe class="shop-table">
                  <el-table-column prop="id" label="商品ID" width="180" />
                  <el-table-column label="购买次数" width="150">
                    <template #default="scope">
                      <el-input-number v-model="decodedData.d.ShopMgr[11][scope.row.id].displayValue" :min="-999999"
                        :max="999999" size="small" :controls-position="'right'" style="width: 110px" />
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="80" align="center">
                    <template #default="scope">
                      <el-button type="danger" circle size="small" @click="removePurchaseRecord(scope.row.id)">
                        <el-icon>
                          <Delete />
                        </el-icon>
                      </el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 玩家数据签页 -->
        <el-tab-pane name="玩家数据" label="玩家数据" v-if="decodedData.d?.PlayerMgr">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <el-descriptions :column="2" border>
                <el-descriptions-item v-for="(value, key) in decodedData.d.PlayerMgr" :key="key" :label="`玩家${key}`">
                  <template v-if="Array.isArray(value)">
                    <el-tag v-for="item in value" :key="item" class="mx-1" style="margin-right: 5px;">
                      {{ item }}
                    </el-tag>
                  </template>
                  <span v-else>{{ value }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-scrollbar>
        </el-tab-pane>

        <!-- 引导数据标签页 -->
        <el-tab-pane name="引导数据" label="引导数据" v-if="decodedData.d?.GuideMgr">
          <el-scrollbar height="calc(100vh - 400px)" class="tabs-scrollbar">
            <div class="tabs-content">
              <el-descriptions :column="2" border>
                <el-descriptions-item v-for="(value, key) in decodedData.d.GuideMgr" :key="key" :label="`引导${key}`">
                  <template v-if="typeof value === 'object'">
                    <pre>{{ JSON.stringify(value, null, 2) }}</pre>
                  </template>
                  <span v-else>{{ value }}</span>
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-scrollbar>
        </el-tab-pane>
      </el-tabs>
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Document,
  RefreshRight,
  Delete,
  Plus  // 添 ElTag 导入
} from '@element-plus/icons-vue'
import {
  ElTag,  // 添 ElTag 导入
  ElTable,
  ElTableColumn,
  ElSelect,
  ElOption,
  ElTabs,
  ElTabPane,
  ElDescriptions,
  ElDescriptionsItem,
  ElDivider,
  ElCollapse,
  ElCollapseItem
} from 'element-plus'  // 导入所有需要的组件
import { NumberEncoder } from '@/utils/numberEncoder'

const emit = defineEmits(['update:operationHistory', 'loading'])

const formData = reactive({
  gameId: null
})

const gameData = reactive({
  gameId: '',
  shelterCoins: 0,
  energy: 0
})

// 解密后的数据
const decodedData = ref(null)

// 添加折叠状态
const isCollapsed = ref(false)

// 添加响应式变量
const activeTab = ref('基本信息') // 设置默认激活的标签页
const highlightedFields = ref(new Set()) // 存储需要高亮的字段

// 修改 base64ex 实现，添加编码方法
const base64ex = {
  CHAR_MAP: ["S", "Q", "v", "X", "C", "Z", "i", "J", "x", "c", "6", "G", "b", "A", "r", "F", "8", "V", "y", "p", "B", "l", "7", "5", "4", "n", "2", "R", "u", "M", "Y", "3", "d", "D", "t", "s", "P", "9", "g", "k", "q", "e", "o", "H", "W", "1", "a", "0", "h", "U", "K", "+", "T", "j", "m", "/", "L", "I", "E", "f", "z", "w", "O", "N"],

  CHAR_INDEX: {},

  init() {
    this.CHAR_INDEX = {};
    for (let t = 0; t < this.CHAR_MAP.length; t++) {
      this.CHAR_INDEX[this.CHAR_MAP[t]] = t;
    }
  },

  // 添加编码方法
  encodeJson(data) {
    const jsonStr = JSON.stringify(data);
    const bytes = new TextEncoder().encode(jsonStr);
    return "NM" + this.encodeArrayBufferBase64(bytes) + "SL";
  },

  encodeArrayBufferBase64(bytes) {
    const len = bytes.length;
    let result = '';

    for (let i = 0; i < Math.ceil(len / 3); i++) {
      const byte1 = bytes[3 * i];
      const byte2 = bytes[3 * i + 1];
      const byte3 = bytes[3 * i + 2];

      // 编码第一个字符
      result += this.CHAR_MAP[byte1 >> 2];

      if (3 * i + 1 < len) {
        // 编码第二个字符
        result += this.CHAR_MAP[((byte1 & 3) << 4) | (byte2 >> 4)];

        if (3 * i + 2 < len) {
          // 编码第三个字符
          result += this.CHAR_MAP[((byte2 & 15) << 2) | (byte3 >> 6)];
          // 编码第四个字符
          result += this.CHAR_MAP[byte3 & 63];
        } else {
          // 只有两个字的情况
          result += this.CHAR_MAP[(byte2 & 15) << 2];
          result += '=';
        }
      } else {
        // 只有一个字节的情况
        result += this.CHAR_MAP[(byte1 & 3) << 4];
        result += '==';
      }
    }

    return result;
  },

  decodeJson(e) {
    try {
      if (!e.startsWith('NM') || !e.endsWith('SL')) {
        throw new Error('Invalid format: Data must start with NM and end with SL');
      }
      const content = e.slice(2, -2);
      const buffer = this.decodeArrayBufferBase64(content);
      const decoder = new TextDecoder('utf-8');
      const jsonStr = decoder.decode(buffer);
      return JSON.parse(jsonStr);
    } catch (error) {
      console.error('Decoding error:', error);
      throw new Error(`解密失败: ${error.message}`);
    }
  },

  decodeArrayBufferBase64(str) {
    try {
      const len = str.length;
      const numGroups = Math.floor(len / 4);
      if (4 * numGroups !== len) {
        throw new Error('Invalid base64 string length');
      }

      let bufferLength = 3 * numGroups;
      if (str[len - 1] === '=') bufferLength--;
      if (str[len - 2] === '=') bufferLength--;

      const buffer = new ArrayBuffer(bufferLength);
      const view = new Uint8Array(buffer);
      let bufferIndex = 0;

      for (let i = 0; i < numGroups; i++) {
        const c1 = this.CHAR_INDEX[str[4 * i]] || 0;
        const c2 = this.CHAR_INDEX[str[4 * i + 1]] || 0;
        const c3 = str[4 * i + 2] === '=' ? 0 : (this.CHAR_INDEX[str[4 * i + 2]] || 0);
        const c4 = str[4 * i + 3] === '=' ? 0 : (this.CHAR_INDEX[str[4 * i + 3]] || 0);

        view[bufferIndex++] = (c1 << 2) | (c2 >> 4);
        if (str[4 * i + 2] !== '=' && bufferIndex < bufferLength) {
          view[bufferIndex++] = ((c2 & 15) << 4) | (c3 >> 2);
        }
        if (str[4 * i + 3] !== '=' && bufferIndex < bufferLength) {
          view[bufferIndex++] = ((c3 & 3) << 6) | c4;
        }
      }

      return buffer;
    } catch (error) {
      console.error('Base64 decoding error:', error);
      throw new Error('Invalid base64 string');
    }
  },

  // ... 其他方法保持不变
};

// 初始化 base64ex
base64ex.init();

// 属性标签映射
const propertyLabels = {
  gameId: '游戏ID',
  shelterCoins: '庇护币',
  energy: '能量',
  pj: '项目代码',
  pt: '平台类型',
  lt: '登录令牌',
  v: '版本号'
}

// 获取属性标签
const getPropertyLabel = (key) => {
  return propertyLabels[key] || key
}

// 判断属性是否可编辑
const isEditable = (key) => {
  return ['shelterCoins', 'energy'].includes(key)
}

// 修改解密处理函数，添加更多的错误处理
const handleDecode = () => {
  try {
    if (!formData.gameId) {
      throw new Error('请输入游戏ID');
    }

    // 先解base64数据
    console.log('Decoding input:', formData.gameId);
    const decoded = base64ex.decodeJson(formData.gameId);
    console.log('Decoded data:', decoded);

    // 解码所有带v和k的数据
    if (decoded.d) {
      Object.entries(decoded.d).forEach(([manager, data]) => {
        Object.entries(data).forEach(([key, value]) => {
          if (value && typeof value === 'object' && 'v' in value && 'k' in value) {
            // 使用 NumberEncoder 解码并保存原始值
            const decodedValue = NumberEncoder.decodeIntData(value, key);
            value.originalValue = decodedValue;
            value.displayValue = decodedValue;
            value.isDecoded = true;
          }
        });
      });
    }

    decodedData.value = decoded;
    activeTab.value = '基本信息'; // 设置解密后默认显示的标签页
    isCollapsed.value = true;

    emit('update:operationHistory', [{
      time: new Date().toLocaleString(),
      function: '解释数据',
      status: '成功'
    }]);

    ElMessage.success('数据解释成功');
  } catch (error) {
    console.error('解密失败:', error);
    ElMessage.error(`解密失败: ${error.message}`);
  }
}

// 处理下载数据
const handleDownload = async () => {
  if (!formData.gameId) {
    ElMessage.warning('请先输入游戏ID')
    return
  }

  try {
    emit('loading', true)

    const requestData = {
      pj: "sg2",
      pt: "wx",
      lt: formData.gameId,
      v: 0
    }

    const encryptedData = base64ex.encodeJson(requestData)

    const response = await fetch('/pdzs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9'
      },
      body: JSON.stringify([encryptedData])
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()

    if (data && data.length > 0) {
      const gameInfo = data[0]
      gameData.gameId = formData.gameId
      gameData.shelterCoins = gameInfo.shelterCoins || 0
      gameData.energy = gameInfo.energy || 0

      emit('update:operationHistory', [{
        time: new Date().toLocaleString(),
        function: '解释数据',
        status: '成功'
      }])

      ElMessage.success('数据解释成功')
    }
  } catch (error) {
    console.error('解释败:', error)
    ElMessage.error(`解释失败: ${error.message}`)
  } finally {
    emit('loading', false)
  }
}

// 修改更新游戏数据的方法
const updateGame = async () => {
  if (!decodedData.value) {
    ElMessage.warning('请先解释游戏数据')
    return
  }

  try {
    emit('loading', true)

    // 生成更新数据
    const updateData = NumberEncoder.generateUpdateData(decodedData.value, decodedData.value.d);

    const encryptedData = base64ex.encodeJson(updateData);

    const response = await fetch('/jsfs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Referer': 'https://servicewechat.com/wx969813315c7ff3d0/8/page-frame.html'
      },
      body: JSON.stringify([encryptedData])
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const result = base64ex.decodeJson(await response.text())
    console.log('服务器返回数据:', result)

    // 检查返回的结果
    if (result && result.r === 1) {
      emit('update:operationHistory', [{
        time: new Date().toLocaleString(),
        function: '更新数据',
        status: '成功'
      }])

      ElMessage.success('数据更新成功')

      // 更新成功后,将当前显示值设置为始值
      Object.values(decodedData.value.d).forEach(manager => {
        if (!manager || typeof manager !== 'object') return;
        Object.values(manager).forEach(value => {
          if (value && typeof value === 'object' && 'displayValue' in value) {
            value.originalValue = value.displayValue;
          }
        });
      });
    } else {
      throw new Error('服务器返回更新失败')
    }

  } catch (error) {
    console.error('更新失败:', error)
    ElMessage.error(`更新失败: ${error.message}`)
    throw error
  } finally {
    emit('loading', false)
  }
}

// 处理更新按钮点击
const handleUpdate = async () => {
  if (!formData.gameId) {
    ElMessage.warning('请先输入游戏ID')
    return
  }
  await updateGame()
}

// 监听 gameId 变化
watch(() => formData.gameId, (newVal) => {
  if (newVal) {
    gameData.gameId = newVal
  }
}, { immediate: true })

// 将卡牌数据转换为表格数据
const getCardList = (cardData) => {
  if (!cardData) return []
  return Object.entries(cardData).map(([cardId, data]) => ({
    cardId,
    ...data
  }))
}

// 修改清空方法
const clearGameId = () => {
  formData.gameId = ''
  decodedData.value = null
  isCollapsed.value = false // 清空后展开文本域
  ElMessage.success('已清空输入内容')
}

// 添加商店相关的方法
const getShopLabel = (key) => {
  const labels = {
    '0': '商店状态',
    '1': '刷新状态',
    '2': '时间戳',
    '3': '状态1',
    '4': '状态2',
    '6': '状态3',
    '7': '刷新次数',
    '8': '视频次数',
    '9': '状态4',
    '10': '状态5'
  }
  return labels[key] || `商店${key}`
}

// 获取基本商店数据（排除商品列表和购买记录）
const getBasicShopData = (shopData) => {
  const result = {}
  for (const key in shopData) {
    if (key !== '5' && key !== '11') {
      result[key] = shopData[key]
    }
  }
  return result
}

// 获取购买历史记录
const getPurchaseHistory = (purchaseData) => {
  return Object.entries(purchaseData).map(([id, count]) => ({
    id,
    count
  }))
}

// 添加获取成对基本商店数据的方法
const getBasicShopDataPairs = () => {
  const basicData = getBasicShopData(decodedData.value.d.ShopMgr)
  const keys = Object.keys(basicData)
  const pairs = []

  for (let i = 0; i < keys.length; i += 2) {
    pairs.push([keys[i], keys[i + 1]])
  }

  return pairs
}

// 添加经济数据相关方法
const getOtherEconomicData = () => {
  const result = {}
  for (const [key, value] of Object.entries(decodedData.value.d.EconomicMgr)) {
    // 排除主要资源
    if (key !== '4' && key !== '5' && key !== '6' && typeof value === 'object' && 'v' in value) {
      result[key] = value
    }
  }
  return result
}

// 修改经济标签映射方法
const getEconomyLabel = (key) => {
  const labels = {
    '6': '免广卷礼盒', 
    '7': '英雄碎片2',  // 修复乱码
    '8': '英雄碎片3',  
    '9': '英雄碎片4',  
    '10': '英雄碎片5', 
    // 如果有更多资源，可以继续添加
  }
  return labels[key] || `英雄碎片${key}` // 默认也改为英雄碎片
}

// 添加关卡数据相关方法
const getBasicStageData = () => {
  const result = {}
  const stageData = decodedData.value.d.StageMgr
  for (const key in stageData) {
    if (key !== '8') { // 排除详细数据
      result[key] = stageData[key]
    }
  }
  return result
}

const getStageLabel = (key) => {
  const labels = {
    '0': '状态',
    '1': '当前关卡',
    '2': '时间戳',
    '3': '状态1',
    '4': '关卡数据',
    '5': '关卡得',
    '6': '状态2',
    '7': '状态3',
    '9': '关卡配置',
    '10': '状态4',
    '11': '关卡记录',
    '12': '状态5',
    '13': '状态6'
  }
  return labels[key] || `关${key}`
}

// 格式化关卡详细数据
const formattedStageData = computed(() => {
  try {
    const data = JSON.parse(decodedData.value.d.StageMgr['8'])
    return JSON.stringify(data, null, 2)
  } catch {
    return decodedData.value.d.StageMgr['8']
  }
})

// 获取关卡信息
const stageInfo = computed(() => {
  try {
    return JSON.parse(decodedData.value.d.StageMgr['8'])
  } catch {
    return {}
  }
})

// 获取关卡类型文本
const getStageTypeText = (type) => {
  const types = {
    'main': '主线关卡',
    'daily': '每日关卡',
    'event': '活动关卡'
  }
  return types[type] || type
}

// 添加获取伤害分析数据的方法
const getDamageAnalytics = (analytics) => {
  if (!analytics) return []
  return Object.entries(analytics).map(([cardId, damage]) => ({
    cardId,
    damage
  }))
}

// 添加购买记录相关的响应式变量
const purchaseDialogVisible = ref(false)
const newPurchaseRecord = reactive({
  id: '',
  count: 0
})

// 添加购买记录相关的方法
const addPurchaseRecord = () => {
  newPurchaseRecord.id = ''
  newPurchaseRecord.count = 0
  purchaseDialogVisible.value = true
}

const confirmAddPurchaseRecord = () => {
  if (!newPurchaseRecord.id) {
    ElMessage.warning('请输入商品ID')
    return
  }

  // 确保 ShopMgr[11] 存在
  if (!decodedData.value.d.ShopMgr[11]) {
    decodedData.value.d.ShopMgr[11] = {}
  }

  // 添加或更新购买记录
  decodedData.value.d.ShopMgr[11][newPurchaseRecord.id] = newPurchaseRecord.count

  purchaseDialogVisible.value = false
  ElMessage.success('购买记录添加成功')
}

// 添加切换到经济数据标签页的方法
const switchToEconomicTab = () => {
  activeTab.value = '经济数据'
}

// 修改设置经济数据的方法，添加英雄碎片参数
const setEconomicData = (coins, diamonds, adTickets = null, setHeroFragments = false) => {
  if (!decodedData.value?.d?.EconomicMgr) return false

  const economicMgr = decodedData.value.d.EconomicMgr

  // 设置金币(4)和钻石(5)
  if (economicMgr['4']?.displayValue !== undefined) {
    economicMgr['4'].displayValue = coins
    highlightedFields.value.add('4')
  }

  if (economicMgr['5']?.displayValue !== undefined) {
    economicMgr['5'].displayValue = diamonds
    highlightedFields.value.add('5')
  }

  // 设置免广卷礼盒(6)
  if (adTickets !== null && economicMgr['6']?.displayValue !== undefined) {
    economicMgr['6'].displayValue = adTickets
    highlightedFields.value.add('6')
  }

  // 设置英雄碎片(7-10和11-18)
  if (setHeroFragments) {
    // 设置英雄碎片2-5
    for (let i = 7; i <= 10; i++) {
      if (economicMgr[i]?.displayValue !== undefined) {
        economicMgr[i].displayValue = 9999
        highlightedFields.value.add(i.toString())
      }
    }
    // 设置英雄碎片11-18
    for (let i = 11; i <= 18; i++) {
      if (economicMgr[i]?.displayValue !== undefined) {
        economicMgr[i].displayValue = 9999
        highlightedFields.value.add(i.toString())
      }
    }
  }

  return true
}

// 添加切换到能量管理标签页的方法
const switchToEnergyTab = () => {
  activeTab.value = '能量管理'
}

// 添加设置体力值的方法
const setEnergyData = (energyValue) => {
  if (!decodedData.value?.d?.EnergyMgr) return false

  const energyMgr = decodedData.value.d.EnergyMgr

  // 设置体力值(4)
  if (energyMgr['4']?.displayValue !== undefined) {
    energyMgr['4'].displayValue = energyValue
    highlightedFields.value.add('4')
  }

  return true
}

// 暴露必要的属性和方法给父组件
defineExpose({
  gameData,
  updateGame,
  switchToEconomicTab,
  switchToEnergyTab,  // 新增
  setEconomicData,
  setEnergyData      // 新增
})

// 修改经济数据标签页中的样式，添加高亮效果
const getHighlightStyle = (key) => {
  if (highlightedFields.value.has(key)) {
    return {
      background: 'rgba(255, 223, 186, 0.3)',
      boxShadow: '0 0 8px rgba(255, 152, 0, 0.3)',
      transition: 'all 0.3s ease'
    }
  }
  return {}
}

// 添加手动添加英雄碎片的方法
const handleAddHeroFragments = () => {
  if (!decodedData.value.d) {
    decodedData.value.d = {};
  }

  if (!decodedData.value.d.EconomicMgr) {
    decodedData.value.d.EconomicMgr = {};
  }

  // 添加英雄碎片2-5 (索引7-10)
  for (let i = 7; i <= 10; i++) {
    if (!decodedData.value.d.EconomicMgr[i]) {
      decodedData.value.d.EconomicMgr[i] = {
        v: 0,  // 初始值
        k: 0,  // 密钥
        displayValue: 9999,  // 显示值
        originalValue: 0,    // 原始值
        isDecoded: true     // 已解码标记
      };
      highlightedFields.value.add(i.toString());
    }
  }

  // 添加英雄碎片11-18
  for (let i = 11; i <= 18; i++) {
    if (!decodedData.value.d.EconomicMgr[i]) {
      decodedData.value.d.EconomicMgr[i] = {
        v: 0,
        k: 0,
        displayValue: 9999,
        originalValue: 0,
        isDecoded: true
      };
      highlightedFields.value.add(i.toString());
    }
  }

  ElMessage.success('英雄碎片添加成功');
}
</script>

<style scoped>
.game-id-form {
  padding: 10px;
  /* 整体内边距 */
}

.el-button {
  margin-right: 10px;
}

.el-button .el-icon {
  margin-right: 4px;
}

.el-form-item {
  margin-bottom: 15px;
  /* 减少表单项之间的间距 */
}

/* 修改文域的样式 */
.game-id-input {
  max-width: 600px;
  /* 增加最大宽 */
}

/* 自定义文本域样式 */
:deep(.el-textarea__inner) {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.5;
  padding: 15px;
  margin: -5px 0;
  /* 通过负边距使文本域更靠近边框 */
}

/* 调整表单标签的样式 */
:deep(.el-form-item__label) {
  padding-right: 8px;
  /* 减少标签右侧padding */
}

@media (max-width: 768px) {
  .game-id-input {
    width: 100% !important;
  }
}

/* 添加新样式 */
.el-divider {
  margin: 24px 0;
}

.el-descriptions {
  margin: 16px 0;
}

:deep(.el-descriptions__label) {
  width: 120px;
}

:deep(.el-input-number) {
  width: 180px;
}

/* 添加新样式 */
.data-section {
  margin: 20px 0;
}

.nested-form {
  padding: 20px;
}

:deep(.el-collapse-item__header) {
  font-size: 16px;
  font-weight: bold;
}

:deep(.el-table) {
  margin: 10px 0;
}

:deep(.el-collapse-item__content) {
  padding: 10px;
}

:deep(.el-descriptions) {
  margin: 10px 0;
}

:deep(.el-input-number) {
  width: 160px;
}

:deep(.el-select) {
  width: 120px;
}

/* 添加新样式 */
.data-tabs {
  margin-top: 20px;
}

.key-label {
  margin-left: 10px;
  color: #666;
  font-size: 12px;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

:deep(.el-descriptions) {
  padding: 10px;
}

:deep(.el-descriptions__cell) {
  padding: 12px 20px;
}

:deep(.el-tag) {
  margin: 2px;
}

pre {
  background: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 添加新样式 */
.input-group {
  position: relative;
  display: inline-block;
  transition: all 0.3s ease;
}

.input-group.collapsed .game-id-input {
  opacity: 0.7;
}

:deep(.el-textarea__inner) {
  transition: all 0.3s ease;
}

.input-group.collapsed :deep(.el-textarea__inner) {
  cursor: pointer;
}

.clear-button {
  position: absolute;
  right: -50px;
  top: 0;
}

/* 添加滚动区域样式 */
.tabs-scrollbar {
  margin: 10px;
}

.tabs-content {
  padding: 0 10px;
  width: 100%;
  /* 使用100%宽度而不是最小宽度 */
}

:deep(.el-tabs__content) {
  overflow: visible;
  padding: 0;
}

:deep(.el-tab-pane) {
  padding: 10px 0;
}

/* 优化滚动条样式 */
:deep(.el-scrollbar__bar) {
  z-index: 100;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

/* 确保表格不会超出容器 */
:deep(.el-table) {
  width: 100% !important;
  min-width: 600px;
  /* 设置表格最小宽度 */
}

/* 确保描述列表不会超出容器 */
:deep(.el-descriptions) {
  width: 100%;
}

/* 修改和添加样式 */
.tabs-content {
  padding: 0 10px;
  min-width: 800px;
  /* 设置最小宽度 */
}

:deep(.el-descriptions__cell) {
  min-width: 200px;
  /* 设置描述项的最小宽度 */
  padding: 12px 20px;
}

:deep(.el-descriptions__label) {
  width: 150px;
  /* 增加标签宽度 */
}

:deep(.el-input-number) {
  width: 180px;
  /* 增加数字输入框宽度 */
}

:deep(.el-select) {
  width: 150px;
  /* 增加选择框宽度 */
}

/* 表格相关样式 */
:deep(.el-table) {
  width: 100%;
  max-width: 900px;
  /* 增加表格整体宽度 */
}

/* 调整商品ID列宽度 */
:deep(.el-table .el-table__cell[data-col-index="0"]) {
  width: 200px !important;
  /* 固定商品ID列宽度 */
}

/* 调整折扣/购买次数列宽度和样式 */
:deep(.el-table .el-table__cell[data-col-index="1"]) {
  width: 150px !important;
  /* 固定折扣列宽度 */
  text-align: center;
}

/* 调整操作列宽度 */
:deep(.el-table .el-table__cell[data-col-index="2"]) {
  width: 100px !important;
  /* 定操作列宽度 */
  text-align: center;
}

/* 调整数字输入框容器样式 */
:deep(.el-input-number) {
  width: 120px !important;
  /* 减小数字输入框宽度 */
  margin: 0 auto;
  /* 居中显示 */
}

/* 调整数字输入框按钮位置 */
:deep(.el-input-number .el-input-number__decrease),
:deep(.el-input-number .el-input-number__increase) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 100%;
}

/* 调整表格单元格内容居中 */
:deep(.el-table .el-table__cell) {
  text-align: center;
}

/* 调整商品ID单元格左对齐 */
:deep(.el-table .el-table__cell[data-col-index="0"]) {
  text-align: left;
}

/* 调整表格内输入框样式 */
:deep(.el-table .el-input) {
  width: 180px;
  /* 调整商品ID输入框宽度 */
}

/* 调整删除按钮位置 */
:deep(.el-table .el-button) {
  padding: 6px;
  margin: 0 auto;
}

/* 调整表格行高 */
:deep(.el-table .el-table__row) {
  height: 50px;
  /* 增加行高 */
}

/* 确保内容垂直居中 */
:deep(.el-table .cell) {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 商品ID列左对齐 */
:deep(.el-table .el-table__cell[data-col-index="0"] .cell) {
  justify-content: flex-start;
}

/* 调整表格头部样式 */
:deep(.el-table__header-wrapper th) {
  text-align: center !important;
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
}

/* 调整表格边框和间距 */
:deep(.el-table--border) {
  border-radius: 4px;
  margin-bottom: 20px;
}

/* 调整表格内边距 */
:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

/* 文本域样式 */
:deep(.el-textarea) {
  width: 100%;
  min-width: 300px;
}

/* 标签页内容区域 */
:deep(.el-tab-pane) {
  padding: 20px;
  min-width: 700px;
}

/* 确保长文本可以换行 */
:deep(.el-descriptions__content) {
  word-break: break-all;
  white-space: pre-wrap;
}

/* 调整密钥显示样式 */
.key-label {
  margin-left: 10px;
  color: #666;
  font-size: 12px;
  word-break: break-all;
  display: block;
  /* 让密钥显示在新行 */
  margin-top: 5px;
}

/* 调整预格式化文本显示 */
pre {
  max-width: 100%;
  overflow-x: auto;
  white-space: pre-wrap;
  word-wrap: break-word;
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
}

/* 确保滚动区域正常工作 */
:deep(.el-scrollbar__wrap) {
  overflow-x: auto !important;
}

/* 调整表单项布局 */
.nested-form {
  width: 100%;
  min-width: 700px;
}

.nested-form .el-form-item {
  margin-bottom: 20px;
  display: flex;
  flex-wrap: wrap;
}

.nested-form .el-form-item__content {
  flex: 1;
  min-width: 300px;
}

/* 修改滚动条相关样式 */
.tabs-content {
  padding: 0 10px;
  width: 100%;
  /* 使用100%宽度而不是最小宽度 */
}

/* 隐藏横向滚动条 */
:deep(.scrollbar-wrapper) {
  overflow-x: hidden !important;
  /* 防止出现底部空白 */
}

:deep(.scrollbar-view) {
  height: 100%;
  width: 100%;
}

/* 调整内容区域自适应 */
:deep(.el-descriptions),
:deep(.el-table),
:deep(.nested-form) {
  width: 100% !important;
  min-width: unset !important;
  /* 移除最小宽度限制 */
}

/* 确保表格内容不会溢出 */
:deep(.el-table__body-wrapper) {
  overflow-x: hidden !important;
}

:deep(.el-table__cell) {
  white-space: normal !important;
  /* 允许文本换行 */
  word-break: break-all;
}

/* 调整描述列表的布 */
:deep(.el-descriptions__cell) {
  min-width: unset !important;
  word-break: break-all;
}

/* 调整表单布局 */
.nested-form .el-form-item {
  flex-wrap: wrap;
}

.nested-form .el-form-item__content {
  flex: 1;
  min-width: unset !important;
}

/* 确保所有内容都在容器内 */
:deep(.el-tab-pane) {
  min-width: unset !important;
  padding: 20px;
}

/* 调整输入框和选框的宽度 */
:deep(.el-input-number),
:deep(.el-select) {
  width: auto !important;
  max-width: 100%;
}

/* 调整预格式化文本显示 */
pre {
  max-width: 100%;
  overflow-x: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 密钥标签自动换行 */
.key-label {
  display: block;
  word-break: break-all;
  white-space: normal;
}

/* 调整标签页内容区域的样式 */
.tabs-content {
  padding: 0 10px;
  width: 100%;
  max-width: 800px;
  /* 增加最大宽度 */
  margin: 0 auto;
}

/* 调整描述列表的宽度 */
:deep(.el-descriptions) {
  width: 100%;
  max-width: 800px;
  /* 增加描述列表最大宽度 */
}

/* 调整表格的宽度 */
:deep(.el-table) {
  width: 100%;
  max-width: 800px;
  /* 增加表格最大宽度 */
}

/* 调整表单宽度 */
.nested-form {
  width: 100%;
  max-width: 800px;
  /* 增加表单最大宽度 */
}

/* 调整输入框宽度 */
:deep(.el-input) {
  width: 250px;
  /* 增加输入框宽度 */
}

/* 调整字输入框宽度 */
:deep(.el-input-number) {
  width: 150px !important;
  /* 增加数字输入框宽度 */
}

/* 调整选框宽度 */
:deep(.el-select) {
  width: 150px !important;
  /* 增加选择框宽度 */
}

/* 调整描述列表宽度 */
:deep(.el-descriptions) {
  width: 100%;
  max-width: 800px;
  /* 增加描述列表最大宽度 */
}

/* 调整描述项的宽度 */
:deep(.el-descriptions__cell) {
  max-width: 400px;
  /* 增加单元格最大宽度 */
}

/* 添加关卡数据相关样式 */
.stage-detail {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.stage-info {
  flex: 1;
  min-width: 250px;
}

.stage-details {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.detail-section h5 {
  margin: 12px 0 8px 0;
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.damage-analytics {
  margin-top: 12px;
}

:deep(.el-descriptions) {
  margin-bottom: 0;
}

:deep(.el-descriptions__cell) {
  padding: 8px 12px;
}

:deep(.el-table--small) {
  font-size: 12px;
}

:deep(.el-table--small td) {
  padding: 4px 8px;
}

/* 确保表格在容器内滚动 */
:deep(.el-table--small .el-table__body-wrapper) {
  max-height: 200px;
  overflow-y: auto;
}

/* 优化小尺寸描述列表的样式 */
:deep(.el-descriptions--small .el-descriptions__label) {
  padding: 6px 10px;
  width: 100px;
  color: #606266;
}

:deep(.el-descriptions--small .el-descriptions__content) {
  padding: 6px 10px;
  color: #303133;
}

/* 调整表格内容的显示 */
:deep(.el-table__body-wrapper) {
  overflow-x: auto !important;
  /* 允许横向滚动 */
}

/* 调整表格操作列的宽度 */
:deep(.el-table .el-table__cell:last-child) {
  min-width: 120px;
  /* 增加操作列的最小宽度 */
}

/* 调整按钮间距 */
.table-operations {
  margin-top: 15px;
  margin-bottom: 20px;
}

/* 调整商品列表的外边距 */
:deep(.el-form-item) {
  margin-bottom: 25px;
}

/* 添加商店数据布局相关样式 */
.shop-basic-data {
  display: flex;
  flex-direction: column;
  gap: 5px;
  /* 减小垂直间距 */
  margin-bottom: 15px;
}

.shop-data-row {
  display: flex;
  gap: 10px;
  /* 减小水平间距 */
  justify-content: flex-start;
  align-items: center;
}

.shop-form-item {
  flex: 1;
  max-width: calc(50% - 5px);
  /* 调整最大宽度 */
  margin-bottom: 0 !important;
}

/* 调整标签宽度和对齐 */
:deep(.shop-form-item .el-form-item__label) {
  width: 70px !important;
  /* 减小标签宽度 */
  padding-right: 5px !important;
  /* 减小右侧内边距 */
  justify-content: flex-end;
  font-size: 14px;
}

/* 调整输入框区域的位置 */
:deep(.shop-form-item .el-form-item__content) {
  margin-left: 70px !important;
  /* 与标签宽度保持一致 */
}

/* 调整数字输入框的宽度和位置 */
:deep(.shop-form-item .el-input-number) {
  width: 110px !important;
  /* 减小输入框宽度 */
  margin-left: -5px;
  /* 向左微调 */
}

/* 调整表单项的内部间距 */
:deep(.el-form-item__content) {
  padding-left: 0;
}

/* 确保标签文字不换行且对齐 */
:deep(.el-form-item__label) {
  white-space: nowrap;
  line-height: 28px;
  /* 减小行高 */
}

/* 调整整体布局的紧凑度 */
.shop-basic-data :deep(.el-form-item) {
  margin-bottom: 5px !important;
  /* 减小底部边距 */
}

/* 调整输入框组件的样式 */
:deep(.el-input-number.is-controls-right) {
  width: 110px !important;
}

/* 调整输入框内部的间距 */
:deep(.el-input-number .el-input__inner) {
  padding-left: 5px;
  padding-right: 5px;
}

/* 调整表单项的高度 */
:deep(.el-form-item) {
  min-height: 32px;
  /* 减小最小高度 */
}

/* 调整输入框控件的位置 */
:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  width: 28px !important;
  /* 减小控件宽度 */
}

/* 商店数据相关样式 */
.shop-section {
  margin-bottom: 30px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.add-button {
  margin-left: auto;
}

.shop-basic-data {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.shop-data-row {
  display: flex;
  gap: 20px;
  align-items: center;
}

.shop-form-item {
  flex: 1;
  margin: 0 !important;
  display: flex;
  align-items: center;
}

:deep(.shop-form-item .el-form-item__label) {
  width: 100px !important;
  padding-right: 12px !important;
  color: #606266;
  font-weight: 500;
}

:deep(.shop-form-item .el-form-item__content) {
  margin-left: 0 !important;
  flex: 1;
}

:deep(.shop-form-item .el-input-number) {
  width: 130px !important;
}

.data-value {
  color: #606266;
  font-size: 14px;
}

.shop-table {
  width: 100%;
  margin-top: 10px;
}

:deep(.el-table) {
  --el-table-border-color: #EBEEF5;
  --el-table-header-bg-color: #F5F7FA;
}

:deep(.el-table th) {
  background-color: #F5F7FA;
  font-weight: bold;
}

:deep(.el-input.el-input--small) {
  width: 100%;
}

:deep(.el-input-number.el-input-number--small) {
  width: 100px !important;
}

/* 确保内容垂直居中 */
:deep(.el-table .cell) {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 商品ID列左对齐 */
:deep(.el-table .el-table__cell:first-child .cell) {
  justify-content: flex-start;
}

/* 调整表格相关样式 */
.shop-table {
  width: 100%;
}

/* 调整表格内输入框样式 */
:deep(.el-table .el-input) {
  width: 150px !important;
  /* 限制输入框宽度 */
}

/* 调整数字输入框样式 */
:deep(.el-table .el-input-number) {
  width: 90px !important;
  /* 减小数字输入框宽度 */
}

/* 确保单元格内容不会溢出 */
:deep(.el-table .cell) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 调整表格行高 */
:deep(.el-table .el-table__row) {
  height: 40px;
  /* 减小行高 */
}

/* 调整表格内边距 */
:deep(.el-table td) {
  padding: 5px 0;
}

/* 调整按钮大小 */
:deep(.el-table .el-button--small) {
  padding: 4px;
  min-height: unset;
}

/* 调整输入框内边距 */
:deep(.el-input__inner) {
  padding: 0 8px;
}

/* 修改折扣输入框相关样式 */
.discount-input {
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-table .el-input-number.el-input-number--small) {
  width: 110px !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  border: 0;
  background: transparent;
}

:deep(.el-input-number__decrease:hover),
:deep(.el-input-number__increase:hover) {
  color: var(--el-color-primary);
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
  padding-right: 30px !important;
}

/* 确保单元格内容居中且有足够空间 */
:deep(.el-table .cell) {
  padding: 0 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 40px;
}

/* 调整数字输入框的位置 */
:deep(.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 25px;
}

/* 调整控制按钮的位置和大小 */
:deep(.el-input-number.is-controls-right .el-input-number__decrease),
:deep(.el-input-number.is-controls-right .el-input-number__increase) {
  height: 16px !important;
  /* 调整控制按钮高度 */
  line-height: 16px !important;
  width: 28px !important;
}

/* 调整购买记录表格相关样式 */
:deep(.el-table .el-table__cell) {
  padding: 5px 8px;
}

:deep(.el-table .el-input-number.el-input-number--small) {
  width: 110px !important;
}

:deep(.el-table .cell) {
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 商品ID对齐 */
:deep(.el-table .el-table__cell:first-child .cell) {
  justify-content: flex-start;
  padding-left: 8px;
}

/* 调整操作列按钮样式 */
:deep(.el-table .el-button--small) {
  padding: 4px;
  margin: 0;
}

/* 调整表格中数字输入框的样式 */
:deep(.el-table .el-input-number.el-input-number--small) {
  width: 110px !important;
  height: 32px !important;
  /* 增加高度 */
}

/* 调整数字输入框的输入区域 */
:deep(.el-table .el-input-number .el-input__wrapper) {
  height: 32px !important;
  /* 增加高度 */
  line-height: 32px !important;
}

/* 调整数字输入框的控制按钮 */
:deep(.el-table .el-input-number.is-controls-right .el-input-number__decrease),
:deep(.el-table .el-input-number.is-controls-right .el-input-number__increase) {
  height: 16px !important;
  /* 调整控制按钮高度 */
  line-height: 16px !important;
  width: 28px !important;
}

/* 调整表格行高 */
:deep(.el-table .el-table__row) {
  height: 50px !important;
  /* 增加行高 */
}

/* 调整单元格内容垂直居中 */
:deep(.el-table .cell) {
  height: 40px !important;
  /* 增加高度 */
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 调整数字输入框中的文本对齐 */
:deep(.el-table .el-input-number .el-input__inner) {
  height: 32px !important;
  line-height: 32px !important;
  text-align: center !important;
  padding: 0 25px 0 8px !important;
}

/* 确保控制按钮位正确 */
:deep(.el-table .el-input-number.is-controls-right) {
  display: flex;
  align-items: center;
}

/* 调整按钮悬停效果 */
:deep(.el-input-number__decrease:hover),
:deep(.el-input-number__increase:hover) {
  color: var(--el-color-primary);
  background-color: var(--el-fill-color-light);
}

/* 调整单元格内边距 */
:deep(.el-table td.el-table__cell) {
  padding: 8px 0;
}

/* 经济数据相关样式 */
.economy-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.economy-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  padding: 10px 0;
}

.economy-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.economy-item:hover {
  background: #f0f2f5;
  transform: translateY(-2px);
}

.item-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 12px;
}

.economy-input {
  width: 160px !important;
}

.economy-item .key-label {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}

:deep(.el-input-number.is-controls-right) {
  width: 160px !important;
}

:deep(.el-input-number__decrease),
:deep(.el-input-number__increase) {
  background: transparent;
  border: none;
  color: #606266;
}

:deep(.el-input-number__decrease:hover),
:deep(.el-input-number__increase:hover) {
  color: var(--el-color-primary);
}

:deep(.el-input-number .el-input__inner) {
  text-align: center;
  font-weight: 500;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #EBEEF5;
}

/* 修改经济数据相关样式 */
.economy-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  width: 100%;
}

.economy-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 修改为固定2列 */
  gap: 12px;
  /* 减小间距 */
  padding: 8px 0;
  width: 100%;
}

.economy-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
  min-width: 0;
  /* 防止子元素溢出 */
}

.economy-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.item-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
  margin-bottom: 8px;
}

.economy-input {
  width: 110px !important;
  /* 减小输入框宽度 */
}

.economy-item .key-label {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
  text-align: center;
  word-break: break-all;
  /* 允许在任意字符间断行 */
}

/* 调整数字输入框样式 */
:deep(.economy-input .el-input__wrapper) {
  padding: 0 8px;
}

:deep(.economy-input .el-input-number__decrease),
:deep(.economy-input .el-input-number__increase) {
  width: 22px !important;
}

:deep(.economy-input.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 25px !important;
}

.section-title {
  font-size: 14px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #EBEEF5;
}

/* 适配移动端 */
@media screen and (max-width: 768px) {
  .economy-grid {
    grid-template-columns: 1fr;
    /* 在移动端改为单列 */
  }

  .economy-section {
    max-width: 100%;
    margin: 0 0 20px;
  }
}

/* 确保内容不会溢出容器 */
.tabs-content {
  width: 100%;
  max-width: 100%;
  padding: 0;
  margin: 0 auto;
}

/* 隐藏横向滚动条 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
}

/* 调整标题样式 */
.section-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #EBEEF5;
  width: 100%;
}

/* 关卡数据相关样式 */
.stage-section {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.stage-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: 16px;
  padding: 10px 0;
}

.stage-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: all 0.3s ease;
}

.stage-item:hover {
  background: #f0f2f5;
  transform: translateY(-2px);
}

.stage-input {
  width: 140px !important;
}

.stage-value {
  font-size: 14px;
  color: #606266;
  margin-top: 8px;
}

.stage-detail {
  display: flex;
  gap: 24px;
  margin-top: 16px;
}

.stage-info {
  flex: 1;
  min-width: 250px;
}

.stage-details {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
}

.detail-section h4 {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.detail-section h5 {
  margin: 12px 0 8px 0;
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.damage-analytics {
  margin-top: 12px;
}

:deep(.el-descriptions) {
  margin-bottom: 0;
}

:deep(.el-descriptions__cell) {
  padding: 8px 12px;
}

:deep(.el-table--small) {
  font-size: 12px;
}

:deep(.el-table--small td) {
  padding: 4px 8px;
}

/* 确保表格在容器内滚动 */
:deep(.el-table--small .el-table__body-wrapper) {
  max-height: 200px;
  overflow-y: auto;
}

/* 优化小尺寸描述列表的样式 */
:deep(.el-descriptions--small .el-descriptions__label) {
  padding: 6px 10px;
  width: 100px;
  color: #606266;
}

:deep(.el-descriptions--small .el-descriptions__content) {
  padding: 6px 10px;
  color: #303133;
}

/* 添加能量管理相关样式 */
.energy-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  /* 修改为固定2列 */
  gap: 12px;
  /* 减小间距 */
  padding: 10px;
  max-width: 600px;
  /* 限制最大宽度 */
  margin: 0 auto;
  /* 居中显示 */
}

.energy-item {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 10px;
  /* 减小内边距 */
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.energy-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.energy-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  /* 减小间距 */
}

.energy-label {
  font-size: 13px;
  color: #606266;
  font-weight: 500;
}

.energy-input {
  width: 110px !important;
  /* 减小输入框宽度 */
}

.energy-value {
  font-size: 14px;
  color: #303133;
}

.energy-item .key-label {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
  text-align: center;
  word-break: break-all;
}

/* 调整数字输入框样式 */
:deep(.energy-input .el-input__wrapper) {
  padding: 0 8px;
}

:deep(.energy-input .el-input-number__decrease),
:deep(.energy-input .el-input-number__increase) {
  width: 22px !important;
  /* 减小控制按钮宽度 */
}

:deep(.energy-input.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 25px !important;
}

/* 适配移动端 */
@media screen and (max-width: 768px) {
  .energy-grid {
    grid-template-columns: 1fr;
    /* 在移动端改为单列 */
    max-width: 100%;
  }
}

/* 调整数字输入框样式 */
:deep(.energy-input .el-input__wrapper) {
  padding: 0 8px;
}

:deep(.energy-input .el-input-number__decrease),
:deep(.energy-input .el-input-number__increase) {
  width: 22px !important;
  /* 减小控制按钮宽度 */
}

:deep(.energy-input.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 25px !important;
}

/* 适配移动端 */
@media screen and (max-width: 768px) {
  .energy-grid {
    grid-template-columns: 1fr;
    /* 在移动端改为单列 */
    max-width: 100%;
  }
}

/* 添加样式 */
.value-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.decoded-input {
  width: 160px !important;
}

.decoded-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

.decoded-input:hover :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger-light-3) inset !important;
}

.key-label {
  color: #909399;
  font-size: 12px;
}

:deep(.el-tag--danger) {
  margin-left: 4px;
}

:deep(.el-tag--warning) {
  margin-left: 4px;
}

/* 修改能量值容器样式 */
.energy-descriptions {
  width: 100%;
}

:deep(.el-descriptions__cell) {
  padding: 12px 20px;
}

:deep(.el-input-number.is-controls-right) {
  width: 160px !important;
}

.stage-descriptions {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.stage-descriptions-item {
  flex: 1;
  min-width: 250px;
}

.stage-descriptions-item .value-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stage-descriptions-item .value-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stage-descriptions-item .decoded-input {
  width: 160px !important;
}

.stage-descriptions-item .decoded-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

.stage-descriptions-item .decoded-input:hover :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger-light-3) inset !important;
}

.stage-descriptions-item .key-label {
  color: #909399;
  font-size: 12px;
}

:deep(.el-tag--danger) {
  margin-left: 4px;
}

:deep(.el-tag--warning) {
  margin-left: 4px;
}

.activities-descriptions {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.activities-descriptions-item {
  flex: 1;
  min-width: 250px;
}

.activities-descriptions-item .value-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.activities-descriptions-item .value-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.activities-descriptions-item .decoded-input {
  width: 160px !important;
}

.activities-descriptions-item .decoded-input :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}

.activities-descriptions-item .decoded-input:hover :deep(.el-input__wrapper) {
  box-shadow: 0 0 0 1px var(--el-color-danger-light-3) inset !important;
}

.activities-descriptions-item .key-label {
  color: #909399;
  font-size: 12px;
}

:deep(.el-tag--danger) {
  margin-left: 4px;
}

:deep(.el-tag--warning) {
  margin-left: 4px;
}

/* 添加高亮相关样式 */
.highlighted {
  background: rgba(255, 223, 186, 0.3);
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.3);
  transition: all 0.3s ease;
}

/* 添加或修改样式 */
.data-tabs {
  margin-top: 20px;
}

:deep(.el-tabs__content) {
  padding: 20px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

:deep(.el-tab-pane) {
  padding: 0;
  min-height: 200px;
  /* 设置最小高度 */
}

.tabs-scrollbar {
  margin: 0;
  padding: 10px;
}

.tabs-content {
  padding: 0;
  width: 100%;
}
</style>
