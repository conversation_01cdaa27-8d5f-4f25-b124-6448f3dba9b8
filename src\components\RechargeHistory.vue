<template>
  <div class="operation-history">
    <el-table 
      :data="formattedData" 
      style="width: 100%" 
      size="small"
      :max-height="tableHeight"
    >
      <el-table-column prop="time" label="日期" width="180" />
      <el-table-column prop="function" label="操作类型" width="120" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="scope">
          <span :class="['status-tag', `status-${scope.row.status}`]">
            {{ scope.row.status }}
          </span>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
export default {
  name: 'RechargeHistory',
  props: {
    initialData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tableHeight: 300 // 设置表格最大高度
    }
  },
  computed: {
    formattedData() {
      return this.initialData.map(item => ({
        ...item,
        status: '成功'
      }))
    }
  }
}
</script>

<style scoped>
.operation-history {
  padding: 0;
  height: 100%;
  overflow: hidden;
}

:deep(.el-table) {
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-table__header) {
  font-weight: bold;
}

:deep(.el-table .cell) {
  padding: 8px;
}

:deep(.el-table__body-wrapper) {
  overflow: hidden !important; /* 强制隐藏滚动条 */
}

.status-tag {
  display: inline-block;
  padding: 2px 10px;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  min-width: 60px;
}

.status-成功 {
  background-color: #f0f9eb;
  color: #67c23a;
}

.status-失败 {
  background-color: #fef0f0;
  color: #f56c6c;
}

:deep(.el-table__row) {
  height: 40px;
}
</style>
