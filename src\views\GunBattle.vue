<template>
  <div class="gun-battle">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <Aim />
              </el-icon>
              <span>枪战王者</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 数据输入区域 -->
          <div class="data-input-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon><Document /></el-icon>
                <span>游戏数据</span>
              </div>
              <el-button
                type="danger"
                size="small"
                class="clear-button"
                @click="handleClearData"
                :icon="Delete"
              >
                清空数据
              </el-button>
            </div>

            <!-- OpenID输入框 -->
            <el-form :model="formData" label-position="top">
              <el-form-item label="OpenID">
                <el-input
                  v-model="formData.openId"
                  placeholder="请输入OpenID"
                  clearable
                  :disabled="loading.download || loading.upload"
                />
              </el-form-item>
            </el-form>

            <!-- 操作按钮 -->
            <div class="action-section">
              <el-button 
                type="primary" 
                :loading="loading.download" 
                @click="handleDownload" 
                :icon="Download"
                class="action-button"
              >
                下载数据
              </el-button>
              <el-button 
                type="success" 
                :loading="loading.upload" 
                @click="handleUpload" 
                :icon="Upload"
                :disabled="!hasData" 
                class="action-button"
              >
                上传数据
              </el-button>
            </div>
          </div>

          <!-- 游戏数据编辑区域 -->
          <div v-if="hasData" class="game-data-section">
            <div class="section-title">
              <el-icon><Document /></el-icon>
              <span>游戏数据</span>
            </div>
            
            <el-tabs type="border-card">
              <!-- 基础属性 -->
              <el-tab-pane label="基础属性">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="金币">
                        <el-input v-model="gameData.gold" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="钻石">
                        <el-input v-model="gameData.diamond" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="体力">
                        <el-input v-model="gameData.tili" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="体力恢复时间">
                        <el-input v-model="gameData.tiliTime" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="版本号">
                        <el-input v-model="gameData.v" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="角色等级">
                        <el-input v-model="gameData.rolelevel" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="章节ID">
                        <el-input v-model="gameData.chapterID" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="关卡等级">
                        <el-input v-model="gameData.level" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="角色经验">
                        <el-input v-model="gameData.roleexp" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 装备系统 -->
              <el-tab-pane label="装备系统">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="枪支ID">
                        <el-input v-model="gameData.gunID" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="服装ID">
                        <el-input v-model="gameData.clothID" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="皮肤ID">
                        <el-input v-model="gameData.SkinID" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="装备列表">
                        <el-input v-model="gameData.equipments" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="装备等级">
                        <el-input v-model="gameData.equipmentslv" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="装备等级数量">
                        <el-input v-model="gameData.equipmentslvnum" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 天赋系统 -->
              <el-tab-pane label="天赋系统">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="天赋">
                        <el-input v-model="gameData.talent" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="天赋等级">
                        <el-input v-model="gameData.talentlv" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="攻击天赋">
                        <el-input v-model="gameData.talentAttack" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="生命天赋">
                        <el-input v-model="gameData.talentHP" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="防御天赋">
                        <el-input v-model="gameData.talentDefense" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 任务系统 -->
              <el-tab-pane label="任务系统">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="任务通关等级">
                        <el-input v-model="gameData.questPassLevel" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="任务章节数量">
                        <el-input v-model="gameData.questChapterNum" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="击杀敌人数量">
                        <el-input v-model="gameData.killEnemyNum" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="使用金币数量">
                        <el-input v-model="gameData.questUseMoneyNum" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="使用钻石数量">
                        <el-input v-model="gameData.questUseDiamondNum" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 战斗数据 -->
              <el-tab-pane label="战斗数据">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="战斗章节">
                        <el-input v-model="gameData.FightChapter" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="战斗等级">
                        <el-input v-model="gameData.FightLevel" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="战斗生命值">
                        <el-input v-model="gameData.FightHP" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="战斗金币">
                        <el-input v-model="gameData.FightGold" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="战斗钻石">
                        <el-input v-model="gameData.FightDiamond" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="战斗芯片">
                        <el-input v-model="gameData.FightChip" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 其他设置 -->
              <el-tab-pane label="其他设置">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="音乐禁用">
                        <el-input v-model="gameData.MusicDisable" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="是否新用户">
                        <el-input v-model="gameData.isNewUser" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="用户ID">
                        <el-input v-model="gameData.UserID" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="24">
                      <el-form-item label="签到物品">
                        <el-input v-model="gameData.signinItem" type="textarea" :rows="2" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 操作历史记录 -->
          <div v-if="operationHistory.length > 0" class="history-section">
            <div class="section-title">
              <el-icon><Timer /></el-icon>
              <span>操作历史</span>
            </div>
            <el-timeline>
              <el-timeline-item
                v-for="(history, index) in operationHistory"
                :key="index"
                :type="history.success ? 'success' : 'danger'"
                :timestamp="history.time"
                size="large"
              >
                {{ history.operation }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Aim,
  Document,
  Download,
  Upload,
  Delete,
  Timer
} from '@element-plus/icons-vue'

// 表单数据
const formData = reactive({
  openId: 'oqyRo5OMEt_xaseBxG-6TB8gUAfI'
})

// 游戏数据
const gameData = reactive({
  v: '',                    // 版本号
  gold: '10000',           // 金币
  roleexp: '',             // 角色经验
  diamond: '200',          // 钻石
  shortcutState: '',       // 快捷状态
  tili: '20',              // 体力
  tiliTime: '',           // 体力恢复时间
  gunID: '1',             // 枪支ID
  clothID: '101',         // 服装ID
  equipments: '1,101,201', // 装备列表
  equipmentslv: '1,1,1',   // 装备等级
  equipmentslvnum: '0,0,0', // 装备等级数量
  chapterID: '1',         // 章节ID
  level: '1',             // 等级
  talent: '0,0,0,0,0,0',  // 天赋
  talentextra: '',        // 额外天赋
  talentAttack: '',       // 攻击天赋
  talentHP: '',           // 生命天赋
  talentAddHP: '',        // 额外生命天赋
  talentDefense: '',      // 防御天赋
  talentGold: '',         // 金币天赋
  guide1: '',             // 新手引导1
  guide2: '',             // 新手引导2
  guide301: '',           // 新手引导301
  guide302: '',           // 新手引导302
  guide303: '',           // 新手引导303
  MusicDisable: '',       // 音乐禁用
  OfflineGoldPool: '',    // 离线金币池
  lastOfflineTime: '1733985625564', // 最后离线时间
  lastOpenTick: '1733985625572',    // 最后打开时间
  REGISTER_DATE: '',      // 注册日期
  sidebarState: '0',      // 侧边栏状态
  goldBoxNum: '',         // 金币宝箱数��
  goldBoxNumlEVELNum: '1', // 金币宝箱等级数量
  diaBoxNum: '',          // 钻石宝箱数量
  diaBoxNumNum: '1',      // 钻石宝箱数量数量
  equipLevel: '',         // 装备等级
  equipLevelNUM: '1',     // 装备等级数量
  questPassLevel: '',     // 任务通关等级
  questPassLevelNum: '1', // 任务通关等级数量
  questChapterNum: '1',   // 任务章节数量
  killEnemy: '',          // 击杀敌人
  killEnemyNum: '1',      // 击杀敌人数量
  questUseMoney: '',      // 使用金币
  questUseMoneyNum: '1',  // 使用金币数量
  questUseDiamond: '',    // 使用钻石
  questUseDiamondNum: '1', // 使用钻石数量
  talentlv: '0',          // 天赋等级
  talentlvNum: '1',       // 天赋等级数量
  rolelevel: '1',         // 角色等级
  rolelevelNum: '1',      // 角色等级数量
  IsComments: '',         // 是否评论
  TryGunID: '',          // 试用枪支ID
  isNewUser: '10',        // 是否新用户
  currentSigninDay: '',   // 当前签到天数
  signinItem: '[0,1,1,1,1,1,1,1,1,1,1,1,1,1]', // 签到物品
  Year: '2024',          // 年
  month: '12',           // 月
  day: '12',             // 日
  dungeonTime: '',       // 副本时间
  FailedTime: '',        // 失败时间
  FightContinue: '',     // 战斗继续
  FightChapter: '',      // 战斗章节
  FightChapterID: '',    // 战斗章节ID
  FightIsNormal: '',     // 是否普通战斗
  FightLevel: '',        // 战斗等级
  FightRoleLevel: '',    // 战斗角色等级
  FightRoleExp: '',      // 战斗角色经验
  FightSkills: '',       // 战斗技能
  FightHP: '',           // 战斗生命值
  FightGold: '',         // 战斗金币
  FightDiamond: '',      // 战斗钻石
  FightChip: '',         // 战斗芯片
  FightReLife: '',       // 战斗复活
  chapterReward: '1',    // 章节奖励
  DroneLevel: '1',       // 无人机等级
  DroneChip: '',         // 无人机芯片
  BuyDiaNum1: '',        // 购买钻石数量1
  BuyDiaNum2: '',        // 购买钻石数量2
  BuyDiaNum3: '',        // 购买钻石数量3
  BuyDiaNum4: '',        // 购买钻石数量4
  BuyDiaNum5: '',        // 购买钻石数量5
  BuyDiaNum6: '',        // 购买钻石数量6
  GiftIds: '',           // 礼物ID
  SkinID: '201',         // 皮肤ID
  Skins: '',             // 皮肤列表
  UserID: '13065581692000420782', // 用户ID
  GetDroneID: '',        // 获得无人机ID
  SessionKey: '',        // 会话密钥
  OpenId: '',            // 开放ID
  LocalData: ''          // 本地数据
})

// 数据状态
const hasData = ref(false)

// 加载状态
const loading = reactive({
  download: false,
  upload: false
})

// 操作历史
const operationHistory = ref([])

// 下载数据
const handleDownload = async () => {
  if (!formData.openId.trim()) {
    ElMessage.warning('请输入OpenID')
    return
  }

  loading.download = true
  try {
    const response = await fetch('https://games.api.gugudang.com/api/property/get2?' + new URLSearchParams({
      appid: 'wx5285ebcfe44e6ebd',
      openId: formData.openId,
      keys: 'saveData'
    }), {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
        'Accept': '*/*',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Referer': 'https://servicewechat.com/wx5285ebcfe44e6ebd/31/page-frame.html',
        'Accept-Language': 'zh-CN,zh;q=0.9'
      }
    })
    
    const data = await response.json()
    if (data.code === 1 && data.msg === "获取成功") {
      // 解析saveData字符串为JSON对象
      const saveData = JSON.parse(data.data.saveData)
      
      // 更新游戏数据
      Object.assign(gameData, saveData)
      hasData.value = true
      
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: '数据下载成功',
        success: true
      })
      
      ElMessage.success('数据下载成功')
    } else {
      throw new Error(data.msg || '数据下载失败')
    }
  } catch (error) {
    console.error('下载数据失败:', error)
    ElMessage.error(error.message || '数据下载失败')
    
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '下载失败: ' + error.message,
      success: false
    })
  } finally {
    loading.download = false
  }
}

// 上传数据
const handleUpload = async () => {
  if (!formData.openId.trim()) {
    ElMessage.warning('请输入OpenID')
    return
  }

  if (!hasData.value) {
    ElMessage.warning('没有可上传的数据')
    return
  }

  loading.upload = true
  try {
    const params = new URLSearchParams({
      appid: 'wx5285ebcfe44e6ebd',
      openId: formData.openId,
      key: 'saveData',
      val: JSON.stringify(gameData)
    })

    const response = await fetch('https://games.api.gugudang.com/api/property/put2?' + params, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
        'Accept': '*/*',
        'Sec-Fetch-Site': 'cross-site',
        'Sec-Fetch-Mode': 'cors',
        'Sec-Fetch-Dest': 'empty',
        'Referer': 'https://servicewechat.com/wx5285ebcfe44e6ebd/31/page-frame.html',
        'Accept-Language': 'zh-CN,zh;q=0.9'
      },
      body: '{}'
    })
    
    const data = await response.json()
    if (data.code === 1) {
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: '数据上传成功',
        success: true
      })
      
      ElMessage.success('数据上传成功')
    } else {
      throw new Error(data.msg || '数据上传失败')
    }
  } catch (error) {
    console.error('上传数据失败:', error)
    ElMessage.error(error.message || '数据上传失败')
    
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '上传失败: ' + error.message,
      success: false
    })
  } finally {
    loading.upload = false
  }
}

// 清空数据
const handleClearData = () => {
  ElMessageBox.confirm(
    '确定要清空数据吗？此操作不可恢复',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      dataInput.value = ''
      hasData.value = false
      
      ElMessage({
        type: 'success',
        message: '数据已清空'
      })
      
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: '清空所有数据',
        success: true
      })
    })
    .catch(() => {
      // 取消清空操作
    })
}
</script>

<style scoped>
.gun-battle {
  padding: 20px;
  height: 100vh;
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
  box-sizing: border-box;
  /* 隐藏滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Chrome, Safari and Opera */
.gun-battle::-webkit-scrollbar {
  display: none;
}

.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  height: auto;
  min-height: calc(100vh - 40px);
}

/* 头部区域样式 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: -20px -20px 24px -20px;
  background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.title .el-icon {
  font-size: 28px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
}

.status-tag {
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  font-weight: 500;
  font-size: 14px;
}

/* 数据输入区域样式 */
.data-input-section {
  margin: 16px 0;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.clear-button {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  gap: 16px;
  margin: 24px 0;
  padding: 0 20px;
}

.action-button {
  flex: 1;
  height: 40px;
}

/* 历史记录区域 */
.history-section {
  margin: 24px 20px;
  padding: 20px;
  background-color: var(--el-fill-color-light);
  border-radius: 8px;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .gun-battle {
    padding: 12px;
  }

  .header-section {
    padding: 16px;
  }

  .action-section {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
  }
}
</style>
