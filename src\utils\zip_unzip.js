// 从 notepad 中复制的 zip_unzip 代码
// ... 完整的代码内容

//const {XMLHttpRequest} = require("xmlhttprequest");
const Z_FIXED$1 = 4
  , Z_BINARY = 0
  , Z_TEXT = 1
  , Z_UNKNOWN$1 = 2;
function zero$1(e) {
    let t = e.length;
    for (; --t >= 0; )
        e[t] = 0
}
const STORED_BLOCK = 0
  , STATIC_TREES = 1
  , DYN_TREES = 2
  , MIN_MATCH$1 = 3
  , MAX_MATCH$1 = 258
  , LENGTH_CODES$1 = 29
  , LITERALS$1 = 256
  , L_CODES$1 = LITERALS$1 + 1 + LENGTH_CODES$1
  , D_CODES$1 = 30
  , BL_CODES$1 = 19
  , HEAP_SIZE$1 = 2 * L_CODES$1 + 1
  , MAX_BITS$1 = 15
  , Buf_size = 16
  , MAX_BL_BITS = 7
  , END_BLOCK = 256
  , REP_3_6 = 16
  , REPZ_3_10 = 17
  , REPZ_11_138 = 18
  , extra_lbits = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0])
  , extra_dbits = new Uint8Array([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13])
  , extra_blbits = new Uint8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 3, 7])
  , bl_order = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15])
  , DIST_CODE_LEN = 512
  , static_ltree = new Array(2 * (L_CODES$1 + 2));
zero$1(static_ltree);
const static_dtree = new Array(2 * D_CODES$1);
zero$1(static_dtree);
const _dist_code = new Array(DIST_CODE_LEN);
zero$1(_dist_code);
const _length_code = new Array(MAX_MATCH$1 - MIN_MATCH$1 + 1);
zero$1(_length_code);
const base_length = new Array(LENGTH_CODES$1);
zero$1(base_length);
const base_dist = new Array(D_CODES$1);
function StaticTreeDesc(e, t, i, s, a) {
    this.static_tree = e,
    this.extra_bits = t,
    this.extra_base = i,
    this.elems = s,
    this.max_length = a,
    this.has_stree = e && e.length
}
let static_l_desc, static_d_desc, static_bl_desc;
function TreeDesc(e, t) {
    this.dyn_tree = e,
    this.max_code = 0,
    this.stat_desc = t
}
zero$1(base_dist);
const d_code = e=>e < 256 ? _dist_code[e] : _dist_code[256 + (e >>> 7)]
  , put_short = (e,t)=>{
    e.pending_buf[e.pending++] = 255 & t,
    e.pending_buf[e.pending++] = t >>> 8 & 255
}
  , send_bits = (e,t,i)=>{
    e.bi_valid > Buf_size - i ? (e.bi_buf |= t << e.bi_valid & 65535,
    put_short(e, e.bi_buf),
    e.bi_buf = t >> Buf_size - e.bi_valid,
    e.bi_valid += i - Buf_size) : (e.bi_buf |= t << e.bi_valid & 65535,
    e.bi_valid += i)
}
  , send_code = (e,t,i)=>{
    send_bits(e, i[2 * t], i[2 * t + 1])
}
  , bi_reverse = (e,t)=>{
    let i = 0;
    do {
        i |= 1 & e,
        e >>>= 1,
        i <<= 1
    } while (--t > 0);
    return i >>> 1
}
  , bi_flush = e=>{
    16 === e.bi_valid ? (put_short(e, e.bi_buf),
    e.bi_buf = 0,
    e.bi_valid = 0) : e.bi_valid >= 8 && (e.pending_buf[e.pending++] = 255 & e.bi_buf,
    e.bi_buf >>= 8,
    e.bi_valid -= 8)
}
  , gen_bitlen = (e,t)=>{
    const i = t.dyn_tree
      , s = t.max_code
      , a = t.stat_desc.static_tree
      , o = t.stat_desc.has_stree
      , n = t.stat_desc.extra_bits
      , r = t.stat_desc.extra_base
      , l = t.stat_desc.max_length;
    let c, d, h, _, u, g, m = 0;
    for (_ = 0; _ <= MAX_BITS$1; _++)
        e.bl_count[_] = 0;
    for (i[2 * e.heap[e.heap_max] + 1] = 0,
    c = e.heap_max + 1; c < HEAP_SIZE$1; c++)
        (_ = i[2 * i[2 * (d = e.heap[c]) + 1] + 1] + 1) > l && (_ = l,
        m++),
        i[2 * d + 1] = _,
        d > s || (e.bl_count[_]++,
        u = 0,
        d >= r && (u = n[d - r]),
        g = i[2 * d],
        e.opt_len += g * (_ + u),
        o && (e.static_len += g * (a[2 * d + 1] + u)));
    if (0 !== m) {
        do {
            for (_ = l - 1; 0 === e.bl_count[_]; )
                _--;
            e.bl_count[_]--,
            e.bl_count[_ + 1] += 2,
            e.bl_count[l]--,
            m -= 2
        } while (m > 0);
        for (_ = l; 0 !== _; _--)
            for (d = e.bl_count[_]; 0 !== d; )
                (h = e.heap[--c]) > s || (i[2 * h + 1] !== _ && (e.opt_len += (_ - i[2 * h + 1]) * i[2 * h],
                i[2 * h + 1] = _),
                d--)
    }
}
  , gen_codes = (e,t,i)=>{
    const s = new Array(MAX_BITS$1 + 1);
    let a, o, n = 0;
    for (a = 1; a <= MAX_BITS$1; a++)
        s[a] = n = n + i[a - 1] << 1;
    for (o = 0; o <= t; o++) {
        let t = e[2 * o + 1];
        0 !== t && (e[2 * o] = bi_reverse(s[t]++, t))
    }
}
  , tr_static_init = ()=>{
    let e, t, i, s, a;
    const o = new Array(MAX_BITS$1 + 1);
    for (i = 0,
    s = 0; s < LENGTH_CODES$1 - 1; s++)
        for (base_length[s] = i,
        e = 0; e < 1 << extra_lbits[s]; e++)
            _length_code[i++] = s;
    for (_length_code[i - 1] = s,
    a = 0,
    s = 0; s < 16; s++)
        for (base_dist[s] = a,
        e = 0; e < 1 << extra_dbits[s]; e++)
            _dist_code[a++] = s;
    for (a >>= 7; s < D_CODES$1; s++)
        for (base_dist[s] = a << 7,
        e = 0; e < 1 << extra_dbits[s] - 7; e++)
            _dist_code[256 + a++] = s;
    for (t = 0; t <= MAX_BITS$1; t++)
        o[t] = 0;
    for (e = 0; e <= 143; )
        static_ltree[2 * e + 1] = 8,
        e++,
        o[8]++;
    for (; e <= 255; )
        static_ltree[2 * e + 1] = 9,
        e++,
        o[9]++;
    for (; e <= 279; )
        static_ltree[2 * e + 1] = 7,
        e++,
        o[7]++;
    for (; e <= 287; )
        static_ltree[2 * e + 1] = 8,
        e++,
        o[8]++;
    for (gen_codes(static_ltree, L_CODES$1 + 1, o),
    e = 0; e < D_CODES$1; e++)
        static_dtree[2 * e + 1] = 5,
        static_dtree[2 * e] = bi_reverse(e, 5);
    static_l_desc = new StaticTreeDesc(static_ltree,extra_lbits,LITERALS$1 + 1,L_CODES$1,MAX_BITS$1),
    static_d_desc = new StaticTreeDesc(static_dtree,extra_dbits,0,D_CODES$1,MAX_BITS$1),
    static_bl_desc = new StaticTreeDesc(new Array(0),extra_blbits,0,BL_CODES$1,MAX_BL_BITS)
}
  , init_block = e=>{
    let t;
    for (t = 0; t < L_CODES$1; t++)
        e.dyn_ltree[2 * t] = 0;
    for (t = 0; t < D_CODES$1; t++)
        e.dyn_dtree[2 * t] = 0;
    for (t = 0; t < BL_CODES$1; t++)
        e.bl_tree[2 * t] = 0;
    e.dyn_ltree[2 * END_BLOCK] = 1,
    e.opt_len = e.static_len = 0,
    e.last_lit = e.matches = 0
}
  , bi_windup = e=>{
    e.bi_valid > 8 ? put_short(e, e.bi_buf) : e.bi_valid > 0 && (e.pending_buf[e.pending++] = e.bi_buf),
    e.bi_buf = 0,
    e.bi_valid = 0
}
  , copy_block = (e,t,i,s)=>{
    bi_windup(e),
    s && (put_short(e, i),
    put_short(e, ~i)),
    e.pending_buf.set(e.window.subarray(t, t + i), e.pending),
    e.pending += i
}
  , smaller = (e,t,i,s)=>{
    const a = 2 * t
      , o = 2 * i;
    return e[a] < e[o] || e[a] === e[o] && s[t] <= s[i]
}
  , pqdownheap = (e,t,i)=>{
    const s = e.heap[i];
    let a = i << 1;
    for (; a <= e.heap_len && (a < e.heap_len && smaller(t, e.heap[a + 1], e.heap[a], e.depth) && a++,
    !smaller(t, s, e.heap[a], e.depth)); )
        e.heap[i] = e.heap[a],
        i = a,
        a <<= 1;
    e.heap[i] = s
}
  , compress_block = (e,t,i)=>{
    let s, a, o, n, r = 0;
    if (0 !== e.last_lit)
        do {
            s = e.pending_buf[e.d_buf + 2 * r] << 8 | e.pending_buf[e.d_buf + 2 * r + 1],
            a = e.pending_buf[e.l_buf + r],
            r++,
            0 === s ? send_code(e, a, t) : (o = _length_code[a],
            send_code(e, o + LITERALS$1 + 1, t),
            0 !== (n = extra_lbits[o]) && (a -= base_length[o],
            send_bits(e, a, n)),
            o = d_code(--s),
            send_code(e, o, i),
            0 !== (n = extra_dbits[o]) && (s -= base_dist[o],
            send_bits(e, s, n)))
        } while (r < e.last_lit);
    send_code(e, END_BLOCK, t)
}
  , build_tree = (e,t)=>{
    const i = t.dyn_tree
      , s = t.stat_desc.static_tree
      , a = t.stat_desc.has_stree
      , o = t.stat_desc.elems;
    let n, r, l, c = -1;
    for (e.heap_len = 0,
    e.heap_max = HEAP_SIZE$1,
    n = 0; n < o; n++)
        0 !== i[2 * n] ? (e.heap[++e.heap_len] = c = n,
        e.depth[n] = 0) : i[2 * n + 1] = 0;
    for (; e.heap_len < 2; )
        i[2 * (l = e.heap[++e.heap_len] = c < 2 ? ++c : 0)] = 1,
        e.depth[l] = 0,
        e.opt_len--,
        a && (e.static_len -= s[2 * l + 1]);
    for (t.max_code = c,
    n = e.heap_len >> 1; n >= 1; n--)
        pqdownheap(e, i, n);
    l = o;
    do {
        n = e.heap[1],
        e.heap[1] = e.heap[e.heap_len--],
        pqdownheap(e, i, 1),
        r = e.heap[1],
        e.heap[--e.heap_max] = n,
        e.heap[--e.heap_max] = r,
        i[2 * l] = i[2 * n] + i[2 * r],
        e.depth[l] = (e.depth[n] >= e.depth[r] ? e.depth[n] : e.depth[r]) + 1,
        i[2 * n + 1] = i[2 * r + 1] = l,
        e.heap[1] = l++,
        pqdownheap(e, i, 1)
    } while (e.heap_len >= 2);
    e.heap[--e.heap_max] = e.heap[1],
    gen_bitlen(e, t),
    gen_codes(i, c, e.bl_count)
}
  , scan_tree = (e,t,i)=>{
    let s, a, o = -1, n = t[1], r = 0, l = 7, c = 4;
    for (0 === n && (l = 138,
    c = 3),
    t[2 * (i + 1) + 1] = 65535,
    s = 0; s <= i; s++)
        a = n,
        n = t[2 * (s + 1) + 1],
        ++r < l && a === n || (r < c ? e.bl_tree[2 * a] += r : 0 !== a ? (a !== o && e.bl_tree[2 * a]++,
        e.bl_tree[2 * REP_3_6]++) : r <= 10 ? e.bl_tree[2 * REPZ_3_10]++ : e.bl_tree[2 * REPZ_11_138]++,
        r = 0,
        o = a,
        0 === n ? (l = 138,
        c = 3) : a === n ? (l = 6,
        c = 3) : (l = 7,
        c = 4))
}
  , send_tree = (e,t,i)=>{
    let s, a, o = -1, n = t[1], r = 0, l = 7, c = 4;
    for (0 === n && (l = 138,
    c = 3),
    s = 0; s <= i; s++)
        if (a = n,
        n = t[2 * (s + 1) + 1],
        !(++r < l && a === n)) {
            if (r < c)
                do {
                    send_code(e, a, e.bl_tree)
                } while (0 != --r);
            else
                0 !== a ? (a !== o && (send_code(e, a, e.bl_tree),
                r--),
                send_code(e, REP_3_6, e.bl_tree),
                send_bits(e, r - 3, 2)) : r <= 10 ? (send_code(e, REPZ_3_10, e.bl_tree),
                send_bits(e, r - 3, 3)) : (send_code(e, REPZ_11_138, e.bl_tree),
                send_bits(e, r - 11, 7));
            r = 0,
            o = a,
            0 === n ? (l = 138,
            c = 3) : a === n ? (l = 6,
            c = 3) : (l = 7,
            c = 4)
        }
}
  , build_bl_tree = e=>{
    let t;
    for (scan_tree(e, e.dyn_ltree, e.l_desc.max_code),
    scan_tree(e, e.dyn_dtree, e.d_desc.max_code),
    build_tree(e, e.bl_desc),
    t = BL_CODES$1 - 1; t >= 3 && 0 === e.bl_tree[2 * bl_order[t] + 1]; t--)
        ;
    return e.opt_len += 3 * (t + 1) + 5 + 5 + 4,
    t
}
  , send_all_trees = (e,t,i,s)=>{
    let a;
    for (send_bits(e, t - 257, 5),
    send_bits(e, i - 1, 5),
    send_bits(e, s - 4, 4),
    a = 0; a < s; a++)
        send_bits(e, e.bl_tree[2 * bl_order[a] + 1], 3);
    send_tree(e, e.dyn_ltree, t - 1),
    send_tree(e, e.dyn_dtree, i - 1)
}
  , detect_data_type = e=>{
    let t, i = 4093624447;
    for (t = 0; t <= 31; t++,
    i >>>= 1)
        if (1 & i && 0 !== e.dyn_ltree[2 * t])
            return Z_BINARY;
    if (0 !== e.dyn_ltree[18] || 0 !== e.dyn_ltree[20] || 0 !== e.dyn_ltree[26])
        return Z_TEXT;
    for (t = 32; t < LITERALS$1; t++)
        if (0 !== e.dyn_ltree[2 * t])
            return Z_TEXT;
    return Z_BINARY
}
;
let static_init_done = !1;
const _tr_init$1 = e=>{
    static_init_done || (tr_static_init(),
    static_init_done = !0),
    e.l_desc = new TreeDesc(e.dyn_ltree,static_l_desc),
    e.d_desc = new TreeDesc(e.dyn_dtree,static_d_desc),
    e.bl_desc = new TreeDesc(e.bl_tree,static_bl_desc),
    e.bi_buf = 0,
    e.bi_valid = 0,
    init_block(e)
}
  , _tr_stored_block$1 = (e,t,i,s)=>{
    send_bits(e, (STORED_BLOCK << 1) + (s ? 1 : 0), 3),
    copy_block(e, t, i, !0)
}
  , _tr_align$1 = e=>{
    send_bits(e, STATIC_TREES << 1, 3),
    send_code(e, END_BLOCK, static_ltree),
    bi_flush(e)
}
  , _tr_flush_block$1 = (e,t,i,s)=>{
    let a, o, n = 0;
    e.level > 0 ? (e.strm.data_type === Z_UNKNOWN$1 && (e.strm.data_type = detect_data_type(e)),
    build_tree(e, e.l_desc),
    build_tree(e, e.d_desc),
    n = build_bl_tree(e),
    a = e.opt_len + 3 + 7 >>> 3,
    (o = e.static_len + 3 + 7 >>> 3) <= a && (a = o)) : a = o = i + 5,
    i + 4 <= a && -1 !== t ? _tr_stored_block$1(e, t, i, s) : e.strategy === Z_FIXED$1 || o === a ? (send_bits(e, (STATIC_TREES << 1) + (s ? 1 : 0), 3),
    compress_block(e, static_ltree, static_dtree)) : (send_bits(e, (DYN_TREES << 1) + (s ? 1 : 0), 3),
    send_all_trees(e, e.l_desc.max_code + 1, e.d_desc.max_code + 1, n + 1),
    compress_block(e, e.dyn_ltree, e.dyn_dtree)),
    init_block(e),
    s && bi_windup(e)
}
  , _tr_tally$1 = (e,t,i)=>(e.pending_buf[e.d_buf + 2 * e.last_lit] = t >>> 8 & 255,
e.pending_buf[e.d_buf + 2 * e.last_lit + 1] = 255 & t,
e.pending_buf[e.l_buf + e.last_lit] = 255 & i,
e.last_lit++,
0 === t ? e.dyn_ltree[2 * i]++ : (e.matches++,
t--,
e.dyn_ltree[2 * (_length_code[i] + LITERALS$1 + 1)]++,
e.dyn_dtree[2 * d_code(t)]++),
e.last_lit === e.lit_bufsize - 1);
var _tr_init_1 = _tr_init$1
  , _tr_stored_block_1 = _tr_stored_block$1
  , _tr_flush_block_1 = _tr_flush_block$1
  , _tr_tally_1 = _tr_tally$1
  , _tr_align_1 = _tr_align$1
  , trees = {
    _tr_init: _tr_init_1,
    _tr_stored_block: _tr_stored_block_1,
    _tr_flush_block: _tr_flush_block_1,
    _tr_tally: _tr_tally_1,
    _tr_align: _tr_align_1
};
const adler32 = (e,t,i,s)=>{
    let a = 65535 & e | 0
      , o = e >>> 16 & 65535 | 0
      , n = 0;
    for (; 0 !== i; ) {
        i -= n = i > 2e3 ? 2e3 : i;
        do {
            o = o + (a = a + t[s++] | 0) | 0
        } while (--n);
        a %= 65521,
        o %= 65521
    }
    return a | o << 16 | 0
}
;
var adler32_1 = adler32;
const makeTable = ()=>{
    let e, t = [];
    for (var i = 0; i < 256; i++) {
        e = i;
        for (var s = 0; s < 8; s++)
            e = 1 & e ? 3988292384 ^ e >>> 1 : e >>> 1;
        t[i] = e
    }
    return t
}
  , crcTable = new Uint32Array(makeTable())
  , crc32 = (e,t,i,s)=>{
    const a = crcTable
      , o = s + i;
    e ^= -1;
    for (let i = s; i < o; i++)
        e = e >>> 8 ^ a[255 & (e ^ t[i])];
    return -1 ^ e
}
;
var crc32_1 = crc32
  , messages = {
    2: "need dictionary",
    1: "stream end",
    0: "",
    "-1": "file error",
    "-2": "stream error",
    "-3": "data error",
    "-4": "insufficient memory",
    "-5": "buffer error",
    "-6": "incompatible version"
}
  , constants$2 = {
    Z_NO_FLUSH: 0,
    Z_PARTIAL_FLUSH: 1,
    Z_SYNC_FLUSH: 2,
    Z_FULL_FLUSH: 3,
    Z_FINISH: 4,
    Z_BLOCK: 5,
    Z_TREES: 6,
    Z_OK: 0,
    Z_STREAM_END: 1,
    Z_NEED_DICT: 2,
    Z_ERRNO: -1,
    Z_STREAM_ERROR: -2,
    Z_DATA_ERROR: -3,
    Z_MEM_ERROR: -4,
    Z_BUF_ERROR: -5,
    Z_NO_COMPRESSION: 0,
    Z_BEST_SPEED: 1,
    Z_BEST_COMPRESSION: 9,
    Z_DEFAULT_COMPRESSION: -1,
    Z_FILTERED: 1,
    Z_HUFFMAN_ONLY: 2,
    Z_RLE: 3,
    Z_FIXED: 4,
    Z_DEFAULT_STRATEGY: 0,
    Z_BINARY: 0,
    Z_TEXT: 1,
    Z_UNKNOWN: 2,
    Z_DEFLATED: 8
};
const {_tr_init: _tr_init, _tr_stored_block: _tr_stored_block, _tr_flush_block: _tr_flush_block, _tr_tally: _tr_tally, _tr_align: _tr_align} = trees
  , {Z_NO_FLUSH: Z_NO_FLUSH$2, Z_PARTIAL_FLUSH: Z_PARTIAL_FLUSH, Z_FULL_FLUSH: Z_FULL_FLUSH$1, Z_FINISH: Z_FINISH$3, Z_BLOCK: Z_BLOCK$1, Z_OK: Z_OK$3, Z_STREAM_END: Z_STREAM_END$3, Z_STREAM_ERROR: Z_STREAM_ERROR$2, Z_DATA_ERROR: Z_DATA_ERROR$2, Z_BUF_ERROR: Z_BUF_ERROR$1, Z_DEFAULT_COMPRESSION: Z_DEFAULT_COMPRESSION$1, Z_FILTERED: Z_FILTERED, Z_HUFFMAN_ONLY: Z_HUFFMAN_ONLY, Z_RLE: Z_RLE, Z_FIXED: Z_FIXED, Z_DEFAULT_STRATEGY: Z_DEFAULT_STRATEGY$1, Z_UNKNOWN: Z_UNKNOWN, Z_DEFLATED: Z_DEFLATED$2} = constants$2
  , MAX_MEM_LEVEL = 9
  , MAX_WBITS$1 = 15
  , DEF_MEM_LEVEL = 8
  , LENGTH_CODES = 29
  , LITERALS = 256
  , L_CODES = LITERALS + 1 + LENGTH_CODES
  , D_CODES = 30
  , BL_CODES = 19
  , HEAP_SIZE = 2 * L_CODES + 1
  , MAX_BITS = 15
  , MIN_MATCH = 3
  , MAX_MATCH = 258
  , MIN_LOOKAHEAD = MAX_MATCH + MIN_MATCH + 1
  , PRESET_DICT = 32
  , INIT_STATE = 42
  , EXTRA_STATE = 69
  , NAME_STATE = 73
  , COMMENT_STATE = 91
  , HCRC_STATE = 103
  , BUSY_STATE = 113
  , FINISH_STATE = 666
  , BS_NEED_MORE = 1
  , BS_BLOCK_DONE = 2
  , BS_FINISH_STARTED = 3
  , BS_FINISH_DONE = 4
  , OS_CODE = 3
  , err = (e,t)=>(e.msg = messages[t],
t)
  , rank = e=>(e << 1) - (e > 4 ? 9 : 0)
  , zero = e=>{
    let t = e.length;
    for (; --t >= 0; )
        e[t] = 0
}
;
let HASH_ZLIB = (e,t,i)=>(t << e.hash_shift ^ i) & e.hash_mask
  , HASH = HASH_ZLIB;
const flush_pending = e=>{
    const t = e.state;
    let i = t.pending;
    i > e.avail_out && (i = e.avail_out),
    0 !== i && (e.output.set(t.pending_buf.subarray(t.pending_out, t.pending_out + i), e.next_out),
    e.next_out += i,
    t.pending_out += i,
    e.total_out += i,
    e.avail_out -= i,
    t.pending -= i,
    0 === t.pending && (t.pending_out = 0))
}
  , flush_block_only = (e,t)=>{
    _tr_flush_block(e, e.block_start >= 0 ? e.block_start : -1, e.strstart - e.block_start, t),
    e.block_start = e.strstart,
    flush_pending(e.strm)
}
  , put_byte = (e,t)=>{
    e.pending_buf[e.pending++] = t
}
  , putShortMSB = (e,t)=>{
    e.pending_buf[e.pending++] = t >>> 8 & 255,
    e.pending_buf[e.pending++] = 255 & t
}
  , read_buf = (e,t,i,s)=>{
    let a = e.avail_in;
    return a > s && (a = s),
    0 === a ? 0 : (e.avail_in -= a,
    t.set(e.input.subarray(e.next_in, e.next_in + a), i),
    1 === e.state.wrap ? e.adler = adler32_1(e.adler, t, a, i) : 2 === e.state.wrap && (e.adler = crc32_1(e.adler, t, a, i)),
    e.next_in += a,
    e.total_in += a,
    a)
}
  , longest_match = (e,t)=>{
    let i, s, a = e.max_chain_length, o = e.strstart, n = e.prev_length, r = e.nice_match;
    const l = e.strstart > e.w_size - MIN_LOOKAHEAD ? e.strstart - (e.w_size - MIN_LOOKAHEAD) : 0
      , c = e.window
      , d = e.w_mask
      , h = e.prev
      , _ = e.strstart + MAX_MATCH;
    let u = c[o + n - 1]
      , g = c[o + n];
    e.prev_length >= e.good_match && (a >>= 2),
    r > e.lookahead && (r = e.lookahead);
    do {
        if (c[(i = t) + n] === g && c[i + n - 1] === u && c[i] === c[o] && c[++i] === c[o + 1]) {
            o += 2,
            i++;
            do {} while (c[++o] === c[++i] && c[++o] === c[++i] && c[++o] === c[++i] && c[++o] === c[++i] && c[++o] === c[++i] && c[++o] === c[++i] && c[++o] === c[++i] && c[++o] === c[++i] && o < _);
            if (s = MAX_MATCH - (_ - o),
            o = _ - MAX_MATCH,
            s > n) {
                if (e.match_start = t,
                n = s,
                s >= r)
                    break;
                u = c[o + n - 1],
                g = c[o + n]
            }
        }
    } while ((t = h[t & d]) > l && 0 != --a);
    return n <= e.lookahead ? n : e.lookahead
}
  , fill_window = e=>{
    const t = e.w_size;
    let i, s, a, o, n;
    do {
        if (o = e.window_size - e.lookahead - e.strstart,
        e.strstart >= t + (t - MIN_LOOKAHEAD)) {
            e.window.set(e.window.subarray(t, t + t), 0),
            e.match_start -= t,
            e.strstart -= t,
            e.block_start -= t,
            i = s = e.hash_size;
            do {
                a = e.head[--i],
                e.head[i] = a >= t ? a - t : 0
            } while (--s);
            i = s = t;
            do {
                a = e.prev[--i],
                e.prev[i] = a >= t ? a - t : 0
            } while (--s);
            o += t
        }
        if (0 === e.strm.avail_in)
            break;
        if (s = read_buf(e.strm, e.window, e.strstart + e.lookahead, o),
        e.lookahead += s,
        e.lookahead + e.insert >= MIN_MATCH)
            for (n = e.strstart - e.insert,
            e.ins_h = e.window[n],
            e.ins_h = HASH(e, e.ins_h, e.window[n + 1]); e.insert && (e.ins_h = HASH(e, e.ins_h, e.window[n + MIN_MATCH - 1]),
            e.prev[n & e.w_mask] = e.head[e.ins_h],
            e.head[e.ins_h] = n,
            n++,
            e.insert--,
            !(e.lookahead + e.insert < MIN_MATCH)); )
                ;
    } while (e.lookahead < MIN_LOOKAHEAD && 0 !== e.strm.avail_in)
}
  , deflate_stored = (e,t)=>{
    let i = 65535;
    for (i > e.pending_buf_size - 5 && (i = e.pending_buf_size - 5); ; ) {
        if (e.lookahead <= 1) {
            if (fill_window(e),
            0 === e.lookahead && t === Z_NO_FLUSH$2)
                return BS_NEED_MORE;
            if (0 === e.lookahead)
                break
        }
        e.strstart += e.lookahead,
        e.lookahead = 0;
        const s = e.block_start + i;
        if ((0 === e.strstart || e.strstart >= s) && (e.lookahead = e.strstart - s,
        e.strstart = s,
        flush_block_only(e, !1),
        0 === e.strm.avail_out))
            return BS_NEED_MORE;
        if (e.strstart - e.block_start >= e.w_size - MIN_LOOKAHEAD && (flush_block_only(e, !1),
        0 === e.strm.avail_out))
            return BS_NEED_MORE
    }
    return e.insert = 0,
    t === Z_FINISH$3 ? (flush_block_only(e, !0),
    0 === e.strm.avail_out ? BS_FINISH_STARTED : BS_FINISH_DONE) : (e.strstart > e.block_start && (flush_block_only(e, !1),
    e.strm.avail_out),
    BS_NEED_MORE)
}
  , deflate_fast = (e,t)=>{
    let i, s;
    for (; ; ) {
        if (e.lookahead < MIN_LOOKAHEAD) {
            if (fill_window(e),
            e.lookahead < MIN_LOOKAHEAD && t === Z_NO_FLUSH$2)
                return BS_NEED_MORE;
            if (0 === e.lookahead)
                break
        }
        if (i = 0,
        e.lookahead >= MIN_MATCH && (e.ins_h = HASH(e, e.ins_h, e.window[e.strstart + MIN_MATCH - 1]),
        i = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h],
        e.head[e.ins_h] = e.strstart),
        0 !== i && e.strstart - i <= e.w_size - MIN_LOOKAHEAD && (e.match_length = longest_match(e, i)),
        e.match_length >= MIN_MATCH)
            if (s = _tr_tally(e, e.strstart - e.match_start, e.match_length - MIN_MATCH),
            e.lookahead -= e.match_length,
            e.match_length <= e.max_lazy_match && e.lookahead >= MIN_MATCH) {
                e.match_length--;
                do {
                    e.strstart++,
                    e.ins_h = HASH(e, e.ins_h, e.window[e.strstart + MIN_MATCH - 1]),
                    i = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h],
                    e.head[e.ins_h] = e.strstart
                } while (0 != --e.match_length);
                e.strstart++
            } else
                e.strstart += e.match_length,
                e.match_length = 0,
                e.ins_h = e.window[e.strstart],
                e.ins_h = HASH(e, e.ins_h, e.window[e.strstart + 1]);
        else
            s = _tr_tally(e, 0, e.window[e.strstart]),
            e.lookahead--,
            e.strstart++;
        if (s && (flush_block_only(e, !1),
        0 === e.strm.avail_out))
            return BS_NEED_MORE
    }
    return e.insert = e.strstart < MIN_MATCH - 1 ? e.strstart : MIN_MATCH - 1,
    t === Z_FINISH$3 ? (flush_block_only(e, !0),
    0 === e.strm.avail_out ? BS_FINISH_STARTED : BS_FINISH_DONE) : e.last_lit && (flush_block_only(e, !1),
    0 === e.strm.avail_out) ? BS_NEED_MORE : BS_BLOCK_DONE
}
  , deflate_slow = (e,t)=>{
    let i, s, a;
    for (; ; ) {
        if (e.lookahead < MIN_LOOKAHEAD) {
            if (fill_window(e),
            e.lookahead < MIN_LOOKAHEAD && t === Z_NO_FLUSH$2)
                return BS_NEED_MORE;
            if (0 === e.lookahead)
                break
        }
        if (i = 0,
        e.lookahead >= MIN_MATCH && (e.ins_h = HASH(e, e.ins_h, e.window[e.strstart + MIN_MATCH - 1]),
        i = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h],
        e.head[e.ins_h] = e.strstart),
        e.prev_length = e.match_length,
        e.prev_match = e.match_start,
        e.match_length = MIN_MATCH - 1,
        0 !== i && e.prev_length < e.max_lazy_match && e.strstart - i <= e.w_size - MIN_LOOKAHEAD && (e.match_length = longest_match(e, i),
        e.match_length <= 5 && (e.strategy === Z_FILTERED || e.match_length === MIN_MATCH && e.strstart - e.match_start > 4096) && (e.match_length = MIN_MATCH - 1)),
        e.prev_length >= MIN_MATCH && e.match_length <= e.prev_length) {
            a = e.strstart + e.lookahead - MIN_MATCH,
            s = _tr_tally(e, e.strstart - 1 - e.prev_match, e.prev_length - MIN_MATCH),
            e.lookahead -= e.prev_length - 1,
            e.prev_length -= 2;
            do {
                ++e.strstart <= a && (e.ins_h = HASH(e, e.ins_h, e.window[e.strstart + MIN_MATCH - 1]),
                i = e.prev[e.strstart & e.w_mask] = e.head[e.ins_h],
                e.head[e.ins_h] = e.strstart)
            } while (0 != --e.prev_length);
            if (e.match_available = 0,
            e.match_length = MIN_MATCH - 1,
            e.strstart++,
            s && (flush_block_only(e, !1),
            0 === e.strm.avail_out))
                return BS_NEED_MORE
        } else if (e.match_available) {
            if ((s = _tr_tally(e, 0, e.window[e.strstart - 1])) && flush_block_only(e, !1),
            e.strstart++,
            e.lookahead--,
            0 === e.strm.avail_out)
                return BS_NEED_MORE
        } else
            e.match_available = 1,
            e.strstart++,
            e.lookahead--
    }
    return e.match_available && (s = _tr_tally(e, 0, e.window[e.strstart - 1]),
    e.match_available = 0),
    e.insert = e.strstart < MIN_MATCH - 1 ? e.strstart : MIN_MATCH - 1,
    t === Z_FINISH$3 ? (flush_block_only(e, !0),
    0 === e.strm.avail_out ? BS_FINISH_STARTED : BS_FINISH_DONE) : e.last_lit && (flush_block_only(e, !1),
    0 === e.strm.avail_out) ? BS_NEED_MORE : BS_BLOCK_DONE
}
  , deflate_rle = (e,t)=>{
    let i, s, a, o;
    const n = e.window;
    for (; ; ) {
        if (e.lookahead <= MAX_MATCH) {
            if (fill_window(e),
            e.lookahead <= MAX_MATCH && t === Z_NO_FLUSH$2)
                return BS_NEED_MORE;
            if (0 === e.lookahead)
                break
        }
        if (e.match_length = 0,
        e.lookahead >= MIN_MATCH && e.strstart > 0 && (s = n[a = e.strstart - 1]) === n[++a] && s === n[++a] && s === n[++a]) {
            o = e.strstart + MAX_MATCH;
            do {} while (s === n[++a] && s === n[++a] && s === n[++a] && s === n[++a] && s === n[++a] && s === n[++a] && s === n[++a] && s === n[++a] && a < o);
            e.match_length = MAX_MATCH - (o - a),
            e.match_length > e.lookahead && (e.match_length = e.lookahead)
        }
        if (e.match_length >= MIN_MATCH ? (i = _tr_tally(e, 1, e.match_length - MIN_MATCH),
        e.lookahead -= e.match_length,
        e.strstart += e.match_length,
        e.match_length = 0) : (i = _tr_tally(e, 0, e.window[e.strstart]),
        e.lookahead--,
        e.strstart++),
        i && (flush_block_only(e, !1),
        0 === e.strm.avail_out))
            return BS_NEED_MORE
    }
    return e.insert = 0,
    t === Z_FINISH$3 ? (flush_block_only(e, !0),
    0 === e.strm.avail_out ? BS_FINISH_STARTED : BS_FINISH_DONE) : e.last_lit && (flush_block_only(e, !1),
    0 === e.strm.avail_out) ? BS_NEED_MORE : BS_BLOCK_DONE
}
  , deflate_huff = (e,t)=>{
    let i;
    for (; ; ) {
        if (0 === e.lookahead && (fill_window(e),
        0 === e.lookahead)) {
            if (t === Z_NO_FLUSH$2)
                return BS_NEED_MORE;
            break
        }
        if (e.match_length = 0,
        i = _tr_tally(e, 0, e.window[e.strstart]),
        e.lookahead--,
        e.strstart++,
        i && (flush_block_only(e, !1),
        0 === e.strm.avail_out))
            return BS_NEED_MORE
    }
    return e.insert = 0,
    t === Z_FINISH$3 ? (flush_block_only(e, !0),
    0 === e.strm.avail_out ? BS_FINISH_STARTED : BS_FINISH_DONE) : e.last_lit && (flush_block_only(e, !1),
    0 === e.strm.avail_out) ? BS_NEED_MORE : BS_BLOCK_DONE
}
;
function Config(e, t, i, s, a) {
    this.good_length = e,
    this.max_lazy = t,
    this.nice_length = i,
    this.max_chain = s,
    this.func = a
}
const configuration_table = [new Config(0,0,0,0,deflate_stored), new Config(4,4,8,4,deflate_fast), new Config(4,5,16,8,deflate_fast), new Config(4,6,32,32,deflate_fast), new Config(4,4,16,16,deflate_slow), new Config(8,16,32,32,deflate_slow), new Config(8,16,128,128,deflate_slow), new Config(8,32,128,256,deflate_slow), new Config(32,128,258,1024,deflate_slow), new Config(32,258,258,4096,deflate_slow)]
  , lm_init = e=>{
    e.window_size = 2 * e.w_size,
    zero(e.head),
    e.max_lazy_match = configuration_table[e.level].max_lazy,
    e.good_match = configuration_table[e.level].good_length,
    e.nice_match = configuration_table[e.level].nice_length,
    e.max_chain_length = configuration_table[e.level].max_chain,
    e.strstart = 0,
    e.block_start = 0,
    e.lookahead = 0,
    e.insert = 0,
    e.match_length = e.prev_length = MIN_MATCH - 1,
    e.match_available = 0,
    e.ins_h = 0
}
;
function DeflateState() {
    this.strm = null,
    this.status = 0,
    this.pending_buf = null,
    this.pending_buf_size = 0,
    this.pending_out = 0,
    this.pending = 0,
    this.wrap = 0,
    this.gzhead = null,
    this.gzindex = 0,
    this.method = Z_DEFLATED$2,
    this.last_flush = -1,
    this.w_size = 0,
    this.w_bits = 0,
    this.w_mask = 0,
    this.window = null,
    this.window_size = 0,
    this.prev = null,
    this.head = null,
    this.ins_h = 0,
    this.hash_size = 0,
    this.hash_bits = 0,
    this.hash_mask = 0,
    this.hash_shift = 0,
    this.block_start = 0,
    this.match_length = 0,
    this.prev_match = 0,
    this.match_available = 0,
    this.strstart = 0,
    this.match_start = 0,
    this.lookahead = 0,
    this.prev_length = 0,
    this.max_chain_length = 0,
    this.max_lazy_match = 0,
    this.level = 0,
    this.strategy = 0,
    this.good_match = 0,
    this.nice_match = 0,
    this.dyn_ltree = new Uint16Array(2 * HEAP_SIZE),
    this.dyn_dtree = new Uint16Array(2 * (2 * D_CODES + 1)),
    this.bl_tree = new Uint16Array(2 * (2 * BL_CODES + 1)),
    zero(this.dyn_ltree),
    zero(this.dyn_dtree),
    zero(this.bl_tree),
    this.l_desc = null,
    this.d_desc = null,
    this.bl_desc = null,
    this.bl_count = new Uint16Array(MAX_BITS + 1),
    this.heap = new Uint16Array(2 * L_CODES + 1),
    zero(this.heap),
    this.heap_len = 0,
    this.heap_max = 0,
    this.depth = new Uint16Array(2 * L_CODES + 1),
    zero(this.depth),
    this.l_buf = 0,
    this.lit_bufsize = 0,
    this.last_lit = 0,
    this.d_buf = 0,
    this.opt_len = 0,
    this.static_len = 0,
    this.matches = 0,
    this.insert = 0,
    this.bi_buf = 0,
    this.bi_valid = 0
}
const deflateResetKeep = e=>{
    if (!e || !e.state)
        return err(e, Z_STREAM_ERROR$2);
    e.total_in = e.total_out = 0,
    e.data_type = Z_UNKNOWN;
    const t = e.state;
    return t.pending = 0,
    t.pending_out = 0,
    t.wrap < 0 && (t.wrap = -t.wrap),
    t.status = t.wrap ? INIT_STATE : BUSY_STATE,
    e.adler = 2 === t.wrap ? 0 : 1,
    t.last_flush = Z_NO_FLUSH$2,
    _tr_init(t),
    Z_OK$3
}
  , deflateReset = e=>{
    const t = deflateResetKeep(e);
    return t === Z_OK$3 && lm_init(e.state),
    t
}
  , deflateSetHeader = (e,t)=>e && e.state ? 2 !== e.state.wrap ? Z_STREAM_ERROR$2 : (e.state.gzhead = t,
Z_OK$3) : Z_STREAM_ERROR$2
  , deflateInit2 = (e,t,i,s,a,o)=>{
    if (!e)
        return Z_STREAM_ERROR$2;
    let n = 1;
    if (t === Z_DEFAULT_COMPRESSION$1 && (t = 6),
    s < 0 ? (n = 0,
    s = -s) : s > 15 && (n = 2,
    s -= 16),
    a < 1 || a > MAX_MEM_LEVEL || i !== Z_DEFLATED$2 || s < 8 || s > 15 || t < 0 || t > 9 || o < 0 || o > Z_FIXED)
        return err(e, Z_STREAM_ERROR$2);
    8 === s && (s = 9);
    const r = new DeflateState;
    return e.state = r,
    r.strm = e,
    r.wrap = n,
    r.gzhead = null,
    r.w_bits = s,
    r.w_size = 1 << r.w_bits,
    r.w_mask = r.w_size - 1,
    r.hash_bits = a + 7,
    r.hash_size = 1 << r.hash_bits,
    r.hash_mask = r.hash_size - 1,
    r.hash_shift = ~~((r.hash_bits + MIN_MATCH - 1) / MIN_MATCH),
    r.window = new Uint8Array(2 * r.w_size),
    r.head = new Uint16Array(r.hash_size),
    r.prev = new Uint16Array(r.w_size),
    r.lit_bufsize = 1 << a + 6,
    r.pending_buf_size = 4 * r.lit_bufsize,
    r.pending_buf = new Uint8Array(r.pending_buf_size),
    r.d_buf = 1 * r.lit_bufsize,
    r.l_buf = 3 * r.lit_bufsize,
    r.level = t,
    r.strategy = o,
    r.method = i,
    deflateReset(e)
}
  , deflateInit = (e,t)=>deflateInit2(e, t, Z_DEFLATED$2, MAX_WBITS$1, DEF_MEM_LEVEL, Z_DEFAULT_STRATEGY$1)
  , deflate$2 = (e,t)=>{
    let i, s;
    if (!e || !e.state || t > Z_BLOCK$1 || t < 0)
        return e ? err(e, Z_STREAM_ERROR$2) : Z_STREAM_ERROR$2;
    const a = e.state;
    if (!e.output || !e.input && 0 !== e.avail_in || a.status === FINISH_STATE && t !== Z_FINISH$3)
        return err(e, 0 === e.avail_out ? Z_BUF_ERROR$1 : Z_STREAM_ERROR$2);
    a.strm = e;
    const o = a.last_flush;
    if (a.last_flush = t,
    a.status === INIT_STATE)
        if (2 === a.wrap)
            e.adler = 0,
            put_byte(a, 31),
            put_byte(a, 139),
            put_byte(a, 8),
            a.gzhead ? (put_byte(a, (a.gzhead.text ? 1 : 0) + (a.gzhead.hcrc ? 2 : 0) + (a.gzhead.extra ? 4 : 0) + (a.gzhead.name ? 8 : 0) + (a.gzhead.comment ? 16 : 0)),
            put_byte(a, 255 & a.gzhead.time),
            put_byte(a, a.gzhead.time >> 8 & 255),
            put_byte(a, a.gzhead.time >> 16 & 255),
            put_byte(a, a.gzhead.time >> 24 & 255),
            put_byte(a, 9 === a.level ? 2 : a.strategy >= Z_HUFFMAN_ONLY || a.level < 2 ? 4 : 0),
            put_byte(a, 255 & a.gzhead.os),
            a.gzhead.extra && a.gzhead.extra.length && (put_byte(a, 255 & a.gzhead.extra.length),
            put_byte(a, a.gzhead.extra.length >> 8 & 255)),
            a.gzhead.hcrc && (e.adler = crc32_1(e.adler, a.pending_buf, a.pending, 0)),
            a.gzindex = 0,
            a.status = EXTRA_STATE) : (put_byte(a, 0),
            put_byte(a, 0),
            put_byte(a, 0),
            put_byte(a, 0),
            put_byte(a, 0),
            put_byte(a, 9 === a.level ? 2 : a.strategy >= Z_HUFFMAN_ONLY || a.level < 2 ? 4 : 0),
            put_byte(a, OS_CODE),
            a.status = BUSY_STATE);
        else {
            let t = Z_DEFLATED$2 + (a.w_bits - 8 << 4) << 8
              , i = -1;
            t |= (i = a.strategy >= Z_HUFFMAN_ONLY || a.level < 2 ? 0 : a.level < 6 ? 1 : 6 === a.level ? 2 : 3) << 6,
            0 !== a.strstart && (t |= PRESET_DICT),
            t += 31 - t % 31,
            a.status = BUSY_STATE,
            putShortMSB(a, t),
            0 !== a.strstart && (putShortMSB(a, e.adler >>> 16),
            putShortMSB(a, 65535 & e.adler)),
            e.adler = 1
        }
    if (a.status === EXTRA_STATE)
        if (a.gzhead.extra) {
            for (i = a.pending; a.gzindex < (65535 & a.gzhead.extra.length) && (a.pending !== a.pending_buf_size || (a.gzhead.hcrc && a.pending > i && (e.adler = crc32_1(e.adler, a.pending_buf, a.pending - i, i)),
            flush_pending(e),
            i = a.pending,
            a.pending !== a.pending_buf_size)); )
                put_byte(a, 255 & a.gzhead.extra[a.gzindex]),
                a.gzindex++;
            a.gzhead.hcrc && a.pending > i && (e.adler = crc32_1(e.adler, a.pending_buf, a.pending - i, i)),
            a.gzindex === a.gzhead.extra.length && (a.gzindex = 0,
            a.status = NAME_STATE)
        } else
            a.status = NAME_STATE;
    if (a.status === NAME_STATE)
        if (a.gzhead.name) {
            i = a.pending;
            do {
                if (a.pending === a.pending_buf_size && (a.gzhead.hcrc && a.pending > i && (e.adler = crc32_1(e.adler, a.pending_buf, a.pending - i, i)),
                flush_pending(e),
                i = a.pending,
                a.pending === a.pending_buf_size)) {
                    s = 1;
                    break
                }
                s = a.gzindex < a.gzhead.name.length ? 255 & a.gzhead.name.charCodeAt(a.gzindex++) : 0,
                put_byte(a, s)
            } while (0 !== s);
            a.gzhead.hcrc && a.pending > i && (e.adler = crc32_1(e.adler, a.pending_buf, a.pending - i, i)),
            0 === s && (a.gzindex = 0,
            a.status = COMMENT_STATE)
        } else
            a.status = COMMENT_STATE;
    if (a.status === COMMENT_STATE)
        if (a.gzhead.comment) {
            i = a.pending;
            do {
                if (a.pending === a.pending_buf_size && (a.gzhead.hcrc && a.pending > i && (e.adler = crc32_1(e.adler, a.pending_buf, a.pending - i, i)),
                flush_pending(e),
                i = a.pending,
                a.pending === a.pending_buf_size)) {
                    s = 1;
                    break
                }
                s = a.gzindex < a.gzhead.comment.length ? 255 & a.gzhead.comment.charCodeAt(a.gzindex++) : 0,
                put_byte(a, s)
            } while (0 !== s);
            a.gzhead.hcrc && a.pending > i && (e.adler = crc32_1(e.adler, a.pending_buf, a.pending - i, i)),
            0 === s && (a.status = HCRC_STATE)
        } else
            a.status = HCRC_STATE;
    if (a.status === HCRC_STATE && (a.gzhead.hcrc ? (a.pending + 2 > a.pending_buf_size && flush_pending(e),
    a.pending + 2 <= a.pending_buf_size && (put_byte(a, 255 & e.adler),
    put_byte(a, e.adler >> 8 & 255),
    e.adler = 0,
    a.status = BUSY_STATE)) : a.status = BUSY_STATE),
    0 !== a.pending) {
        if (flush_pending(e),
        0 === e.avail_out)
            return a.last_flush = -1,
            Z_OK$3
    } else if (0 === e.avail_in && rank(t) <= rank(o) && t !== Z_FINISH$3)
        return err(e, Z_BUF_ERROR$1);
    if (a.status === FINISH_STATE && 0 !== e.avail_in)
        return err(e, Z_BUF_ERROR$1);
    if (0 !== e.avail_in || 0 !== a.lookahead || t !== Z_NO_FLUSH$2 && a.status !== FINISH_STATE) {
        let i = a.strategy === Z_HUFFMAN_ONLY ? deflate_huff(a, t) : a.strategy === Z_RLE ? deflate_rle(a, t) : configuration_table[a.level].func(a, t);
        if (i !== BS_FINISH_STARTED && i !== BS_FINISH_DONE || (a.status = FINISH_STATE),
        i === BS_NEED_MORE || i === BS_FINISH_STARTED)
            return 0 === e.avail_out && (a.last_flush = -1),
            Z_OK$3;
        if (i === BS_BLOCK_DONE && (t === Z_PARTIAL_FLUSH ? _tr_align(a) : t !== Z_BLOCK$1 && (_tr_stored_block(a, 0, 0, !1),
        t === Z_FULL_FLUSH$1 && (zero(a.head),
        0 === a.lookahead && (a.strstart = 0,
        a.block_start = 0,
        a.insert = 0))),
        flush_pending(e),
        0 === e.avail_out))
            return a.last_flush = -1,
            Z_OK$3
    }
    return t !== Z_FINISH$3 ? Z_OK$3 : a.wrap <= 0 ? Z_STREAM_END$3 : (2 === a.wrap ? (put_byte(a, 255 & e.adler),
    put_byte(a, e.adler >> 8 & 255),
    put_byte(a, e.adler >> 16 & 255),
    put_byte(a, e.adler >> 24 & 255),
    put_byte(a, 255 & e.total_in),
    put_byte(a, e.total_in >> 8 & 255),
    put_byte(a, e.total_in >> 16 & 255),
    put_byte(a, e.total_in >> 24 & 255)) : (putShortMSB(a, e.adler >>> 16),
    putShortMSB(a, 65535 & e.adler)),
    flush_pending(e),
    a.wrap > 0 && (a.wrap = -a.wrap),
    0 !== a.pending ? Z_OK$3 : Z_STREAM_END$3)
}
  , deflateEnd = e=>{
    if (!e || !e.state)
        return Z_STREAM_ERROR$2;
    const t = e.state.status;
    return t !== INIT_STATE && t !== EXTRA_STATE && t !== NAME_STATE && t !== COMMENT_STATE && t !== HCRC_STATE && t !== BUSY_STATE && t !== FINISH_STATE ? err(e, Z_STREAM_ERROR$2) : (e.state = null,
    t === BUSY_STATE ? err(e, Z_DATA_ERROR$2) : Z_OK$3)
}
  , deflateSetDictionary = (e,t)=>{
    let i = t.length;
    if (!e || !e.state)
        return Z_STREAM_ERROR$2;
    const s = e.state
      , a = s.wrap;
    if (2 === a || 1 === a && s.status !== INIT_STATE || s.lookahead)
        return Z_STREAM_ERROR$2;
    if (1 === a && (e.adler = adler32_1(e.adler, t, i, 0)),
    s.wrap = 0,
    i >= s.w_size) {
        0 === a && (zero(s.head),
        s.strstart = 0,
        s.block_start = 0,
        s.insert = 0);
        let e = new Uint8Array(s.w_size);
        e.set(t.subarray(i - s.w_size, i), 0),
        t = e,
        i = s.w_size
    }
    const o = e.avail_in
      , n = e.next_in
      , r = e.input;
    for (e.avail_in = i,
    e.next_in = 0,
    e.input = t,
    fill_window(s); s.lookahead >= MIN_MATCH; ) {
        let e = s.strstart
          , t = s.lookahead - (MIN_MATCH - 1);
        do {
            s.ins_h = HASH(s, s.ins_h, s.window[e + MIN_MATCH - 1]),
            s.prev[e & s.w_mask] = s.head[s.ins_h],
            s.head[s.ins_h] = e,
            e++
        } while (--t);
        s.strstart = e,
        s.lookahead = MIN_MATCH - 1,
        fill_window(s)
    }
    return s.strstart += s.lookahead,
    s.block_start = s.strstart,
    s.insert = s.lookahead,
    s.lookahead = 0,
    s.match_length = s.prev_length = MIN_MATCH - 1,
    s.match_available = 0,
    e.next_in = n,
    e.input = r,
    e.avail_in = o,
    s.wrap = a,
    Z_OK$3
}
;
var deflateInit_1 = deflateInit
  , deflateInit2_1 = deflateInit2
  , deflateReset_1 = deflateReset
  , deflateResetKeep_1 = deflateResetKeep
  , deflateSetHeader_1 = deflateSetHeader
  , deflate_2$1 = deflate$2
  , deflateEnd_1 = deflateEnd
  , deflateSetDictionary_1 = deflateSetDictionary
  , deflateInfo = "pako deflate (from Nodeca project)"
  , deflate_1$2 = {
    deflateInit: deflateInit_1,
    deflateInit2: deflateInit2_1,
    deflateReset: deflateReset_1,
    deflateResetKeep: deflateResetKeep_1,
    deflateSetHeader: deflateSetHeader_1,
    deflate: deflate_2$1,
    deflateEnd: deflateEnd_1,
    deflateSetDictionary: deflateSetDictionary_1,
    deflateInfo: deflateInfo
};
const _has = (e,t)=>Object.prototype.hasOwnProperty.call(e, t);
var assign = function(e) {
    const t = Array.prototype.slice.call(arguments, 1);
    for (; t.length; ) {
        const i = t.shift();
        if (i) {
            if ("object" != typeof i)
                throw new TypeError(i + "must be non-object");
            for (const t in i)
                _has(i, t) && (e[t] = i[t])
        }
    }
    return e
}
  , flattenChunks = e=>{
    let t = 0;
    for (let i = 0, s = e.length; i < s; i++)
        t += e[i].length;
    const i = new Uint8Array(t);
    for (let t = 0, s = 0, a = e.length; t < a; t++) {
        let a = e[t];
        i.set(a, s),
        s += a.length
    }
    return i
}
  , common = {
    assign: assign,
    flattenChunks: flattenChunks
};
let STR_APPLY_UIA_OK = !0;
try {
    String.fromCharCode.apply(null, new Uint8Array(1))
} catch (e) {
    STR_APPLY_UIA_OK = !1
}
const _utf8len = new Uint8Array(256);
for (let e = 0; e < 256; e++)
    _utf8len[e] = e >= 252 ? 6 : e >= 248 ? 5 : e >= 240 ? 4 : e >= 224 ? 3 : e >= 192 ? 2 : 1;
_utf8len[254] = _utf8len[254] = 1;
var string2buf = e=>{
    if ("function" == typeof TextEncoder && TextEncoder.prototype.encode)
        return (new TextEncoder).encode(e);
    let t, i, s, a, o, n = e.length, r = 0;
    for (a = 0; a < n; a++)
        55296 == (64512 & (i = e.charCodeAt(a))) && a + 1 < n && 56320 == (64512 & (s = e.charCodeAt(a + 1))) && (i = 65536 + (i - 55296 << 10) + (s - 56320),
        a++),
        r += i < 128 ? 1 : i < 2048 ? 2 : i < 65536 ? 3 : 4;
    for (t = new Uint8Array(r),
    o = 0,
    a = 0; o < r; a++)
        55296 == (64512 & (i = e.charCodeAt(a))) && a + 1 < n && 56320 == (64512 & (s = e.charCodeAt(a + 1))) && (i = 65536 + (i - 55296 << 10) + (s - 56320),
        a++),
        i < 128 ? t[o++] = i : i < 2048 ? (t[o++] = 192 | i >>> 6,
        t[o++] = 128 | 63 & i) : i < 65536 ? (t[o++] = 224 | i >>> 12,
        t[o++] = 128 | i >>> 6 & 63,
        t[o++] = 128 | 63 & i) : (t[o++] = 240 | i >>> 18,
        t[o++] = 128 | i >>> 12 & 63,
        t[o++] = 128 | i >>> 6 & 63,
        t[o++] = 128 | 63 & i);
    return t
}
;
const buf2binstring = (e,t)=>{
    if (t < 65534 && e.subarray && STR_APPLY_UIA_OK)
        return String.fromCharCode.apply(null, e.length === t ? e : e.subarray(0, t));
    let i = "";
    for (let s = 0; s < t; s++)
        i += String.fromCharCode(e[s]);
    return i
}
;
var buf2string = (e,t)=>{
    const i = t || e.length;
    if ("function" == typeof TextDecoder && TextDecoder.prototype.decode)
        return (new TextDecoder).decode(e.subarray(0, t));
    let s, a;
    const o = new Array(2 * i);
    for (a = 0,
    s = 0; s < i; ) {
        let t = e[s++];
        if (t < 128) {
            o[a++] = t;
            continue
        }
        let n = _utf8len[t];
        if (n > 4)
            o[a++] = 65533,
            s += n - 1;
        else {
            for (t &= 2 === n ? 31 : 3 === n ? 15 : 7; n > 1 && s < i; )
                t = t << 6 | 63 & e[s++],
                n--;
            n > 1 ? o[a++] = 65533 : t < 65536 ? o[a++] = t : (t -= 65536,
            o[a++] = 55296 | t >> 10 & 1023,
            o[a++] = 56320 | 1023 & t)
        }
    }
    return buf2binstring(o, a)
}
  , utf8border = (e,t)=>{
    (t = t || e.length) > e.length && (t = e.length);
    let i = t - 1;
    for (; i >= 0 && 128 == (192 & e[i]); )
        i--;
    return i < 0 ? t : 0 === i ? t : i + _utf8len[e[i]] > t ? i : t
}
  , strings = {
    string2buf: string2buf,
    buf2string: buf2string,
    utf8border: utf8border
};
function ZStream() {
    this.input = null,
    this.next_in = 0,
    this.avail_in = 0,
    this.total_in = 0,
    this.output = null,
    this.next_out = 0,
    this.avail_out = 0,
    this.total_out = 0,
    this.msg = "",
    this.state = null,
    this.data_type = 2,
    this.adler = 0
}
var zstream = ZStream;
const toString$1 = Object.prototype.toString
  , {Z_NO_FLUSH: Z_NO_FLUSH$1, Z_SYNC_FLUSH: Z_SYNC_FLUSH, Z_FULL_FLUSH: Z_FULL_FLUSH, Z_FINISH: Z_FINISH$2, Z_OK: Z_OK$2, Z_STREAM_END: Z_STREAM_END$2, Z_DEFAULT_COMPRESSION: Z_DEFAULT_COMPRESSION, Z_DEFAULT_STRATEGY: Z_DEFAULT_STRATEGY, Z_DEFLATED: Z_DEFLATED$1} = constants$2;
function Deflate$1(e) {
    this.options = common.assign({
        level: Z_DEFAULT_COMPRESSION,
        method: Z_DEFLATED$1,
        chunkSize: 16384,
        windowBits: 15,
        memLevel: 8,
        strategy: Z_DEFAULT_STRATEGY
    }, e || {});
    let t = this.options;
    t.raw && t.windowBits > 0 ? t.windowBits = -t.windowBits : t.gzip && t.windowBits > 0 && t.windowBits < 16 && (t.windowBits += 16),
    this.err = 0,
    this.msg = "",
    this.ended = !1,
    this.chunks = [],
    this.strm = new zstream,
    this.strm.avail_out = 0;
    let i = deflate_1$2.deflateInit2(this.strm, t.level, t.method, t.windowBits, t.memLevel, t.strategy);
    if (i !== Z_OK$2)
        throw new Error(messages[i]);
    if (t.header && deflate_1$2.deflateSetHeader(this.strm, t.header),
    t.dictionary) {
        let e;
        if (e = "string" == typeof t.dictionary ? strings.string2buf(t.dictionary) : "[object ArrayBuffer]" === toString$1.call(t.dictionary) ? new Uint8Array(t.dictionary) : t.dictionary,
        (i = deflate_1$2.deflateSetDictionary(this.strm, e)) !== Z_OK$2)
            throw new Error(messages[i]);
        this._dict_set = !0
    }
}
function deflate$1(e, t) {
    const i = new Deflate$1(t);
    if (i.push(e, !0),
    i.err)
        throw i.msg || messages[i.err];
    return i.result
}
function deflateRaw$1(e, t) {
    return (t = t || {}).raw = !0,
    deflate$1(e, t)
}
function gzip$1(e, t) {
    return (t = t || {}).gzip = !0,
    deflate$1(e, t)
}
Deflate$1.prototype.push = function(e, t) {
    const i = this.strm
      , s = this.options.chunkSize;
    let a, o;
    if (this.ended)
        return !1;
    for (o = t === ~~t ? t : !0 === t ? Z_FINISH$2 : Z_NO_FLUSH$1,
    "string" == typeof e ? i.input = strings.string2buf(e) : "[object ArrayBuffer]" === toString$1.call(e) ? i.input = new Uint8Array(e) : i.input = e,
    i.next_in = 0,
    i.avail_in = i.input.length; ; )
        if (0 === i.avail_out && (i.output = new Uint8Array(s),
        i.next_out = 0,
        i.avail_out = s),
        (o === Z_SYNC_FLUSH || o === Z_FULL_FLUSH) && i.avail_out <= 6)
            this.onData(i.output.subarray(0, i.next_out)),
            i.avail_out = 0;
        else {
            if ((a = deflate_1$2.deflate(i, o)) === Z_STREAM_END$2)
                return i.next_out > 0 && this.onData(i.output.subarray(0, i.next_out)),
                a = deflate_1$2.deflateEnd(this.strm),
                this.onEnd(a),
                this.ended = !0,
                a === Z_OK$2;
            if (0 !== i.avail_out) {
                if (o > 0 && i.next_out > 0)
                    this.onData(i.output.subarray(0, i.next_out)),
                    i.avail_out = 0;
                else if (0 === i.avail_in)
                    break
            } else
                this.onData(i.output)
        }
    return !0
}
,
Deflate$1.prototype.onData = function(e) {
    this.chunks.push(e)
}
,
Deflate$1.prototype.onEnd = function(e) {
    e === Z_OK$2 && (this.result = common.flattenChunks(this.chunks)),
    this.chunks = [],
    this.err = e,
    this.msg = this.strm.msg
}
;
var Deflate_1$1 = Deflate$1
  , deflate_2 = deflate$1
  , deflateRaw_1$1 = deflateRaw$1
  , gzip_1$1 = gzip$1
  , constants$1 = constants$2
  , deflate_1$1 = {
    Deflate: Deflate_1$1,
    deflate: deflate_2,
    deflateRaw: deflateRaw_1$1,
    gzip: gzip_1$1,
    constants: constants$1
};
const BAD$1 = 30
  , TYPE$1 = 12;
var inffast = function(e, t) {
    let i, s, a, o, n, r, l, c, d, h, _, u, g, m, p, S, f, E, T, C, A, I, L, y;
    const b = e.state;
    i = e.next_in,
    L = e.input,
    s = i + (e.avail_in - 5),
    a = e.next_out,
    y = e.output,
    o = a - (t - e.avail_out),
    n = a + (e.avail_out - 257),
    r = b.dmax,
    l = b.wsize,
    c = b.whave,
    d = b.wnext,
    h = b.window,
    _ = b.hold,
    u = b.bits,
    g = b.lencode,
    m = b.distcode,
    p = (1 << b.lenbits) - 1,
    S = (1 << b.distbits) - 1;
    e: do {
        u < 15 && (_ += L[i++] << u,
        u += 8,
        _ += L[i++] << u,
        u += 8),
        f = g[_ & p];
        t: for (; ; ) {
            if (_ >>>= E = f >>> 24,
            u -= E,
            0 === (E = f >>> 16 & 255))
                y[a++] = 65535 & f;
            else {
                if (!(16 & E)) {
                    if (0 == (64 & E)) {
                        f = g[(65535 & f) + (_ & (1 << E) - 1)];
                        continue t
                    }
                    if (32 & E) {
                        b.mode = TYPE$1;
                        break e
                    }
                    e.msg = "invalid literal/length code",
                    b.mode = BAD$1;
                    break e
                }
                T = 65535 & f,
                (E &= 15) && (u < E && (_ += L[i++] << u,
                u += 8),
                T += _ & (1 << E) - 1,
                _ >>>= E,
                u -= E),
                u < 15 && (_ += L[i++] << u,
                u += 8,
                _ += L[i++] << u,
                u += 8),
                f = m[_ & S];
                i: for (; ; ) {
                    if (_ >>>= E = f >>> 24,
                    u -= E,
                    !(16 & (E = f >>> 16 & 255))) {
                        if (0 == (64 & E)) {
                            f = m[(65535 & f) + (_ & (1 << E) - 1)];
                            continue i
                        }
                        e.msg = "invalid distance code",
                        b.mode = BAD$1;
                        break e
                    }
                    if (C = 65535 & f,
                    u < (E &= 15) && (_ += L[i++] << u,
                    (u += 8) < E && (_ += L[i++] << u,
                    u += 8)),
                    (C += _ & (1 << E) - 1) > r) {
                        e.msg = "invalid distance too far back",
                        b.mode = BAD$1;
                        break e
                    }
                    if (_ >>>= E,
                    u -= E,
                    C > (E = a - o)) {
                        if ((E = C - E) > c && b.sane) {
                            e.msg = "invalid distance too far back",
                            b.mode = BAD$1;
                            break e
                        }
                        if (A = 0,
                        I = h,
                        0 === d) {
                            if (A += l - E,
                            E < T) {
                                T -= E;
                                do {
                                    y[a++] = h[A++]
                                } while (--E);
                                A = a - C,
                                I = y
                            }
                        } else if (d < E) {
                            if (A += l + d - E,
                            (E -= d) < T) {
                                T -= E;
                                do {
                                    y[a++] = h[A++]
                                } while (--E);
                                if (A = 0,
                                d < T) {
                                    T -= E = d;
                                    do {
                                        y[a++] = h[A++]
                                    } while (--E);
                                    A = a - C,
                                    I = y
                                }
                            }
                        } else if (A += d - E,
                        E < T) {
                            T -= E;
                            do {
                                y[a++] = h[A++]
                            } while (--E);
                            A = a - C,
                            I = y
                        }
                        for (; T > 2; )
                            y[a++] = I[A++],
                            y[a++] = I[A++],
                            y[a++] = I[A++],
                            T -= 3;
                        T && (y[a++] = I[A++],
                        T > 1 && (y[a++] = I[A++]))
                    } else {
                        A = a - C;
                        do {
                            y[a++] = y[A++],
                            y[a++] = y[A++],
                            y[a++] = y[A++],
                            T -= 3
                        } while (T > 2);
                        T && (y[a++] = y[A++],
                        T > 1 && (y[a++] = y[A++]))
                    }
                    break
                }
            }
            break
        }
    } while (i < s && a < n);
    i -= T = u >> 3,
    _ &= (1 << (u -= T << 3)) - 1,
    e.next_in = i,
    e.next_out = a,
    e.avail_in = i < s ? s - i + 5 : 5 - (i - s),
    e.avail_out = a < n ? n - a + 257 : 257 - (a - n),
    b.hold = _,
    b.bits = u
};
const MAXBITS = 15
  , ENOUGH_LENS$1 = 852
  , ENOUGH_DISTS$1 = 592
  , CODES$1 = 0
  , LENS$1 = 1
  , DISTS$1 = 2
  , lbase = new Uint16Array([3, 4, 5, 6, 7, 8, 9, 10, 11, 13, 15, 17, 19, 23, 27, 31, 35, 43, 51, 59, 67, 83, 99, 115, 131, 163, 195, 227, 258, 0, 0])
  , lext = new Uint8Array([16, 16, 16, 16, 16, 16, 16, 16, 17, 17, 17, 17, 18, 18, 18, 18, 19, 19, 19, 19, 20, 20, 20, 20, 21, 21, 21, 21, 16, 72, 78])
  , dbase = new Uint16Array([1, 2, 3, 4, 5, 7, 9, 13, 17, 25, 33, 49, 65, 97, 129, 193, 257, 385, 513, 769, 1025, 1537, 2049, 3073, 4097, 6145, 8193, 12289, 16385, 24577, 0, 0])
  , dext = new Uint8Array([16, 16, 16, 16, 17, 17, 18, 18, 19, 19, 20, 20, 21, 21, 22, 22, 23, 23, 24, 24, 25, 25, 26, 26, 27, 27, 28, 28, 29, 29, 64, 64])
  , inflate_table = (e,t,i,s,a,o,n,r)=>{
    const l = r.bits;
    let c, d, h, _, u, g, m = 0, p = 0, S = 0, f = 0, E = 0, T = 0, C = 0, A = 0, I = 0, L = 0, y = null, b = 0;
    const D = new Uint16Array(MAXBITS + 1)
      , R = new Uint16Array(MAXBITS + 1);
    let w, M, v, N = null, P = 0;
    for (m = 0; m <= MAXBITS; m++)
        D[m] = 0;
    for (p = 0; p < s; p++)
        D[t[i + p]]++;
    for (E = l,
    f = MAXBITS; f >= 1 && 0 === D[f]; f--)
        ;
    if (E > f && (E = f),
    0 === f)
        return a[o++] = 20971520,
        a[o++] = 20971520,
        r.bits = 1,
        0;
    for (S = 1; S < f && 0 === D[S]; S++)
        ;
    for (E < S && (E = S),
    A = 1,
    m = 1; m <= MAXBITS; m++)
        if (A <<= 1,
        (A -= D[m]) < 0)
            return -1;
    if (A > 0 && (e === CODES$1 || 1 !== f))
        return -1;
    for (R[1] = 0,
    m = 1; m < MAXBITS; m++)
        R[m + 1] = R[m] + D[m];
    for (p = 0; p < s; p++)
        0 !== t[i + p] && (n[R[t[i + p]]++] = p);
    if (e === CODES$1 ? (y = N = n,
    g = 19) : e === LENS$1 ? (y = lbase,
    b -= 257,
    N = lext,
    P -= 257,
    g = 256) : (y = dbase,
    N = dext,
    g = -1),
    L = 0,
    p = 0,
    m = S,
    u = o,
    T = E,
    C = 0,
    h = -1,
    _ = (I = 1 << E) - 1,
    e === LENS$1 && I > ENOUGH_LENS$1 || e === DISTS$1 && I > ENOUGH_DISTS$1)
        return 1;
    for (; ; ) {
        w = m - C,
        n[p] < g ? (M = 0,
        v = n[p]) : n[p] > g ? (M = N[P + n[p]],
        v = y[b + n[p]]) : (M = 96,
        v = 0),
        c = 1 << m - C,
        S = d = 1 << T;
        do {
            a[u + (L >> C) + (d -= c)] = w << 24 | M << 16 | v | 0
        } while (0 !== d);
        for (c = 1 << m - 1; L & c; )
            c >>= 1;
        if (0 !== c ? (L &= c - 1,
        L += c) : L = 0,
        p++,
        0 == --D[m]) {
            if (m === f)
                break;
            m = t[i + n[p]]
        }
        if (m > E && (L & _) !== h) {
            for (0 === C && (C = E),
            u += S,
            A = 1 << (T = m - C); T + C < f && !((A -= D[T + C]) <= 0); )
                T++,
                A <<= 1;
            if (I += 1 << T,
            e === LENS$1 && I > ENOUGH_LENS$1 || e === DISTS$1 && I > ENOUGH_DISTS$1)
                return 1;
            a[h = L & _] = E << 24 | T << 16 | u - o | 0
        }
    }
    return 0 !== L && (a[u + L] = m - C << 24 | 64 << 16 | 0),
    r.bits = E,
    0
}
;
var inftrees = inflate_table;
const CODES = 0
  , LENS = 1
  , DISTS = 2
  , {Z_FINISH: Z_FINISH$1, Z_BLOCK: Z_BLOCK, Z_TREES: Z_TREES, Z_OK: Z_OK$1, Z_STREAM_END: Z_STREAM_END$1, Z_NEED_DICT: Z_NEED_DICT$1, Z_STREAM_ERROR: Z_STREAM_ERROR$1, Z_DATA_ERROR: Z_DATA_ERROR$1, Z_MEM_ERROR: Z_MEM_ERROR$1, Z_BUF_ERROR: Z_BUF_ERROR, Z_DEFLATED: Z_DEFLATED} = constants$2
  , HEAD = 1
  , FLAGS = 2
  , TIME = 3
  , OS = 4
  , EXLEN = 5
  , EXTRA = 6
  , NAME = 7
  , COMMENT = 8
  , HCRC = 9
  , DICTID = 10
  , DICT = 11
  , TYPE = 12
  , TYPEDO = 13
  , STORED = 14
  , COPY_ = 15
  , COPY = 16
  , TABLE = 17
  , LENLENS = 18
  , CODELENS = 19
  , LEN_ = 20
  , LEN = 21
  , LENEXT = 22
  , DIST = 23
  , DISTEXT = 24
  , MATCH = 25
  , LIT = 26
  , CHECK = 27
  , LENGTH = 28
  , DONE = 29
  , BAD = 30
  , MEM = 31
  , SYNC = 32
  , ENOUGH_LENS = 852
  , ENOUGH_DISTS = 592
  , MAX_WBITS = 15
  , DEF_WBITS = MAX_WBITS
  , zswap32 = e=>(e >>> 24 & 255) + (e >>> 8 & 65280) + ((65280 & e) << 8) + ((255 & e) << 24);
function InflateState() {
    this.mode = 0,
    this.last = !1,
    this.wrap = 0,
    this.havedict = !1,
    this.flags = 0,
    this.dmax = 0,
    this.check = 0,
    this.total = 0,
    this.head = null,
    this.wbits = 0,
    this.wsize = 0,
    this.whave = 0,
    this.wnext = 0,
    this.window = null,
    this.hold = 0,
    this.bits = 0,
    this.length = 0,
    this.offset = 0,
    this.extra = 0,
    this.lencode = null,
    this.distcode = null,
    this.lenbits = 0,
    this.distbits = 0,
    this.ncode = 0,
    this.nlen = 0,
    this.ndist = 0,
    this.have = 0,
    this.next = null,
    this.lens = new Uint16Array(320),
    this.work = new Uint16Array(288),
    this.lendyn = null,
    this.distdyn = null,
    this.sane = 0,
    this.back = 0,
    this.was = 0
}
const inflateResetKeep = e=>{
    if (!e || !e.state)
        return Z_STREAM_ERROR$1;
    const t = e.state;
    return e.total_in = e.total_out = t.total = 0,
    e.msg = "",
    t.wrap && (e.adler = 1 & t.wrap),
    t.mode = HEAD,
    t.last = 0,
    t.havedict = 0,
    t.dmax = 32768,
    t.head = null,
    t.hold = 0,
    t.bits = 0,
    t.lencode = t.lendyn = new Int32Array(ENOUGH_LENS),
    t.distcode = t.distdyn = new Int32Array(ENOUGH_DISTS),
    t.sane = 1,
    t.back = -1,
    Z_OK$1
}
  , inflateReset = e=>{
    if (!e || !e.state)
        return Z_STREAM_ERROR$1;
    const t = e.state;
    return t.wsize = 0,
    t.whave = 0,
    t.wnext = 0,
    inflateResetKeep(e)
}
  , inflateReset2 = (e,t)=>{
    let i;
    if (!e || !e.state)
        return Z_STREAM_ERROR$1;
    const s = e.state;
    return t < 0 ? (i = 0,
    t = -t) : (i = 1 + (t >> 4),
    t < 48 && (t &= 15)),
    t && (t < 8 || t > 15) ? Z_STREAM_ERROR$1 : (null !== s.window && s.wbits !== t && (s.window = null),
    s.wrap = i,
    s.wbits = t,
    inflateReset(e))
}
  , inflateInit2 = (e,t)=>{
    if (!e)
        return Z_STREAM_ERROR$1;
    const i = new InflateState;
    e.state = i,
    i.window = null;
    const s = inflateReset2(e, t);
    return s !== Z_OK$1 && (e.state = null),
    s
}
  , inflateInit = e=>inflateInit2(e, DEF_WBITS);
let virgin = !0, lenfix, distfix;
const fixedtables = e=>{
    if (virgin) {
        lenfix = new Int32Array(512),
        distfix = new Int32Array(32);
        let t = 0;
        for (; t < 144; )
            e.lens[t++] = 8;
        for (; t < 256; )
            e.lens[t++] = 9;
        for (; t < 280; )
            e.lens[t++] = 7;
        for (; t < 288; )
            e.lens[t++] = 8;
        for (inftrees(LENS, e.lens, 0, 288, lenfix, 0, e.work, {
            bits: 9
        }),
        t = 0; t < 32; )
            e.lens[t++] = 5;
        inftrees(DISTS, e.lens, 0, 32, distfix, 0, e.work, {
            bits: 5
        }),
        virgin = !1
    }
    e.lencode = lenfix,
    e.lenbits = 9,
    e.distcode = distfix,
    e.distbits = 5
}
  , updatewindow = (e,t,i,s)=>{
    let a;
    const o = e.state;
    return null === o.window && (o.wsize = 1 << o.wbits,
    o.wnext = 0,
    o.whave = 0,
    o.window = new Uint8Array(o.wsize)),
    s >= o.wsize ? (o.window.set(t.subarray(i - o.wsize, i), 0),
    o.wnext = 0,
    o.whave = o.wsize) : ((a = o.wsize - o.wnext) > s && (a = s),
    o.window.set(t.subarray(i - s, i - s + a), o.wnext),
    (s -= a) ? (o.window.set(t.subarray(i - s, i), 0),
    o.wnext = s,
    o.whave = o.wsize) : (o.wnext += a,
    o.wnext === o.wsize && (o.wnext = 0),
    o.whave < o.wsize && (o.whave += a))),
    0
}
  , inflate$2 = (e,t)=>{
    let i, s, a, o, n, r, l, c, d, h, _, u, g, m, p, S, f, E, T, C, A, I, L = 0;
    const y = new Uint8Array(4);
    let b, D;
    const R = new Uint8Array([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);
    if (!e || !e.state || !e.output || !e.input && 0 !== e.avail_in)
        return Z_STREAM_ERROR$1;
    (i = e.state).mode === TYPE && (i.mode = TYPEDO),
    n = e.next_out,
    a = e.output,
    l = e.avail_out,
    o = e.next_in,
    s = e.input,
    r = e.avail_in,
    c = i.hold,
    d = i.bits,
    h = r,
    _ = l,
    I = Z_OK$1;
    e: for (; ; )
        switch (i.mode) {
        case HEAD:
            if (0 === i.wrap) {
                i.mode = TYPEDO;
                break
            }
            for (; d < 16; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            if (2 & i.wrap && 35615 === c) {
                i.check = 0,
                y[0] = 255 & c,
                y[1] = c >>> 8 & 255,
                i.check = crc32_1(i.check, y, 2, 0),
                c = 0,
                d = 0,
                i.mode = FLAGS;
                break
            }
            if (i.flags = 0,
            i.head && (i.head.done = !1),
            !(1 & i.wrap) || (((255 & c) << 8) + (c >> 8)) % 31) {
                e.msg = "incorrect header check",
                i.mode = BAD;
                break
            }
            if ((15 & c) !== Z_DEFLATED) {
                e.msg = "unknown compression method",
                i.mode = BAD;
                break
            }
            if (d -= 4,
            A = 8 + (15 & (c >>>= 4)),
            0 === i.wbits)
                i.wbits = A;
            else if (A > i.wbits) {
                e.msg = "invalid window size",
                i.mode = BAD;
                break
            }
            i.dmax = 1 << i.wbits,
            e.adler = i.check = 1,
            i.mode = 512 & c ? DICTID : TYPE,
            c = 0,
            d = 0;
            break;
        case FLAGS:
            for (; d < 16; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            if (i.flags = c,
            (255 & i.flags) !== Z_DEFLATED) {
                e.msg = "unknown compression method",
                i.mode = BAD;
                break
            }
            if (57344 & i.flags) {
                e.msg = "unknown header flags set",
                i.mode = BAD;
                break
            }
            i.head && (i.head.text = c >> 8 & 1),
            512 & i.flags && (y[0] = 255 & c,
            y[1] = c >>> 8 & 255,
            i.check = crc32_1(i.check, y, 2, 0)),
            c = 0,
            d = 0,
            i.mode = TIME;
        case TIME:
            for (; d < 32; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            i.head && (i.head.time = c),
            512 & i.flags && (y[0] = 255 & c,
            y[1] = c >>> 8 & 255,
            y[2] = c >>> 16 & 255,
            y[3] = c >>> 24 & 255,
            i.check = crc32_1(i.check, y, 4, 0)),
            c = 0,
            d = 0,
            i.mode = OS;
        case OS:
            for (; d < 16; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            i.head && (i.head.xflags = 255 & c,
            i.head.os = c >> 8),
            512 & i.flags && (y[0] = 255 & c,
            y[1] = c >>> 8 & 255,
            i.check = crc32_1(i.check, y, 2, 0)),
            c = 0,
            d = 0,
            i.mode = EXLEN;
        case EXLEN:
            if (1024 & i.flags) {
                for (; d < 16; ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                i.length = c,
                i.head && (i.head.extra_len = c),
                512 & i.flags && (y[0] = 255 & c,
                y[1] = c >>> 8 & 255,
                i.check = crc32_1(i.check, y, 2, 0)),
                c = 0,
                d = 0
            } else
                i.head && (i.head.extra = null);
            i.mode = EXTRA;
        case EXTRA:
            if (1024 & i.flags && ((u = i.length) > r && (u = r),
            u && (i.head && (A = i.head.extra_len - i.length,
            i.head.extra || (i.head.extra = new Uint8Array(i.head.extra_len)),
            i.head.extra.set(s.subarray(o, o + u), A)),
            512 & i.flags && (i.check = crc32_1(i.check, s, u, o)),
            r -= u,
            o += u,
            i.length -= u),
            i.length))
                break e;
            i.length = 0,
            i.mode = NAME;
        case NAME:
            if (2048 & i.flags) {
                if (0 === r)
                    break e;
                u = 0;
                do {
                    A = s[o + u++],
                    i.head && A && i.length < 65536 && (i.head.name += String.fromCharCode(A))
                } while (A && u < r);
                if (512 & i.flags && (i.check = crc32_1(i.check, s, u, o)),
                r -= u,
                o += u,
                A)
                    break e
            } else
                i.head && (i.head.name = null);
            i.length = 0,
            i.mode = COMMENT;
        case COMMENT:
            if (4096 & i.flags) {
                if (0 === r)
                    break e;
                u = 0;
                do {
                    A = s[o + u++],
                    i.head && A && i.length < 65536 && (i.head.comment += String.fromCharCode(A))
                } while (A && u < r);
                if (512 & i.flags && (i.check = crc32_1(i.check, s, u, o)),
                r -= u,
                o += u,
                A)
                    break e
            } else
                i.head && (i.head.comment = null);
            i.mode = HCRC;
        case HCRC:
            if (512 & i.flags) {
                for (; d < 16; ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                if (c !== (65535 & i.check)) {
                    e.msg = "header crc mismatch",
                    i.mode = BAD;
                    break
                }
                c = 0,
                d = 0
            }
            i.head && (i.head.hcrc = i.flags >> 9 & 1,
            i.head.done = !0),
            e.adler = i.check = 0,
            i.mode = TYPE;
            break;
        case DICTID:
            for (; d < 32; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            e.adler = i.check = zswap32(c),
            c = 0,
            d = 0,
            i.mode = DICT;
        case DICT:
            if (0 === i.havedict)
                return e.next_out = n,
                e.avail_out = l,
                e.next_in = o,
                e.avail_in = r,
                i.hold = c,
                i.bits = d,
                Z_NEED_DICT$1;
            e.adler = i.check = 1,
            i.mode = TYPE;
        case TYPE:
            if (t === Z_BLOCK || t === Z_TREES)
                break e;
        case TYPEDO:
            if (i.last) {
                c >>>= 7 & d,
                d -= 7 & d,
                i.mode = CHECK;
                break
            }
            for (; d < 3; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            switch (i.last = 1 & c,
            d -= 1,
            3 & (c >>>= 1)) {
            case 0:
                i.mode = STORED;
                break;
            case 1:
                if (fixedtables(i),
                i.mode = LEN_,
                t === Z_TREES) {
                    c >>>= 2,
                    d -= 2;
                    break e
                }
                break;
            case 2:
                i.mode = TABLE;
                break;
            case 3:
                e.msg = "invalid block type",
                i.mode = BAD
            }
            c >>>= 2,
            d -= 2;
            break;
        case STORED:
            for (c >>>= 7 & d,
            d -= 7 & d; d < 32; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            if ((65535 & c) != (c >>> 16 ^ 65535)) {
                e.msg = "invalid stored block lengths",
                i.mode = BAD;
                break
            }
            if (i.length = 65535 & c,
            c = 0,
            d = 0,
            i.mode = COPY_,
            t === Z_TREES)
                break e;
        case COPY_:
            i.mode = COPY;
        case COPY:
            if (u = i.length) {
                if (u > r && (u = r),
                u > l && (u = l),
                0 === u)
                    break e;
                a.set(s.subarray(o, o + u), n),
                r -= u,
                o += u,
                l -= u,
                n += u,
                i.length -= u;
                break
            }
            i.mode = TYPE;
            break;
        case TABLE:
            for (; d < 14; ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            if (i.nlen = 257 + (31 & c),
            c >>>= 5,
            d -= 5,
            i.ndist = 1 + (31 & c),
            c >>>= 5,
            d -= 5,
            i.ncode = 4 + (15 & c),
            c >>>= 4,
            d -= 4,
            i.nlen > 286 || i.ndist > 30) {
                e.msg = "too many length or distance symbols",
                i.mode = BAD;
                break
            }
            i.have = 0,
            i.mode = LENLENS;
        case LENLENS:
            for (; i.have < i.ncode; ) {
                for (; d < 3; ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                i.lens[R[i.have++]] = 7 & c,
                c >>>= 3,
                d -= 3
            }
            for (; i.have < 19; )
                i.lens[R[i.have++]] = 0;
            if (i.lencode = i.lendyn,
            i.lenbits = 7,
            b = {
                bits: i.lenbits
            },
            I = inftrees(CODES, i.lens, 0, 19, i.lencode, 0, i.work, b),
            i.lenbits = b.bits,
            I) {
                e.msg = "invalid code lengths set",
                i.mode = BAD;
                break
            }
            i.have = 0,
            i.mode = CODELENS;
        case CODELENS:
            for (; i.have < i.nlen + i.ndist; ) {
                for (; S = (L = i.lencode[c & (1 << i.lenbits) - 1]) >>> 16 & 255,
                f = 65535 & L,
                !((p = L >>> 24) <= d); ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                if (f < 16)
                    c >>>= p,
                    d -= p,
                    i.lens[i.have++] = f;
                else {
                    if (16 === f) {
                        for (D = p + 2; d < D; ) {
                            if (0 === r)
                                break e;
                            r--,
                            c += s[o++] << d,
                            d += 8
                        }
                        if (c >>>= p,
                        d -= p,
                        0 === i.have) {
                            e.msg = "invalid bit length repeat",
                            i.mode = BAD;
                            break
                        }
                        A = i.lens[i.have - 1],
                        u = 3 + (3 & c),
                        c >>>= 2,
                        d -= 2
                    } else if (17 === f) {
                        for (D = p + 3; d < D; ) {
                            if (0 === r)
                                break e;
                            r--,
                            c += s[o++] << d,
                            d += 8
                        }
                        d -= p,
                        A = 0,
                        u = 3 + (7 & (c >>>= p)),
                        c >>>= 3,
                        d -= 3
                    } else {
                        for (D = p + 7; d < D; ) {
                            if (0 === r)
                                break e;
                            r--,
                            c += s[o++] << d,
                            d += 8
                        }
                        d -= p,
                        A = 0,
                        u = 11 + (127 & (c >>>= p)),
                        c >>>= 7,
                        d -= 7
                    }
                    if (i.have + u > i.nlen + i.ndist) {
                        e.msg = "invalid bit length repeat",
                        i.mode = BAD;
                        break
                    }
                    for (; u--; )
                        i.lens[i.have++] = A
                }
            }
            if (i.mode === BAD)
                break;
            if (0 === i.lens[256]) {
                e.msg = "invalid code -- missing end-of-block",
                i.mode = BAD;
                break
            }
            if (i.lenbits = 9,
            b = {
                bits: i.lenbits
            },
            I = inftrees(LENS, i.lens, 0, i.nlen, i.lencode, 0, i.work, b),
            i.lenbits = b.bits,
            I) {
                e.msg = "invalid literal/lengths set",
                i.mode = BAD;
                break
            }
            if (i.distbits = 6,
            i.distcode = i.distdyn,
            b = {
                bits: i.distbits
            },
            I = inftrees(DISTS, i.lens, i.nlen, i.ndist, i.distcode, 0, i.work, b),
            i.distbits = b.bits,
            I) {
                e.msg = "invalid distances set",
                i.mode = BAD;
                break
            }
            if (i.mode = LEN_,
            t === Z_TREES)
                break e;
        case LEN_:
            i.mode = LEN;
        case LEN:
            if (r >= 6 && l >= 258) {
                e.next_out = n,
                e.avail_out = l,
                e.next_in = o,
                e.avail_in = r,
                i.hold = c,
                i.bits = d,
                inffast(e, _),
                n = e.next_out,
                a = e.output,
                l = e.avail_out,
                o = e.next_in,
                s = e.input,
                r = e.avail_in,
                c = i.hold,
                d = i.bits,
                i.mode === TYPE && (i.back = -1);
                break
            }
            for (i.back = 0; S = (L = i.lencode[c & (1 << i.lenbits) - 1]) >>> 16 & 255,
            f = 65535 & L,
            !((p = L >>> 24) <= d); ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            if (S && 0 == (240 & S)) {
                for (E = p,
                T = S,
                C = f; S = (L = i.lencode[C + ((c & (1 << E + T) - 1) >> E)]) >>> 16 & 255,
                f = 65535 & L,
                !(E + (p = L >>> 24) <= d); ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                c >>>= E,
                d -= E,
                i.back += E
            }
            if (c >>>= p,
            d -= p,
            i.back += p,
            i.length = f,
            0 === S) {
                i.mode = LIT;
                break
            }
            if (32 & S) {
                i.back = -1,
                i.mode = TYPE;
                break
            }
            if (64 & S) {
                e.msg = "invalid literal/length code",
                i.mode = BAD;
                break
            }
            i.extra = 15 & S,
            i.mode = LENEXT;
        case LENEXT:
            if (i.extra) {
                for (D = i.extra; d < D; ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                i.length += c & (1 << i.extra) - 1,
                c >>>= i.extra,
                d -= i.extra,
                i.back += i.extra
            }
            i.was = i.length,
            i.mode = DIST;
        case DIST:
            for (; S = (L = i.distcode[c & (1 << i.distbits) - 1]) >>> 16 & 255,
            f = 65535 & L,
            !((p = L >>> 24) <= d); ) {
                if (0 === r)
                    break e;
                r--,
                c += s[o++] << d,
                d += 8
            }
            if (0 == (240 & S)) {
                for (E = p,
                T = S,
                C = f; S = (L = i.distcode[C + ((c & (1 << E + T) - 1) >> E)]) >>> 16 & 255,
                f = 65535 & L,
                !(E + (p = L >>> 24) <= d); ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                c >>>= E,
                d -= E,
                i.back += E
            }
            if (c >>>= p,
            d -= p,
            i.back += p,
            64 & S) {
                e.msg = "invalid distance code",
                i.mode = BAD;
                break
            }
            i.offset = f,
            i.extra = 15 & S,
            i.mode = DISTEXT;
        case DISTEXT:
            if (i.extra) {
                for (D = i.extra; d < D; ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                i.offset += c & (1 << i.extra) - 1,
                c >>>= i.extra,
                d -= i.extra,
                i.back += i.extra
            }
            if (i.offset > i.dmax) {
                e.msg = "invalid distance too far back",
                i.mode = BAD;
                break
            }
            i.mode = MATCH;
        case MATCH:
            if (0 === l)
                break e;
            if (u = _ - l,
            i.offset > u) {
                if ((u = i.offset - u) > i.whave && i.sane) {
                    e.msg = "invalid distance too far back",
                    i.mode = BAD;
                    break
                }
                u > i.wnext ? (u -= i.wnext,
                g = i.wsize - u) : g = i.wnext - u,
                u > i.length && (u = i.length),
                m = i.window
            } else
                m = a,
                g = n - i.offset,
                u = i.length;
            u > l && (u = l),
            l -= u,
            i.length -= u;
            do {
                a[n++] = m[g++]
            } while (--u);
            0 === i.length && (i.mode = LEN);
            break;
        case LIT:
            if (0 === l)
                break e;
            a[n++] = i.length,
            l--,
            i.mode = LEN;
            break;
        case CHECK:
            if (i.wrap) {
                for (; d < 32; ) {
                    if (0 === r)
                        break e;
                    r--,
                    c |= s[o++] << d,
                    d += 8
                }
                if (_ -= l,
                e.total_out += _,
                i.total += _,
                _ && (e.adler = i.check = i.flags ? crc32_1(i.check, a, _, n - _) : adler32_1(i.check, a, _, n - _)),
                _ = l,
                (i.flags ? c : zswap32(c)) !== i.check) {
                    e.msg = "incorrect data check",
                    i.mode = BAD;
                    break
                }
                c = 0,
                d = 0
            }
            i.mode = LENGTH;
        case LENGTH:
            if (i.wrap && i.flags) {
                for (; d < 32; ) {
                    if (0 === r)
                        break e;
                    r--,
                    c += s[o++] << d,
                    d += 8
                }
                if (c !== (4294967295 & i.total)) {
                    e.msg = "incorrect length check",
                    i.mode = BAD;
                    break
                }
                c = 0,
                d = 0
            }
            i.mode = DONE;
        case DONE:
            I = Z_STREAM_END$1;
            break e;
        case BAD:
            I = Z_DATA_ERROR$1;
            break e;
        case MEM:
            return Z_MEM_ERROR$1;
        case SYNC:
        default:
            return Z_STREAM_ERROR$1
        }
    return e.next_out = n,
    e.avail_out = l,
    e.next_in = o,
    e.avail_in = r,
    i.hold = c,
    i.bits = d,
    (i.wsize || _ !== e.avail_out && i.mode < BAD && (i.mode < CHECK || t !== Z_FINISH$1)) && updatewindow(e, e.output, e.next_out, _ - e.avail_out),
    h -= e.avail_in,
    _ -= e.avail_out,
    e.total_in += h,
    e.total_out += _,
    i.total += _,
    i.wrap && _ && (e.adler = i.check = i.flags ? crc32_1(i.check, a, _, e.next_out - _) : adler32_1(i.check, a, _, e.next_out - _)),
    e.data_type = i.bits + (i.last ? 64 : 0) + (i.mode === TYPE ? 128 : 0) + (i.mode === LEN_ || i.mode === COPY_ ? 256 : 0),
    (0 === h && 0 === _ || t === Z_FINISH$1) && I === Z_OK$1 && (I = Z_BUF_ERROR),
    I
}
  , inflateEnd = e=>{
    if (!e || !e.state)
        return Z_STREAM_ERROR$1;
    let t = e.state;
    return t.window && (t.window = null),
    e.state = null,
    Z_OK$1
}
  , inflateGetHeader = (e,t)=>{
    if (!e || !e.state)
        return Z_STREAM_ERROR$1;
    const i = e.state;
    return 0 == (2 & i.wrap) ? Z_STREAM_ERROR$1 : (i.head = t,
    t.done = !1,
    Z_OK$1)
}
  , inflateSetDictionary = (e,t)=>{
    const i = t.length;
    let s, a, o;
    return e && e.state ? 0 !== (s = e.state).wrap && s.mode !== DICT ? Z_STREAM_ERROR$1 : s.mode === DICT && (a = adler32_1(a = 1, t, i, 0)) !== s.check ? Z_DATA_ERROR$1 : (o = updatewindow(e, t, i, i)) ? (s.mode = MEM,
    Z_MEM_ERROR$1) : (s.havedict = 1,
    Z_OK$1) : Z_STREAM_ERROR$1
}
;
var inflateReset_1 = inflateReset
  , inflateReset2_1 = inflateReset2
  , inflateResetKeep_1 = inflateResetKeep
  , inflateInit_1 = inflateInit
  , inflateInit2_1 = inflateInit2
  , inflate_2$1 = inflate$2
  , inflateEnd_1 = inflateEnd
  , inflateGetHeader_1 = inflateGetHeader
  , inflateSetDictionary_1 = inflateSetDictionary
  , inflateInfo = "pako inflate (from Nodeca project)"
  , inflate_1$2 = {
    inflateReset: inflateReset_1,
    inflateReset2: inflateReset2_1,
    inflateResetKeep: inflateResetKeep_1,
    inflateInit: inflateInit_1,
    inflateInit2: inflateInit2_1,
    inflate: inflate_2$1,
    inflateEnd: inflateEnd_1,
    inflateGetHeader: inflateGetHeader_1,
    inflateSetDictionary: inflateSetDictionary_1,
    inflateInfo: inflateInfo
};
function GZheader() {
    this.text = 0,
    this.time = 0,
    this.xflags = 0,
    this.os = 0,
    this.extra = null,
    this.extra_len = 0,
    this.name = "",
    this.comment = "",
    this.hcrc = 0,
    this.done = !1
}
var gzheader = GZheader;
const toString = Object.prototype.toString
  , {Z_NO_FLUSH: Z_NO_FLUSH, Z_FINISH: Z_FINISH, Z_OK: Z_OK, Z_STREAM_END: Z_STREAM_END, Z_NEED_DICT: Z_NEED_DICT, Z_STREAM_ERROR: Z_STREAM_ERROR, Z_DATA_ERROR: Z_DATA_ERROR, Z_MEM_ERROR: Z_MEM_ERROR} = constants$2;
function Inflate$1(e) {
    this.options = common.assign({
        chunkSize: 65536,
        windowBits: 15,
        to: ""
    }, e || {});
    const t = this.options;
    t.raw && t.windowBits >= 0 && t.windowBits < 16 && (t.windowBits = -t.windowBits,
    0 === t.windowBits && (t.windowBits = -15)),
    !(t.windowBits >= 0 && t.windowBits < 16) || e && e.windowBits || (t.windowBits += 32),
    t.windowBits > 15 && t.windowBits < 48 && 0 == (15 & t.windowBits) && (t.windowBits |= 15),
    this.err = 0,
    this.msg = "",
    this.ended = !1,
    this.chunks = [],
    this.strm = new zstream,
    this.strm.avail_out = 0;
    let i = inflate_1$2.inflateInit2(this.strm, t.windowBits);
    if (i !== Z_OK)
        throw new Error(messages[i]);
    if (this.header = new gzheader,
    inflate_1$2.inflateGetHeader(this.strm, this.header),
    t.dictionary && ("string" == typeof t.dictionary ? t.dictionary = strings.string2buf(t.dictionary) : "[object ArrayBuffer]" === toString.call(t.dictionary) && (t.dictionary = new Uint8Array(t.dictionary)),
    t.raw && (i = inflate_1$2.inflateSetDictionary(this.strm, t.dictionary)) !== Z_OK))
        throw new Error(messages[i])
}
function inflate$1(e, t) {
    const i = new Inflate$1(t);
    if (i.push(e),
    i.err)
        throw i.msg || messages[i.err];
    return i.result
}
function inflateRaw$1(e, t) {
    return (t = t || {}).raw = !0,
    inflate$1(e, t)
}
Inflate$1.prototype.push = function(e, t) {
    const i = this.strm
      , s = this.options.chunkSize
      , a = this.options.dictionary;
    let o, n, r;
    if (this.ended)
        return !1;
    for (n = t === ~~t ? t : !0 === t ? Z_FINISH : Z_NO_FLUSH,
    "[object ArrayBuffer]" === toString.call(e) ? i.input = new Uint8Array(e) : i.input = e,
    i.next_in = 0,
    i.avail_in = i.input.length; ; ) {
        for (0 === i.avail_out && (i.output = new Uint8Array(s),
        i.next_out = 0,
        i.avail_out = s),
        (o = inflate_1$2.inflate(i, n)) === Z_NEED_DICT && a && ((o = inflate_1$2.inflateSetDictionary(i, a)) === Z_OK ? o = inflate_1$2.inflate(i, n) : o === Z_DATA_ERROR && (o = Z_NEED_DICT)); i.avail_in > 0 && o === Z_STREAM_END && i.state.wrap > 0 && 0 !== e[i.next_in]; )
            inflate_1$2.inflateReset(i),
            o = inflate_1$2.inflate(i, n);
        switch (o) {
        case Z_STREAM_ERROR:
        case Z_DATA_ERROR:
        case Z_NEED_DICT:
        case Z_MEM_ERROR:
            return this.onEnd(o),
            this.ended = !0,
            !1
        }
        if (r = i.avail_out,
        i.next_out && (0 === i.avail_out || o === Z_STREAM_END))
            if ("string" === this.options.to) {
                let e = strings.utf8border(i.output, i.next_out)
                  , t = i.next_out - e
                  , a = strings.buf2string(i.output, e);
                i.next_out = t,
                i.avail_out = s - t,
                t && i.output.set(i.output.subarray(e, e + t), 0),
                this.onData(a)
            } else
                this.onData(i.output.length === i.next_out ? i.output : i.output.subarray(0, i.next_out));
        if (o !== Z_OK || 0 !== r) {
            if (o === Z_STREAM_END)
                return o = inflate_1$2.inflateEnd(this.strm),
                this.onEnd(o),
                this.ended = !0,
                !0;
            if (0 === i.avail_in)
                break
        }
    }
    return !0
}
,
Inflate$1.prototype.onData = function(e) {
    this.chunks.push(e)
}
,
Inflate$1.prototype.onEnd = function(e) {
    e === Z_OK && ("string" === this.options.to ? this.result = this.chunks.join("") : this.result = common.flattenChunks(this.chunks)),
    this.chunks = [],
    this.err = e,
    this.msg = this.strm.msg
}
;
var Inflate_1$1 = Inflate$1
  , inflate_2 = inflate$1
  , inflateRaw_1$1 = inflateRaw$1
  , ungzip$1 = inflate$1
  , constants = constants$2
  , inflate_1$1 = {
    Inflate: Inflate_1$1,
    inflate: inflate_2,
    inflateRaw: inflateRaw_1$1,
    ungzip: ungzip$1,
    constants: constants
};
const {Deflate: Deflate, deflate: deflate, deflateRaw: deflateRaw, gzip: gzip} = deflate_1$1
  , {Inflate: Inflate, inflate: inflate, inflateRaw: inflateRaw, ungzip: ungzip} = inflate_1$1;
var Deflate_1 = Deflate
  , deflate_1 = deflate
  , deflateRaw_1 = deflateRaw
  , gzip_1 = gzip
  , Inflate_1 = Inflate
  , inflate_1 = inflate
  , inflateRaw_1 = inflateRaw
  , ungzip_1 = ungzip
  , constants_1 = constants$2
  , pako$2 = {
    Deflate: Deflate_1,
    deflate: deflate_1,
    deflateRaw: deflateRaw_1,
    gzip: gzip_1,
    Inflate: Inflate_1,
    inflate: inflate_1,
    inflateRaw: inflateRaw_1,
    ungzip: ungzip_1,
    constants: constants_1
};
class ULModuleBase {
    constructor() {
        ULLog.console.log("ULModuleBase-constructor")
    }
    init() {
        ULEventDispatcher.getInstance().addListener(ULConst.CMD_SDK_COLLECTION_MODULE_CHECKER_CONFIG, this.getCollectionModuleCheckerPriority(), this.onCmdSdkCollectionModuleCheckerConfig.bind(this))
    }
    onDisposeModule() {
        ULLog.console.log("ULModuleBase-onDisposeModule")
    }
    loadSubModules() {
        return []
    }
    getSubAdModuleClassName() {
        return []
    }
    getAdMainClassName() {
        return ""
    }
    createSubAdObj(e, t) {
        return null
    }
    createMainAdObj() {
        return null
    }
    loadModuleCheckerConfig() {
        return []
    }
    onCmdSdkCollectionModuleCheckerConfig(e) {
        let t = e.getReturnValue() || []
          , i = this.loadModuleCheckerConfig();
        for (let e = 0; e < i.length; e++) {
            const s = i[e];
            t.push(s)
        }
        e.setReturnValue(t, !0)
    }
    getCollectionModuleCheckerPriority() {
        return ULConst.PRIORITY_NONE
    }
}

    unzip = function(e) {
        return ungzip_1(e, {
            to: "string"
        })
    }
    zip = function(e) {
        return gzip_1(e)
    }


    post_ = function (d){
        h = zip(d)
        var _ = new ArrayBuffer(h.length)
          , u = new Uint8Array(_);
        for (let e = 0; e < h.length; e++)
            u[e] = h[e];
        console.log(u)
    }


// 导出需要的函数
module.exports = {
  ungzip_1,
  gzip_1
} 