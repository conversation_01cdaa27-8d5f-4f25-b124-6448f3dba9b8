<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>根本打不过游戏调试工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        .warning {
            background-color: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .loading {
            color: #6c757d;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
        input {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
        .url-display {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>根本打不过游戏 - 调试工具</h1>
        
        <div class="test-item info">
            <h3>调试说明</h3>
            <p>这个工具用于诊断"根本打不过"游戏的下载问题，帮助定位JSON解析错误的原因。</p>
            <p><strong>默认游戏ID:</strong> 68819796</p>
            <p><strong>COS路径:</strong> /215/</p>
        </div>

        <div class="test-item">
            <h3>1. 测试nginx代理</h3>
            <input type="text" id="gameIdInput" value="68819796" placeholder="游戏ID">
            <button onclick="testNginxProxy()">测试nginx代理下载</button>
            <div id="nginxResult"></div>
        </div>

        <div class="test-item">
            <h3>2. 直接测试COS访问</h3>
            <button onclick="testDirectCOS()">直接访问COS</button>
            <div id="directResult"></div>
        </div>

        <div class="test-item">
            <h3>3. 网络诊断</h3>
            <button onclick="runDiagnostics()">运行完整诊断</button>
            <div id="diagnosticsResult"></div>
        </div>

        <div class="test-item">
            <h3>4. 响应头分析</h3>
            <button onclick="analyzeHeaders()">分析响应头</button>
            <div id="headersResult"></div>
        </div>
    </div>

    <script>
        // 测试nginx代理
        async function testNginxProxy() {
            const gameId = document.getElementById('gameIdInput').value || '68819796';
            const resultDiv = document.getElementById('nginxResult');
            resultDiv.innerHTML = '<div class="loading">正在测试nginx代理...</div>';
            
            try {
                const timestamp = Date.now();
                const url = `/cant-beat-me/download/${gameId}_Yp_Default.json?timestamp=${timestamp}`;
                
                resultDiv.innerHTML += `<div class="info"><strong>请求URL:</strong> <div class="url-display">${url}</div></div>`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': '*/*'
                    }
                });
                
                const contentType = response.headers.get('content-type');
                const responseText = await response.text();
                
                let resultHtml = `
                    <div class="info">
                        <strong>📋 响应信息</strong><br>
                        状态码: ${response.status}<br>
                        内容类型: ${contentType || '未知'}<br>
                        响应大小: ${responseText.length} 字符<br>
                    </div>
                `;
                
                if (response.ok) {
                    // 检查是否是HTML响应
                    if (responseText.trim().startsWith('<!') || responseText.trim().startsWith('<html')) {
                        resultHtml += `
                            <div class="error">
                                <strong>❌ 收到HTML响应</strong><br>
                                这表明nginx代理配置可能有问题，或者请求被重定向到了错误页面。<br>
                                <details>
                                    <summary>查看HTML内容</summary>
                                    <pre>${responseText.substring(0, 1000)}${responseText.length > 1000 ? '...' : ''}</pre>
                                </details>
                            </div>
                        `;
                    } else {
                        try {
                            const jsonData = JSON.parse(responseText);
                            resultHtml += `
                                <div class="success">
                                    <strong>✅ JSON解析成功</strong><br>
                                    数据结构正确<br>
                                    <details>
                                        <summary>查看JSON数据</summary>
                                        <pre>${JSON.stringify(jsonData, null, 2).substring(0, 1000)}...</pre>
                                    </details>
                                </div>
                            `;
                        } catch (parseError) {
                            resultHtml += `
                                <div class="error">
                                    <strong>❌ JSON解析失败</strong><br>
                                    错误: ${parseError.message}<br>
                                    <details>
                                        <summary>查看原始响应</summary>
                                        <pre>${responseText.substring(0, 1000)}${responseText.length > 1000 ? '...' : ''}</pre>
                                    </details>
                                </div>
                            `;
                        }
                    }
                } else {
                    resultHtml += `
                        <div class="error">
                            <strong>❌ HTTP错误</strong><br>
                            状态码: ${response.status}<br>
                            <details>
                                <summary>查看错误响应</summary>
                                <pre>${responseText}</pre>
                            </details>
                        </div>
                    `;
                }
                
                resultDiv.innerHTML = resultHtml;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 请求失败</strong><br>
                        错误信息: ${error.message}<br>
                        这可能是网络连接问题或CORS错误
                    </div>
                `;
            }
        }

        // 直接测试COS
        async function testDirectCOS() {
            const resultDiv = document.getElementById('directResult');
            resultDiv.innerHTML = '<div class="loading">正在测试直接COS访问...</div>';
            
            try {
                const url = 'https://yp-storage-1318351391.cos.ap-shanghai.myqcloud.com/215/68819796_Yp_Default.json';
                resultDiv.innerHTML += `<div class="info"><strong>直接COS URL:</strong> <div class="url-display">${url}</div></div>`;
                
                const response = await fetch(url, {
                    method: 'GET',
                    mode: 'cors'
                });
                
                if (response.ok) {
                    const data = await response.text();
                    try {
                        const jsonData = JSON.parse(data);
                        resultDiv.innerHTML += `
                            <div class="success">
                                <strong>✅ 直接COS访问成功</strong><br>
                                状态码: ${response.status}<br>
                                数据大小: ${data.length} 字符<br>
                                JSON解析: 成功<br>
                                <details>
                                    <summary>查看数据结构</summary>
                                    <pre>${JSON.stringify(jsonData, null, 2).substring(0, 500)}...</pre>
                                </details>
                            </div>
                        `;
                    } catch (parseError) {
                        resultDiv.innerHTML += `
                            <div class="warning">
                                <strong>⚠️ COS访问成功但JSON解析失败</strong><br>
                                这表明COS上的文件可能损坏<br>
                                解析错误: ${parseError.message}
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML += `
                        <div class="error">
                            <strong>❌ 直接COS访问失败</strong><br>
                            状态码: ${response.status}<br>
                            这表明文件可能不存在或COS配置有问题
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="error">
                        <strong>❌ 直接COS访问出错</strong><br>
                        错误信息: ${error.message}<br>
                        这可能是CORS策略限制
                    </div>
                `;
            }
        }

        // 运行完整诊断
        async function runDiagnostics() {
            const resultDiv = document.getElementById('diagnosticsResult');
            resultDiv.innerHTML = '<div class="loading">正在运行完整诊断...</div>';
            
            let diagnostics = [];
            
            // 检查1: 浏览器环境
            diagnostics.push({
                name: '浏览器环境检查',
                status: 'info',
                message: `用户代理: ${navigator.userAgent.substring(0, 100)}...`
            });
            
            // 检查2: 当前页面URL
            diagnostics.push({
                name: '当前页面URL',
                status: 'info',
                message: `页面地址: ${window.location.href}`
            });
            
            // 检查3: 测试基础网络连接
            try {
                const testResponse = await fetch('/');
                diagnostics.push({
                    name: '基础网络连接',
                    status: testResponse.ok ? 'success' : 'error',
                    message: `根路径访问: ${testResponse.status}`
                });
            } catch (error) {
                diagnostics.push({
                    name: '基础网络连接',
                    status: 'error',
                    message: `网络连接失败: ${error.message}`
                });
            }
            
            // 检查4: 测试看谁能打过游戏（对比）
            try {
                const compareResponse = await fetch('/who-beat-me/download/68173379_Yp_Default.json?timestamp=' + Date.now());
                diagnostics.push({
                    name: '看谁能打过游戏对比',
                    status: compareResponse.ok ? 'success' : 'warning',
                    message: `状态码: ${compareResponse.status}, 内容类型: ${compareResponse.headers.get('content-type') || '未知'}`
                });
            } catch (error) {
                diagnostics.push({
                    name: '看谁能打过游戏对比',
                    status: 'error',
                    message: `请求失败: ${error.message}`
                });
            }
            
            // 生成诊断报告
            let reportHtml = '<div class="info"><h4>📊 诊断报告</h4></div>';
            
            diagnostics.forEach(item => {
                const statusClass = item.status === 'success' ? 'success' : 
                                  item.status === 'error' ? 'error' : 
                                  item.status === 'warning' ? 'warning' : 'info';
                
                reportHtml += `
                    <div class="${statusClass}">
                        <strong>${item.name}:</strong> ${item.message}
                    </div>
                `;
            });
            
            resultDiv.innerHTML = reportHtml;
        }

        // 分析响应头
        async function analyzeHeaders() {
            const resultDiv = document.getElementById('headersResult');
            resultDiv.innerHTML = '<div class="loading">正在分析响应头...</div>';
            
            try {
                const url = '/cant-beat-me/download/68819796_Yp_Default.json?timestamp=' + Date.now();
                const response = await fetch(url);
                
                const headers = {};
                for (let [key, value] of response.headers.entries()) {
                    headers[key] = value;
                }
                
                let headersHtml = `
                    <div class="info">
                        <strong>📋 响应头分析</strong><br>
                        状态码: ${response.status}<br>
                        状态文本: ${response.statusText}<br>
                    </div>
                    <div class="info">
                        <strong>所有响应头:</strong>
                        <pre>${JSON.stringify(headers, null, 2)}</pre>
                    </div>
                `;
                
                // 检查关键响应头
                const criticalHeaders = ['content-type', 'access-control-allow-origin', 'x-debug-backend', 'x-debug-filename'];
                criticalHeaders.forEach(header => {
                    const value = headers[header];
                    if (value) {
                        headersHtml += `
                            <div class="success">
                                <strong>✅ ${header}:</strong> ${value}
                            </div>
                        `;
                    } else {
                        headersHtml += `
                            <div class="warning">
                                <strong>⚠️ 缺少 ${header} 响应头</strong>
                            </div>
                        `;
                    }
                });
                
                resultDiv.innerHTML = headersHtml;
                
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 响应头分析失败</strong><br>
                        错误信息: ${error.message}
                    </div>
                `;
            }
        }
    </script>
</body>
</html>
