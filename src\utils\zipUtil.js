/**
 * 基于 gzip 的压缩/解压工具类
 */

// 导入 zip_unzip 中的 ungzip_1 和 gzip_1 函数
const { ungzip_1, gzip_1 } = require('./zip_unzip')

class ZipUtil {
  /**
   * gzip解压数据
   * @param {Uint8Array} data - 需要解压的二进制数据
   * @returns {string} 解压后的字符串
   */
  static unzip(data) {
    try {
      return ungzip_1(data, {
        to: 'string'
      })
    } catch (error) {
      console.error('解压数据失败:', error)
      throw error
    }
  }

  /**
   * gzip压缩数据
   * @param {string} data - 需要压缩的字符串
   * @returns {Uint8Array} 压缩后的二进制数据
   */
  static zip(data) {
    try {
      return gzip_1(data)
    } catch (error) {
      console.error('压缩数据失败:', error)
      throw error
    }
  }

  /**
   * 将压缩后的数据转换为 Uint8Array
   * @param {string} data - 需要压缩的字符串
   * @returns {Uint8Array} 转换后的 Uint8Array 数据
   */
  static zipToUint8Array(data) {
    try {
      const zippedData = this.zip(data)
      const buffer = new ArrayBuffer(zippedData.length)
      const uint8Array = new Uint8Array(buffer)
      
      for (let i = 0; i < zippedData.length; i++) {
        uint8Array[i] = zippedData[i]
      }
      
      return uint8Array
    } catch (error) {
      console.error('转换数据失败:', error)
      throw error
    }
  }

  /**
   * 将二进制数据转换为 base64 字符串
   * @param {Uint8Array} uint8Array - 二进制数据
   * @returns {string} base64 字符串
   */
  static uint8ArrayToBase64(uint8Array) {
    try {
      let binary = ''
      for (let i = 0; i < uint8Array.length; i++) {
        binary += String.fromCharCode(uint8Array[i])
      }
      return btoa(binary)
    } catch (error) {
      console.error('转换 base64 失败:', error)
      throw error
    }
  }

  /**
   * 将 base64 字符串转换为 Uint8Array
   * @param {string} base64 - base64 字符串
   * @returns {Uint8Array} 二进制数据
   */
  static base64ToUint8Array(base64) {
    try {
      const binary = atob(base64)
      const uint8Array = new Uint8Array(binary.length)
      for (let i = 0; i < binary.length; i++) {
        uint8Array[i] = binary.charCodeAt(i)
      }
      return uint8Array
    } catch (error) {
      console.error('转换 Uint8Array 失败:', error)
      throw error
    }
  }
}

export default ZipUtil 