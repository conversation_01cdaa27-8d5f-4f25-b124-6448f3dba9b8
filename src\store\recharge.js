// 初始状态
const state = {
  gold: 1000,
  energy: 100,
  talent: 50,
  level: 10,
  talentStone: 5
}

// 修改状态的方法
const mutations = {
  updateGold(state, amount) {
    state.gold += amount
  },
  updateEnergy(state, amount) {
    state.energy += amount
  },
  updateTalent(state, amount) {
    state.talent += amount
  },
  updateLevel(state, amount) {
    state.level += amount
  },
  updateTalentStone(state, amount) {
    state.talentStone += amount
  }
}

// 异步操作
const actions = {
  recharge({ commit }, { type, amount }) {
    return new Promise((resolve) => {
      // 模拟异步操作
      setTimeout(() => {
        switch (type) {
          case 'gold':
            commit('updateGold', amount)
            break
          case 'energy':
            commit('updateEnergy', amount)
            break
          case 'talent':
            commit('updateTalent', amount)
            break
          case 'level':
            commit('updateLevel', amount)
            break
          case 'talentStone':
            commit('updateTalentStone', amount)
            break
        }
        resolve()
      }, 500) // 假设充值操作需要500ms
    })
  }
}

// 获取状态的方法
const getters = {
  getUserStats: (state) => state
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
