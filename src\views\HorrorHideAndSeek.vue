<template>
  <div class="horror-hide-and-seek">
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="game-card">
          <div class="card-header">
            <el-icon>
              <View />
            </el-icon>
            <span class="title">恐怖躲猫猫充值管理</span>
          </div>

          <div class="form-section">
            <el-form ref="formRef" :model="form" label-position="top">
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item>
                    <template #label>
                      <div class="label-wrapper">
                        <span>sessionKey</span>
                        <span class="clear-btn" @click="clearSessionKey">清空</span>
                      </div>
                    </template>
                    <el-input v-model="form.gameId" placeholder="请输入sessionKey" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <div class="button-group">
            <el-button type="primary" @click="handleDownload" :loading="loading.download" :icon="Download"
              class="action-button">下载数据</el-button>
            <el-button type="success" @click="handleUpload" :loading="loading.upload" :icon="Upload"
              class="action-button" :disabled="!hasData">上传数据</el-button>
          </div>

          <div v-if="hasData" class="game-data-section">
            <el-tabs v-model="activeTab">
              <el-tab-pane label="基础信息">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="广告卷">
                        <el-input-number v-model="gameData.adTickets" :min="0" :precision="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="金币">
                        <el-input-number v-model="gameData.coins" :min="0" :precision="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="等级">
                        <el-input-number v-model="gameData.level" :min="1" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <el-tab-pane label="道具信息">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="道具1">
                        <el-input-number v-model="gameData.item1" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="道具2">
                        <el-input-number v-model="gameData.item2" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="道具3">
                        <el-input-number v-model="gameData.item3" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <el-tab-pane label="其他设置">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="音乐">
                        <el-switch v-model="gameData.musicEnabled" active-text="开启" inactive-text="关闭" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="音效">
                        <el-switch v-model="gameData.soundEnabled" active-text="开启" inactive-text="关闭" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>

          <div v-if="operationHistory.length" class="history-section">
            <div class="section-title">
              <el-icon>
                <Timer />
              </el-icon>
              <span>操作记录</span>
            </div>
            <el-timeline>
              <el-timeline-item v-for="(history, index) in operationHistory.slice(0, 5)" :key="index"
                :type="history.success ? 'success' : 'danger'" :timestamp="history.time" :hollow="true" size="normal">
                {{ history.operation }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import {
  View,
  Download,
  Upload,
  Timer
} from '@element-plus/icons-vue'

// 表单引用
const formRef = ref(null)

// 表单数据
const form = reactive({
  gameId: 'eyJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NDAzNzc3NDUsImV4cCI6MTc0MDk4MjU0NSwiaXNzIjoiMDAxN2IxZGYtNTg1Ny1hMzgxLTAwMDAtMDAwMDAwMDA0ZTIxIiwic3ViIjoiNzY2ODY5IiwianRpIjoiMjI0MTY1NCJ9.kLCMtIxvi5JObES4EtCwQoc3kTFw7eGv2gvYI8cJp8o'
})

// 游戏数据
const gameData = reactive({
  adTickets: 0,  // 广告卷
  coins: 0,      // 金币
  level: 1,
  item1: 0,
  item2: 0,
  item3: 0,
  musicEnabled: true,
  soundEnabled: true
})

// 加载状态
const loading = reactive({
  download: false,
  upload: false
})

// 活动标签页
const activeTab = ref('0')

// 是否有数据
const hasData = ref(false)

// 操作历史
const operationHistory = ref([])

// 添加操作历史记录
const addHistory = (operation, success = true) => {
  operationHistory.value.unshift({
    operation,
    success,
    time: new Date().toLocaleString()
  })
}

// 下载数据
const handleDownload = async () => {
  if (!form.gameId) {
    ElMessage.warning('请填写完整的sessionKey')
    return
  }

  loading.download = true
  try {
    const params = {
      includes: 'DateType_PlayerData,DateType_GameData,DateType_SeasonData,DateType_RecordData',
      session_key: form.gameId
    }

    const response = await fetch(`/api/v1/archive/batch_get?${new URLSearchParams(params)}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': '*/*'
      }
    })

    if (!response.ok) {
      throw new Error('网络请求失败')
    }

    const data = await response.json()

    // 处理返回的数据
    if (data && data.data && data.data.DateType_PlayerData) {
      const playerData = data.data.DateType_PlayerData.data
      if (playerData.playerBagData) {
        try {
          const bagData = JSON.parse(playerData.playerBagData)
          // 设置广告卷和金币数据
          gameData.adTickets = parseInt(bagData['3']) || 0  // 广告卷
          gameData.coins = parseInt(bagData['4']) || 0      // 金币
        } catch (e) {
          console.error('解析背包数据失败:', e)
        }
      }

      hasData.value = true
      addHistory('下载数据成功')
      ElMessage.success('数据下载成功')
    }
  } catch (error) {
    console.error('下载数据失败:', error)
    addHistory('下载数据失败', false)
    ElMessage.error('下载数据失败')
  } finally {
    loading.download = false
  }
}

// 上传数据
const handleUpload = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.upload = true
  try {
    // 构建上传数据
    const uploadData = {
      DateType_PlayerData: {
        isNewPlay: "IsNewPlay",
        lastGameDate: 1740368022474,
        CreationTime: 0,
        haveRoleArr: "[1001,1002,1003,1004,1005,1006,1007,1008]",
        haveMonsterArr: "[]",
        freeToPlayRoleLimitTimes: "{}",
        useRoleId: 1001,
        useMonsterId: 2001,
        showSkinId: 1001,
        playerBagData: JSON.stringify({
          "3": gameData.adTickets,  // 广告卷
          "4": gameData.coins       // 金币
        }),
        UnlockMapId: "[801]",
        isAgreeToPrivacy: false,
        dailyPrivacyAuthorize: "{}",
        thirdOartyRoleInfo: "{}",
        applyThirdOartyRoleInfoTime: 0,
        UseHeadId: 1001,
        playerNickName: "普通玩家",
        HavePlayerTitleArr: "[]",
        UsePlayerTitleId: 0,
        playerExp: 20,
        playerLoginTime: 1740377745761,
        playerLoginGameData: "{\"ContinuousLoginDays\":1}"
      },
      DateType_GameData: {
        gameShock: false,
        GamePlayTime: 2,
        StartGameTime: 3,
        GamePlayTime_Hide: 2,
        GamePlayTime_Hunt: 0,
        NearSixOutcome: "[false,false]",
        winStreakTimes_hide: 0,
        lossContinuousTimes_hide: 2,
        winStreakTimes_hunt: 0,
        lossContinuousTimes_hunt: 0,
        CountModelAIData_Hide: "{\"YaLiCount\":3,\"ShuangYingCount\":2,\"TangYingCount\":0,\"BiShuCount\":5}",
        CountModelAIData_Hunt: "{\"YaLiCount\":4,\"ShuangYingCount\":2,\"TangYingCount\":2,\"BiShuCount\":5}",
        CountModePlayeTimes: "{\"1\":{\"win\":0,\"lose\":2}}",
        slelectMapId: 801
      },
      DateType_SeasonData: {
        curSeason: 0,
        curSeasonMaxScore: 0,
        curSeasonStartTimes: 0,
        curSeasonFreeRWState: "{}",
        curSeasonVideoRWState: "{}",
        seasonDanCorrelationData: "{\"curSeasonId\":1,\"dailyAllowance\":[]}"
      },
      DateType_RecordData: {
        shopFirstFree: "{}",
        taskTypeSaveData: "{\"101\":0,\"102\":0,\"201\":2}",
        getAdTaskReward: "{}",
        GuideTaskState: "[]",
        NoviceRBIRecord: "[9001,9002,9003,9004,9005]",
        TrunTableTime: 2,
        SetCommonUseTime: 0,
        GameSpecialPopTime: "{}",
        CanSign: false,
        SignBitData: 2,
        SignPopTime: 0,
        HeadIconClickTime: "{}",
        TitleIconClickTime: "{}",
        PlatformReward: "{\"8\":0,\"9\":0}",
        TrunTableAllTime: 2,
        TrunTableExtraReward: "{}",
        unlockgridnum: 0,
        roleBookReward: "{}",
        enterHuntModeData: "{}",
        specialPropData: "{}",
        settlementRecordData: "{\"playerlv\":[1,1],\"RankScore\":[1,1]}",
        NewVersionPorpData: "{}",
        NewVersionReward: "{\"dailyCoin\":1}",
        PropInPortalShowTime: "{}",
        loopSignData: "{}",
        DiscountedGiftPack: "{\"discount\":0.3,\"discountRoleArr\":[100301,1009,100101,100801,101201,100501,100601,1010,100401,101101,1011,100201,100701,101001,100901],\"isBuy\":false}",
        foreverunlockgridnum: 0,
        NewYearGift: false,
        SystemNoticeData: "{\"dailyPopOpenViewTimes\":1,\"version\":20250120,\"popCloseViewTimes\":1}"
      }
    }

    const response = await fetch(`/api/v1/archive/batch_set?session_key=${form.gameId}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*'
      },
      body: JSON.stringify(uploadData)
    })

    if (!response.ok) {
      throw new Error('上传失败')
    }

    const result = await response.json()
    if (result.code === 0) {
      addHistory('上传数据成功')
      ElMessage.success('数据上传成功')
    } else {
      throw new Error(result.message || '上传失败')
    }
  } catch (error) {
    console.error('上传数据失败:', error)
    addHistory('上传数据失败', false)
    ElMessage.error('上传数据失败')
  } finally {
    loading.upload = false
  }
}

// 清空 sessionKey
const clearSessionKey = () => {
  form.gameId = ''
}
</script>

<style scoped>
.horror-hide-and-seek {
  padding: 20px;
}

.game-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.card-header .el-icon {
  font-size: 24px;
  margin-right: 10px;
  color: #409EFF;
}

.card-header .title {
  font-size: 18px;
  font-weight: bold;
}

.form-section {
  margin-bottom: 20px;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.action-button {
  min-width: 120px;
}

.game-data-section {
  margin-top: 20px;
  border-top: 1px solid #EBEEF5;
  padding-top: 20px;
}

.history-section {
  margin-top: 20px;
  border-top: 1px solid #EBEEF5;
  padding-top: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: bold;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

:deep(.el-timeline-item__node--normal) {
  left: -2px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 25px;
}

:deep(.el-timeline-item__timestamp) {
  font-size: 13px;
}

@media (max-width: 768px) {
  .horror-hide-and-seek {
    padding: 10px;
  }

  .button-group {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
  }
}

.label-wrapper {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
}

.label-wrapper span:first-child {
  margin-right: 15px;
}

.clear-btn {
  padding: 1px 6px;
  border: 1px dashed #909399;
  color: #909399;
  cursor: pointer;
  border-radius: 3px;
  font-size: 12px;
  transition: all 0.3s;
  line-height: 1.4;
  background-color: rgba(144, 147, 153, 0.05);
  user-select: none;
}

.clear-btn:hover {
  color: #409EFF;
  border-color: #409EFF;
  background-color: rgba(64, 158, 255, 0.05);
}

.clear-btn:active {
  transform: scale(0.98);
}
</style>