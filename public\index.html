<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width,initial-scale=1.0" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico" />
    <title><%= htmlWebpackPlugin.options.title %></title>
    <!-- pako库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pako/2.1.0/pako.min.js"></script>
    <!-- 全局错误处理脚本 -->
    <script>
      // 全局拦截ResizeObserver错误
      const originalErrorHandler = window.onerror;
      window.onerror = function (message, source, lineno, colno, error) {
        if (message && message.toString().includes("ResizeObserver")) {
          // 阻止错误传播
          return true;
        }
        // 其他错误正常处理
        return originalErrorHandler
          ? originalErrorHandler(message, source, lineno, colno, error)
          : false;
      };

      // 另一种捕获方式 - 处理未捕获的promise错误
      window.addEventListener("unhandledrejection", function (event) {
        if (
          event.reason &&
          event.reason.message &&
          event.reason.message.includes("ResizeObserver")
        ) {
          // 阻止显示错误
          event.preventDefault();
        }
      });

      // 为所有iframe添加相同的错误处理
      window.addEventListener("DOMContentLoaded", function () {
        // 为webpack-dev-server overlay iframe添加错误处理
        try {
          const handleIframes = function () {
            const iframes = document.querySelectorAll("iframe");
            iframes.forEach((iframe) => {
              try {
                if (iframe.contentWindow) {
                  iframe.contentWindow.onerror = window.onerror;
                  iframe.contentWindow.addEventListener(
                    "unhandledrejection",
                    window.addEventListener("unhandledrejection")
                  );
                }
              } catch (e) {
                // 跨域iframe无法访问
              }
            });
          };

          // 立即处理现有iframe
          handleIframes();

          // 监听可能后续添加的iframe
          const observer = new MutationObserver(function (mutations) {
            handleIframes();
          });

          observer.observe(document.body, {
            childList: true,
            subtree: true,
          });
        } catch (e) {
          // 忽略错误
        }
      });
    </script>
  </head>
  <body>
    <noscript>
      <strong
        >We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work
        properly without JavaScript enabled. Please enable it to
        continue.</strong
      >
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
