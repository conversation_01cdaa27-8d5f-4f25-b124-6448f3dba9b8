<template>
  <div class="user-information">
    <h1>用户信息</h1>
    <UserStats :stats="userStats" />
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import UserStats from '@/components/UserStats.vue'

export default {
  name: 'UserInformation',
  components: {
    UserStats
  },
  computed: {
    ...mapGetters('recharge', ['getUserStats']),
    userStats() {
      return this.getUserStats
    }
  }
}
</script>
