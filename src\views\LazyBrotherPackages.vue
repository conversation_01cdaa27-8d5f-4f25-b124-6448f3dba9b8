<template>
  <div class="lazy-brother-packages">
    <div class="watermark">
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
    </div>
    <div class="content-wrapper">
      <div class="page-header">
        <div class="header-content">
          <div class="title-icon">
            <el-icon>
              <MoonNight />
            </el-icon>
          </div>
          <div class="title-text">躺平小弟套餐</div>
        </div>
        <div class="title-decoration"></div>
        <div class="tags-row">
          <div class="tag green">
            <el-icon>
              <Check />
            </el-icon>
            <span>V小程序充值</span>
          </div>
          <div class="tag red">
            <el-icon>
              <MagicStick />
            </el-icon>
            <span>一键拉满</span>
          </div>
        </div>
      </div>

      <div class="packages-table">
        <!-- 表头 -->
        <div class="table-header">
          <div class="table-cell package-col">套餐</div>
          <div class="table-cell content-col">内容</div>
          <div class="table-cell price-col">价格</div>
          <div class="table-cell gift-col">五星晒图赠送</div>
        </div>

        <!-- 套餐1 -->
        <div class="table-row package-1">
          <div class="table-cell package-col">1</div>
          <div class="table-cell content-col">
            <div class="content-item">
              <el-icon>
                <Ticket />
              </el-icon>
              <span>无限广告券</span>
            </div>
          </div>
          <div class="table-cell price-col">10</div>
          <div class="table-cell gift-col">
            <div class="gift-item">
              <el-icon>
                <Coin />
              </el-icon>
              <span>无限金币</span>
            </div>
          </div>
        </div>

        <!-- 套餐2 -->
        <div class="table-row package-2">
          <div class="table-cell package-col">2</div>
          <div class="table-cell content-col">
            <div class="content-item">
              <el-icon>
                <Coin />
              </el-icon>
              <span>无限金币</span>
            </div>
            <div class="content-item">
              <el-icon>
                <Star />
              </el-icon>
              <span>无限钻石</span>
            </div>
            <div class="content-item">
              <el-icon>
                <Lightning />
              </el-icon>
              <span>无限体力</span>
            </div>
          </div>
          <div class="table-cell price-col">15</div>
          <div class="table-cell gift-col">
            <div class="gift-item">
              <el-icon>
                <Ticket />
              </el-icon>
              <span>无限免广告券</span>
            </div>
          </div>
        </div>

        <!-- 套餐3（推荐） -->
        <div class="table-row package-3">
          <div class="table-cell package-col">
            <div class="package-name">
              <div class="recommend-badge">推荐</div>
              <div>3</div>
              <div>无限畅玩</div>
            </div>
          </div>
          <div class="table-cell content-col">
            <div class="content-item">
              <el-icon>
                <Coin />
              </el-icon>
              <span>无限金币</span>
            </div>
            <div class="content-item">
              <el-icon>
                <Star />
              </el-icon>
              <span>无限钻石</span>
            </div>
            <div class="content-item">
              <el-icon>
                <Lightning />
              </el-icon>
              <span>无限体力</span>
            </div>
            <div class="content-item">
              <el-icon>
                <Ticket />
              </el-icon>
              <span>无限免广告券</span>
            </div>
          </div>
          <div class="table-cell price-col">20</div>
          <div class="table-cell gift-col">
            <div class="gift-item">
              <el-icon>
                <Collection />
              </el-icon>
              <span>无限装备碎片</span>
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-notice">
        <div class="warning-message">
          <el-icon>
            <Warning />
          </el-icon>
          <span>注意!充值成功后进度会重新开始!!!</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  MoonNight, Star, Money, Lightning, Present, Check, Timer,
  Coin, GoldMedal, Trophy, Medal, Discount, Collection,
  VideoPlay, Ticket, MagicStick
} from '@element-plus/icons-vue'

const handlePackageClick = () => {
  // 处理点击事件
}
</script>

<style scoped>
.lazy-brother-packages {
  background: linear-gradient(135deg, #2b5876 0%, #4e4376 100%);
  padding: 10px;
  color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  overflow-x: hidden;
}

.watermark {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(4, 1fr);
  color: rgba(255, 255, 255, 0.06);
  font-size: 24px;
  pointer-events: none;
  z-index: 100;
  user-select: none;
  transform: rotate(-30deg);
  place-items: center;
  font-weight: 500;
}

.watermark-text {
  white-space: nowrap;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}

.content-wrapper {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 15px 20px 20px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

.page-header {
  background-color: #274060;
  padding: 18px 15px 0 15px;
  margin-bottom: 20px;
  width: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  width: 100%;
  justify-content: center;
}

.title-icon {
  font-size: 34px;
  color: white;
}

.title-text {
  font-size: 34px;
  font-weight: bold;
  color: white;
}

.title-decoration {
  height: 4px;
  background: #F56C6C;
  width: 100%;
  margin-bottom: 12px;
}

.tags-row {
  display: flex;
  width: 100%;
  max-width: 340px;
  margin: 0 auto 12px auto;
  gap: 10px;
}

.tag {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 9px 0;
  border-radius: 30px;
  font-size: 16px;
  font-weight: bold;
}

.tag.green {
  background-color: #67c23a;
  color: white;
}

.tag.red {
  background-color: #f56c6c;
  color: white;
}

.tag .el-icon {
  font-size: 18px;
}

.packages-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0 auto;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  position: relative;
  max-width: 900px;
}

.table-header {
  display: flex;
  background: linear-gradient(135deg, #ff8e67, #ff5555);
  color: white;
  font-weight: bold;
  text-align: center;
  padding: 8px 0;
  font-size: 20px;
}

.table-row {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 5px 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.package-col {
  flex: 0.2;
  font-weight: bold;
  font-size: 28px;
  min-width: 50px;
}

.content-col {
  flex: 1.2;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.price-col {
  flex: 0.3;
  font-size: 28px;
  font-weight: bold;
  min-width: 50px;
}

.gift-col {
  flex: 0.8;
  flex-direction: column;
}

.content-item,
.gift-item {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-bottom: 4px;
  padding: 1px 0;
  transition: transform 0.2s ease;
  font-size: 20px;
  justify-content: center;
}

.content-item .el-icon,
.gift-item .el-icon {
  font-size: 24px;
  margin-right: 2px;
}

.content-item:hover,
.gift-item:hover {
  transform: translateX(3px);
}

/* 套餐行的背景色 */
.package-1 {
  background: linear-gradient(to right, #02aab0, #00cdac);
  color: white;
}

.package-2 {
  background: linear-gradient(to right, #396afc, #2948ff);
  color: white;
}

.package-3 {
  background: linear-gradient(to right, #834d9b, #d04ed6);
  color: white;
  position: relative;
  overflow: hidden;
}

.package-3::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="100" height="100" viewBox="0 0 100 100"><rect x="0" y="0" width="50" height="50" fill="rgba(255,255,255,0.05)"/><rect x="50" y="50" width="50" height="50" fill="rgba(255,255,255,0.05)"/></svg>');
  background-size: 20px 20px;
  opacity: 0.5;
  pointer-events: none;
}

.package-3 .content-item,
.package-3 .gift-item,
.package-3 .package-name {
  color: white;
  position: relative;
  z-index: 1;
}

.package-3 .content-item .el-icon,
.package-3 .gift-item .el-icon {
  color: rgba(255, 255, 255, 0.9);
}

.package-name {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  gap: 4px;
  position: relative;
  padding: 15px 0;
}

.package-name div {
  font-size: 16px;
  color: white;
}

.package-name div:nth-child(2) {
  font-size: 24px;
  font-weight: bold;
  margin: 5px 0;
}

.package-name div:last-child {
  font-size: 14px;
  opacity: 0.9;
}

.recommend-badge {
  background: #ff6b6b;
  color: white;
  padding: 3px 8px;
  border-radius: 30px;
  font-size: 14px;
  position: absolute;
  left: 10px;
  top: 10px;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transform: none;
}

.bottom-notice {
  margin-top: 20px;
  text-align: center;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9);
  padding: 14px 15px;
  background: rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(5px);
  border-radius: 10px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.warning-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: bold;
  color: #ffcc00;
  font-size: 18px;
}

.warning-message .el-icon {
  font-size: 22px;
  color: #ffcc00;
}

@media (max-width: 768px) {
  .lazy-brother-packages {
    padding: 0;
  }

  .content-wrapper {
    padding: 12px;
  }

  .watermark {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(6, 1fr);
    font-size: 18px;
  }

  .page-header {
    padding: 12px 12px 0 12px;
    margin-bottom: 15px;
  }

  .title-text {
    font-size: 28px;
  }

  .title-icon {
    font-size: 28px;
  }

  .table-header {
    display: none;
    /* 在移动端隐藏表头 */
  }

  .packages-table {
    display: flex;
    flex-direction: column;
    gap: 20px;
    box-shadow: none;
    background: transparent;
  }

  .table-row {
    flex-direction: column;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    margin-bottom: 20px;
    transform: scale(1);
    transition: all 0.3s ease;
  }

  .table-row:active {
    transform: scale(0.98);
  }

  .table-cell {
    width: 100%;
    box-sizing: border-box;
    padding: 8px;
  }

  .package-col {
    font-size: 26px;
    padding: 20px 12px;
    position: relative;
  }

  .package-col::after {
    content: "套餐";
    position: absolute;
    right: 12px;
    font-size: 18px;
    opacity: 0.8;
  }

  .content-col {
    position: relative;
    padding-top: 30px;
    background-color: rgba(0, 0, 0, 0.1);
    align-items: center;
  }

  .content-col::before {
    content: "内容";
    position: absolute;
    top: 8px;
    left: 12px;
    font-size: 18px;
    opacity: 0.8;
  }

  .price-col {
    position: relative;
    font-size: 30px;
    padding: 20px 12px;
    background: rgba(0, 0, 0, 0.05);
  }

  .price-col::before {
    content: "¥";
    font-size: 26px;
    margin-right: 2px;
    vertical-align: top;
    position: relative;
    top: -2px;
  }

  .price-col::after {
    content: "价格";
    position: absolute;
    top: 8px;
    right: 12px;
    font-size: 18px;
    opacity: 0.8;
  }

  .gift-col {
    position: relative;
    padding-top: 30px;
    padding-bottom: 15px;
    background-color: rgba(255, 255, 255, 0.1);
  }

  .gift-col::before {
    content: "五星晒图赠送";
    position: absolute;
    top: 8px;
    left: 0;
    right: 0;
    text-align: center;
    font-size: 18px;
    opacity: 0.8;
  }

  .content-item,
  .gift-item {
    margin-bottom: 8px;
    font-size: 18px;
  }

  .content-item .el-icon,
  .gift-item .el-icon {
    font-size: 20px;
  }

  .package-name {
    gap: 2px;
  }

  .package-name div:first-child {
    font-size: 18px;
    background: #FF5E62;
    color: white;
    padding: 6px 15px;
    border-radius: 20px;
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 2;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
  }

  .bottom-notice {
    font-size: 15px;
    line-height: 1.5;
    padding: 12px 15px;
    margin-top: 10px;
    margin-bottom: 15px;
  }

  .tag {
    padding: 6px 0;
    font-size: 14px;
  }

  .tag .el-icon {
    font-size: 16px;
  }

  .title-decoration {
    bottom: 36px;
  }

  .warning-message {
    font-size: 16px;
  }

  .warning-message .el-icon {
    font-size: 20px;
  }

  .recommend-badge {
    left: 15px;
    top: 15px;
    font-size: 14px;
    padding: 2px 10px;
  }

  .package-name {
    gap: 2px;
    padding: 10px 0;
  }

  .package-name div:nth-child(2) {
    font-size: 22px;
    margin: 3px 0;
  }

  .package-name div:last-child {
    font-size: 13px;
  }
}
</style>