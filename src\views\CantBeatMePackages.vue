<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="cant-beat-me-packages">
      <div class="watermark"></div>
      <div class="page-header">
        <div class="title-wrapper">
          <h2>
            <el-icon><KnifeFork /></el-icon>
            砍不过我呀套餐
          </h2>
          <div class="title-decoration"></div>
        </div>
        <div class="tags-container">
          <el-tag type="success" effect="dark" class="support-tag glow">
            <el-icon><Check /></el-icon>
            WX小程序支持
          </el-tag>
          <el-tag type="warning" effect="dark" class="support-tag glow">
            <el-icon><Timer /></el-icon>
            秒冲秒到账
          </el-tag>
        </div>
      </div>

      <div class="package-list">
        <!-- 基础套餐 -->
        <div class="package-card">
          <div class="package-header basic">
            <div class="icon-wrapper">
              <el-icon class="package-icon"><Money /></el-icon>
            </div>
            <div class="package-info">
              <span class="package-title">基础套餐</span>
              <div class="package-features">
                <div class="feature">
                  <el-icon><Coin /></el-icon>
                  <span>无限金币</span>
                </div>
                <div class="feature">
                  <el-icon><GoldMedal /></el-icon>
                  <span>无限钻石</span>
                </div>
                <div class="feature">
                  <el-icon><Lightning /></el-icon>
                  <span>无限体力</span>
                </div>
              </div>
            </div>
          </div>
          <div class="package-price">
            <el-tag type="success" effect="dark" class="price-tag">
              <span class="price-value">5</span>
              <span class="price-unit">元</span>
            </el-tag>
          </div>
        </div>

        <!-- 豪华套餐 -->
        <div class="package-card highlight">
          <div class="hot-tag">热门</div>
          <div class="package-header premium">
            <div class="icon-wrapper">
              <el-icon class="package-icon"><Star /></el-icon>
            </div>
            <div class="package-info">
              <span class="package-title">豪华套餐</span>
              <div class="package-features">
                <div class="feature">
                  <el-icon><Coin /></el-icon>
                  <span>无限金币</span>
                </div>
                <div class="feature">
                  <el-icon><GoldMedal /></el-icon>
                  <span>无限钻石</span>
                </div>
                <div class="feature">
                  <el-icon><Lightning /></el-icon>
                  <span>无限体力</span>
                </div>
                <div class="feature">
                  <el-icon><User /></el-icon>
                  <span>全英雄解锁</span>
                </div>
                <div class="feature">
                  <el-icon><Box /></el-icon>
                  <span>全装备碎片</span>
                </div>
                <div class="feature">
                  <el-icon><Present /></el-icon>
                  <span>全道具碎片</span>
                </div>
              </div>
            </div>
          </div>
          <div class="package-price">
            <div class="special-price">
              <div class="special-tag">特惠</div>
              <el-tag type="warning" effect="dark" class="price-tag">
                <span class="price-value">10</span>
                <span class="price-unit">元</span>
              </el-tag>
            </div>
          </div>
        </div>

        <!-- 至尊套餐 -->
        <div class="package-card highlight supreme">
          <div class="supreme-tag">至尊</div>
          <div class="package-header supreme">
            <div class="icon-wrapper supreme">
              <el-icon class="package-icon"><Trophy /></el-icon>
            </div>
            <div class="package-info">
              <span class="package-title">至尊无限</span>
              <div class="package-features">
                <div class="feature">
                  <el-icon><Coin /></el-icon>
                  <span>无限金币</span>
                </div>
                <div class="feature">
                  <el-icon><GoldMedal /></el-icon>
                  <span>无限钻石</span>
                </div>
                <div class="feature">
                  <el-icon><Lightning /></el-icon>
                  <span>无限体力</span>
                </div>
                <div class="feature">
                  <el-icon><User /></el-icon>
                  <span>全英雄解锁</span>
                </div>
                <div class="feature">
                  <el-icon><Box /></el-icon>
                  <span>全装备碎片</span>
                </div>
                <div class="feature">
                  <el-icon><Present /></el-icon>
                  <span>全道具碎片</span>
                </div>
                <div class="feature supreme-feature">
                  <el-icon><Medal /></el-icon>
                  <span>英雄碎片无限</span>
                </div>
              </div>
            </div>
          </div>
          <div class="package-price">
            <div class="special-price">
              <div class="supreme-price-tag">尊享</div>
              <el-tag type="danger" effect="dark" class="price-tag">
                <span class="price-value">15</span>
                <span class="price-unit">元</span>
              </el-tag>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { 
  Star,
  Money,
  Lightning,
  User,
  Box,
  Present,
  Check,
  Timer,
  KnifeFork,
  Coin,
  GoldMedal,
  Trophy,
  Medal
} from '@element-plus/icons-vue'
</script>

<style scoped>
.cant-beat-me-packages {
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
  position: relative;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.title-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.title-wrapper h2 {
  font-size: 28px;
  color: #303133;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.title-decoration {
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #409EFF, #67C23A);
  border-radius: 2px;
}

.tags-container {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 15px;
}

.support-tag {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 12px;
}

.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px rgba(255, 255, 255, .2);
  }
  to {
    box-shadow: 0 0 15px rgba(255, 255, 255, .4);
  }
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 25px;
  max-width: 450px;
  margin: 0 auto;
  padding: 10px;
}

.package-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  transition: transform 0.3s ease;
}

.package-card:hover {
  transform: translateY(-5px);
}

.package-card.highlight {
  background: linear-gradient(135deg, #fff 0%, #f0f9ff 100%);
  border: 2px solid #E6A23C;
}

.hot-tag {
  position: absolute;
  top: -12px;
  right: 20px;
  background: #F56C6C;
  color: white;
  padding: 2px 12px;
  border-radius: 12px;
  font-size: 12px;
  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.4);
}

.package-header {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  margin-bottom: 15px;
}

.icon-wrapper {
  background: linear-gradient(135deg, #409EFF 0%, #67C23A 100%);
  padding: 10px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.package-icon {
  font-size: 24px;
  color: white;
}

.package-info {
  flex: 1;
}

.package-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
  display: block;
  margin-bottom: 10px;
}

.package-features {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 8px;
}

.feature {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #606266;
  font-size: 14px;
}

.feature .el-icon {
  color: #409EFF;
}

.package-price {
  display: flex;
  justify-content: center;
  padding-right: 60px;
  margin-top: 15px;
}

.price-tag {
  padding: 8px 15px;
  border-radius: 20px;
  min-width: 80px;
  text-align: center;
}

.price-value {
  font-size: 24px;
  font-weight: bold;
  margin-right: 2px;
}

.price-unit {
  font-size: 14px;
}

.special-price {
  position: relative;
  display: inline-block;
}

.special-tag {
  position: absolute;
  top: -18px;
  left: -10px;
  background: #F56C6C;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 8px 8px 8px 0;
  transform: rotate(-15deg);
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
  z-index: 1;
}

.package-card.supreme {
  background: linear-gradient(135deg, #fff 0%, #fff5f5 100%);
  border: 2px solid #F56C6C;
}

.supreme-tag {
  position: absolute;
  top: -12px;
  right: 20px;
  background: #F56C6C;
  color: white;
  padding: 2px 12px;
  border-radius: 12px;
  font-size: 12px;
  box-shadow: 0 2px 6px rgba(245, 108, 108, 0.4);
  animation: glow 2s ease-in-out infinite alternate;
}

.icon-wrapper.supreme {
  background: linear-gradient(135deg, #F56C6C 0%, #E6A23C 100%);
}

.supreme-feature {
  color: #F56C6C !important;
}

.supreme-feature .el-icon {
  color: #F56C6C !important;
}

.supreme-price-tag {
  position: absolute;
  top: -18px;
  left: -10px;
  background: #F56C6C;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 8px 8px 8px 0;
  transform: rotate(-15deg);
  box-shadow: 0 2px 4px rgba(245, 108, 108, 0.3);
  z-index: 1;
  animation: glow 2s ease-in-out infinite alternate;
}

@media (max-width: 768px) {
  .cant-beat-me-packages {
    padding: 15px;
  }
  
  .package-features {
    grid-template-columns: repeat(2, 1fr);
  }
}

.watermark {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 100;
  background-image: linear-gradient(
    -45deg,
    rgba(0, 0, 0, 0.03) 25%,
    transparent 25%,
    transparent 50%,
    rgba(0, 0, 0, 0.03) 50%,
    rgba(0, 0, 0, 0.03) 75%,
    transparent 75%,
    transparent
  );
  background-size: 100px 100px;
}

.watermark::before {
  content: "跳跳鱼小游戏";
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 48px;
  color: rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  pointer-events: none;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.watermark::after {
  content: "跳跳鱼小游戏";
  position: fixed;
  top: 25%;
  left: 25%;
  transform: translate(-50%, -50%) rotate(-45deg);
  font-size: 48px;
  color: rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  pointer-events: none;
  text-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}
</style> 