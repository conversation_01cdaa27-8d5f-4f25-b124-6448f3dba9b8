<template>
  <div class="cant-beat-me">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <KnifeFork />
              </el-icon>
              <span>砍不过我呀</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
              <el-row :gutter="20">
                <el-col :span="18">
                  <el-form-item prop="loginCode" label="游戏ID">
                    <el-input v-model="form.loginCode" placeholder="请输入游戏ID" :disabled="loading.download" clearable />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-tooltip content="手动输入版本控制号,推荐50开始" placement="top" effect="dark" :show-after="200">
                    <el-form-item prop="lv" label="版本控制" class="version-control">
                      <el-input-number v-model="form.lv" :min="1" :max="999" controls-position="right"
                        :disabled="loading.upload" />
                    </el-form-item>
                  </el-tooltip>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 操作按钮 -->
          <div class="action-section">
            <el-button type="primary" :loading="loading.download" @click="handleDownload" :icon="Download"
              class="action-button">
              下载数据
            </el-button>
            <el-button type="success" :loading="loading.upload" @click="handleUpload" :icon="Upload"
              :disabled="!hasData" class="action-button">
              上传数据
            </el-button>
            <el-tooltip content="修改金币/钻石/体力为无限(999999999)" placement="top" effect="dark">
              <el-button type="danger" :loading="loading.package1" @click="handlePackage1" :icon="Present"
                :disabled="!hasData" class="action-button">
                套餐1
              </el-button>
            </el-tooltip>
            <el-tooltip content="无限钻石+无限金币+无限体力+全英雄解锁+碎片数量999999" placement="top" effect="dark">
              <el-button type="warning" :loading="loading.package2" @click="handlePackage2" :icon="Star"
                :disabled="!hasData" class="action-button">
                套餐2
              </el-button>
            </el-tooltip>
            <el-tooltip content="无限钻石+无限金币+无限体力+全英雄解锁+碎片数量999999+英雄碎片9999" placement="top" effect="dark">
              <el-button type="danger" :loading="loading.package3" @click="handlePackage3" :icon="Star"
                :disabled="!hasData" class="action-button">
                套餐3
              </el-button>
            </el-tooltip>
          </div>

          <!-- 添加数据显示区域 -->
          <div v-if="hasData" class="game-data-section">
            <div class="section-title">
              <el-icon>
                <Document />
              </el-icon>
              <span>游戏数据</span>
            </div>

            <el-tabs type="border-card">
              <!-- 基础属性 -->
              <el-tab-pane label="基础属性">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <!-- 游戏资源 -->
                    <el-col :span="8">
                      <el-form-item label="金币" :class="{ 'modified-item': modifiedFields.includes('gold') }">
                        <el-input-number v-model="gameData.itemData['2']" :min="0" :max="999999999"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="钻石" :class="{ 'modified-item': modifiedFields.includes('diamond') }">
                        <el-input-number v-model="gameData.itemData['3']" :min="0" :max="999999999"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="体力" :class="{ 'modified-item': modifiedFields.includes('power') }">
                        <el-input-number v-model="gameData.power" :min="0" :max="999999999" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <!-- 体力系统 -->
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="体力购买上限">
                        <el-input-number v-model="gameData.powerBuyTimes[0]" :min="0" :max="99"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="已购买次数">
                        <el-input-number v-model="gameData.powerBuyTimes[1]" :min="0" :max="99"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <!-- 游戏进度 -->
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="当前关卡">
                        <el-input-number v-model="gameData.level" :min="1" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="当前波次">
                        <el-input-number v-model="gameData.levelWave" :min="1" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="扫荡次数">
                        <el-input-number v-model="gameData.sweepTimes" :min="0" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 装备系统 -->
              <el-tab-pane label="装备系统">
                <el-table :data="toolsList" border stripe style="width: 100%">
                  <el-table-column label="装备ID" prop="id" width="100" align="center" />
                  <el-table-column label="等级" width="150" align="center">
                    <template #default="scope">
                      <el-input-number v-model="gameData.toolData[scope.row.id].lv" :min="0" :max="999"
                        controls-position="right" style="width: 100%" />
                    </template>
                  </el-table-column>
                  <el-table-column label="碎片数量" width="150" align="center">
                    <template #default="scope">
                      <el-input-number v-model="gameData.toolData[scope.row.id].c" :min="0" :max="999999"
                        controls-position="right" style="width: 100%" />
                    </template>
                  </el-table-column>
                  <el-table-column label="类型" width="120" align="center">
                    <template #default="scope">
                      <el-tag :type="scope.row.id >= 502 ? 'danger' : 'info'">
                        {{ scope.row.id >= 502 ? '携带道具' : '普通装备' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>

              <!-- 任务系统 -->
              <el-tab-pane label="任务系统">
                <el-tabs>
                  <el-tab-pane label="每日任务">
                    <el-table :data="dailyMissionList" border stripe>
                      <el-table-column label="任务ID" prop="id" width="100" />
                      <el-table-column label="进度" width="150">
                        <template #default="scope">
                          <el-input-number v-model="gameData.dayMissionData[scope.row.id].pro" :min="0"
                            controls-position="right" style="width: 100%" />
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" width="120">
                        <template #default="scope">
                          <el-switch v-model="gameData.dayMissionData[scope.row.id].isReward" active-text="已领取"
                            inactive-text="未领取" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>

                  <el-tab-pane label="周常任务">
                    <el-table :data="weekMissionList" border stripe>
                      <el-table-column label="任务ID" prop="id" width="100" />
                      <el-table-column label="进度" width="150">
                        <template #default="scope">
                          <el-input-number v-model="gameData.weekMissionData[scope.row.id].pro" :min="0"
                            controls-position="right" style="width: 100%" />
                        </template>
                      </el-table-column>
                      <el-table-column label="状态" width="120">
                        <template #default="scope">
                          <el-switch v-model="gameData.weekMissionData[scope.row.id].isReward" active-text="已领取"
                            inactive-text="未领取" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>

              <!-- 英雄系统 -->
              <el-tab-pane label="英雄系统">
                <el-tabs>
                  <!-- 英雄列表 -->
                  <el-tab-pane label="英雄列表">
                    <el-row :gutter="20">
                      <el-col :span="12">
                        <h3>上阵中的英雄</h3>
                        <div style="border: 2px solid #ff4949; padding: 10px; border-radius: 4px;">
                          <div style="color: #ff4949; margin-bottom: 10px; font-size: 12px;">
                            注意：这里修改的是上阵英雄，请勿随意修改！
                          </div>
                          <el-select 
                            v-model="gameData.arrayHeros" 
                            multiple 
                            placeholder="选择英雄"
                            style="width: 100%"
                          >
                            <el-option
                              v-for="id in 10"
                              :key="id"
                              :label="`英雄${id}`"
                              :value="id"
                            />
                          </el-select>
                        </div>
                      </el-col>
                      <el-col :span="12">
                        <h3>已解锁英雄</h3>
                        <el-select v-model="gameData.unlockHeros" multiple placeholder="选择英雄" style="width: 100%">
                          <el-option v-for="id in 10" :key="id" :label="`英雄${id}`" :value="id" />
                        </el-select>
                      </el-col>
                    </el-row>
                  </el-tab-pane>

                  <!-- 英雄天赋 -->
                  <el-tab-pane label="英雄天赋">
                    <!-- 添加手动添加英雄按钮 -->
                    <div class="hero-actions" style="margin-bottom: 16px;">
                      <el-button type="primary" @click="handleAddAllMissingHeroes" :icon="Plus"
                        :disabled="missingHeroIds.length === 0">
                        一键添加缺少的英雄
                      </el-button>
                      <el-tag v-if="missingHeroIds.length > 0" type="warning" style="margin-left: 10px;">
                        缺少英雄ID: {{ missingHeroIds.join(', ') }}
                      </el-tag>
                    </div>
                    <el-table :data="heroList" border stripe>
                      <el-table-column label="英雄ID" prop="id" width="100" align="center" />
                      <el-table-column label="等级" width="150" align="center">
                        <template #default="scope">
                          <el-input-number v-model="gameData.heroData[scope.row.id].lv" :min="0" :max="999"
                            controls-position="right" style="width: 100%" />
                        </template>
                      </el-table-column>
                      <el-table-column label="碎片数量" width="150" align="center">
                        <template #default="scope">
                          <el-input-number v-model="gameData.heroData[scope.row.id].c" :min="0" :max="999999"
                            controls-position="right" style="width: 100%" />
                        </template>
                      </el-table-column>
                      <el-table-column label="天赋点" width="150" align="center">
                        <template #default="scope">
                          <el-input-number v-model="gameData.heroData[scope.row.id].tPoint" :min="0" :max="999"
                            controls-position="right" style="width: 100%" />
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="120" align="center">
                        <template #default="scope">
                          <el-button type="danger" size="small" @click="handleDeleteHero(scope.row.id)">
                            删除
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>

              <!-- 其他设置 -->
              <el-tab-pane label="其他设置">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="游戏速度">
                        <el-input-number v-model="gameData.timeScale" :min="0.5" :max="2" :step="0.1"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="音乐">
                        <el-switch v-model="gameData.isMMute" active-text="静音" inactive-text="开启" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="音效">
                        <el-switch v-model="gameData.isSMute" active-text="静音" inactive-text="开启" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 在 game-data-section 后添加操作历史部分 -->
          <div v-if="operationHistory.length" class="history-section">
            <div class="section-title">
              <el-icon>
                <Timer />
              </el-icon>
              <span>操作记录</span>
            </div>
            <el-timeline>
              <el-timeline-item v-for="(history, index) in operationHistory.slice(0, 5)" :key="index"
                :type="history.success ? 'success' : 'danger'" :timestamp="history.time" :hollow="true" size="normal">
                {{ history.operation }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, h } from 'vue'
import { ElMessage, ElMessageBox, ElSelect, ElOption } from 'element-plus'
import { KnifeFork, Download, Upload, Document, Timer, Present, Star, Plus } from '@element-plus/icons-vue'

// 表单引用
const formRef = ref(null)

// 表单数据
const form = reactive({
  loginCode: 'oPEgG7ep-9mpeVtvnjv8jXGYQ9wE',
  token: '',
  uid: '',
  lv: 50
})

// 表单验证规则
const rules = {
  loginCode: [
    { required: true, message: '请输入游戏ID', trigger: 'blur' }
  ]
}

// 加载状态
const loading = reactive({
  download: false,
  upload: false,
  package1: false,
  package2: false,
  package3: false
})

// 是否已获取数据
const hasData = ref(false)

// 游戏数据
const gameData = ref(null)

// 计算道具列表
const toolsList = computed(() => {
  if (!gameData.value?.toolData) return []
  return Object.entries(gameData.value.toolData).map(([id]) => ({
    id
  }))
})

// 添加操作历史数组
const operationHistory = ref([])

// 修改字段数组
const modifiedFields = ref([])

// 添加每日任务列表计算属性
const dailyMissionList = computed(() => {
  if (!gameData.value?.dayMissionData) return [];
  return Object.keys(gameData.value.dayMissionData).map(id => ({
    id,
    name: `任务${id}`
  }));
});

// 添加周常任务列表计算属性
const weekMissionList = computed(() => {
  if (!gameData.value?.weekMissionData) return [];
  return Object.keys(gameData.value.weekMissionData).map(id => ({
    id,
    name: `任务${id}`
  }));
});

// 在 script setup 部分添加 heroList 计算属性
const heroList = computed(() => {
  if (!gameData.value?.heroData) return []
  return Object.keys(gameData.value.heroData).map(id => ({
    id
  }))
})

// 添加计算缺失英雄ID的计算属性
const missingHeroIds = computed(() => {
  if (!gameData.value?.heroData) return []
  const existingIds = new Set(Object.keys(gameData.value.heroData))
  const allIds = Array.from({ length: 10 }, (_, i) => String(i + 1))
  return allIds.filter(id => !existingIds.has(id))
})

// 下载数据方法
const handleDownload = async () => {
  if (!form.loginCode.trim()) {
    ElMessage.warning('请输入游戏ID')
    return
  }

  loading.download = true
  try {
    // 第一步：获取token
    const loginResponse = await fetch('/xsapi/rabbit/user/p8loginwow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'xweb_xhr': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555'
      },
      body: JSON.stringify({
        loginCode: form.loginCode.trim()
      })
    })

    if (!loginResponse.ok) {
      throw new Error('获取token失败')
    }

    const loginData = await loginResponse.json()
    console.log('登录响应数据:', loginData)
    if (!loginData.ok || !loginData.token) {
      throw new Error('获取token失败')
    }

    // 保存token和uid
    form.token = loginData.token
    form.uid = loginData.uid

    // 第二步：获取游戏数据
    const gameResponse = await fetch('/xsapi/rabbit/game/infowow', {
      headers: {
        'uToken': form.token,
        'uid': form.uid,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555'
      }
    })

    if (!gameResponse.ok) {
      throw new Error('获取游戏数据失败')
    }

    const gameResult = await gameResponse.json()
    console.log('游戏数据响应:', gameResult)
    if (!gameResult.ok || !gameResult.baseData) {
      throw new Error('获取游戏数据失败')
    }

    // 保存原始数据格式
    const originalData = JSON.parse(gameResult.baseData)
    console.log('解析后的游戏数据:', originalData)
    // 确保数据类型一致
    gameData.value = {
      ...originalData,
      itemData: {
        ...originalData.itemData,
        '2': parseInt(originalData.itemData['2']) || 0,
        '3': parseInt(originalData.itemData['3']) || 0,
        '50': parseInt(originalData.itemData['50']) || 0
      },
      power: parseInt(originalData.power) || 0,
      level: parseInt(originalData.level) || 1,
      levelWave: parseInt(originalData.levelWave) || 1,
      sweepTimes: parseInt(originalData.sweepTimes) || 0,
      powerBuyTimes: Array.isArray(originalData.powerBuyTimes) ?
        originalData.powerBuyTimes.map(v => parseInt(v) || 0) : [5, 0],
      toolData: Object.entries(originalData.toolData || {}).reduce((acc, [id, data]) => {
        acc[id] = {
          lv: parseInt(data.lv) || 0,
          c: parseInt(data.c) || 0
        }
        return acc
      }, {}),
      heroData: Object.entries(originalData.heroData || {}).reduce((acc, [id, data]) => {
        acc[id] = {
          lv: parseInt(data.lv) || 0,
          c: parseInt(data.c) || 0,
          tPoint: parseInt(data.tPoint) || 0
        }
        return acc
      }, {})
    }

    hasData.value = true
    ElMessage.success('数据下载成功')

    // 添加操作历史
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '下载游戏数据成功',
      success: true
    })
  } catch (error) {
    console.error('下载失败:', error)
    ElMessage.error(error.message || '下载失败')
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `下载失败: ${error.message}`,
      success: false
    })
  } finally {
    loading.download = false
  }
}

// 上传数据方法
const handleUpload = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.upload = true
  try {
    // 打印上传前的数据
    console.log('上传前的游戏数据:', gameData.value)

    // 确保数据类型一致
    const uploadData = {
      ...gameData.value,
      itemData: {
        ...gameData.value.itemData,
        '2': parseInt(gameData.value.itemData['2']) || 0,
        '3': parseInt(gameData.value.itemData['3']) || 0,
        '50': parseInt(gameData.value.itemData['50']) || 0,
        '51': parseInt(gameData.value.itemData['51']) || 0
      },
      power: parseInt(gameData.value.power) || 0,
      level: parseInt(gameData.value.level) || 1,
      levelWave: parseInt(gameData.value.levelWave) || 1,
      sweepTimes: parseInt(gameData.value.sweepTimes) || 0,
      powerBuyTimes: Array.isArray(gameData.value.powerBuyTimes) ?
        gameData.value.powerBuyTimes.map(v => parseInt(v) || 0) : [5, 0],
      toolData: Object.entries(gameData.value.toolData || {}).reduce((acc, [id, data]) => {
        acc[id] = {
          lv: parseInt(data.lv) || 0,
          c: parseInt(data.c) || 0
        }
        return acc
      }, {}),
      heroData: Object.entries(gameData.value.heroData || {}).reduce((acc, [id, data]) => {
        acc[id] = {
          lv: parseInt(data.lv) || 0,
          c: parseInt(data.c) || 0,
          tPoint: parseInt(data.tPoint) || 0
        }
        return acc
      }, {}),
      dataVersion: 35,  // 新增: 数据版本号
      t: Date.now(),    // 新增: 当前时间戳
      f: 1,             // 新增: 标记位
    }

    // 打印处理后的数据
    console.log('处理后的上传数据:', uploadData)

    // 构建完整的请求体
    const requestBody = {
      lv: parseInt(form.lv) || 50,  // 使用表单中的 lv 值
      info: JSON.stringify(uploadData)
    }

    console.log('最终请求体:', requestBody)

    // 发送上传请求
    const response = await fetch('/xsapi/rabbit/game/setbaseinfowow', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'uToken': form.token,
        'uid': form.uid,
        'xweb_xhr': '1',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
        'Referer': 'https://servicewechat.com/wx7065d302cdfa0f48/23/page-frame.html',
        'Accept': '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9'
      },
      body: JSON.stringify(requestBody)
    })

    console.log('服务器响应状态:', response.status)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('上传失败响应:', errorText)
      throw new Error(`上传失败: ${response.status} - ${errorText}`)
    }

    const result = await response.json()
    console.log('服务器返回数据:', result)

    if (!result.ok) {
      throw new Error(result.message || '上传数据失败')
    }

    ElMessage.success('数据上传成功')
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '更新游戏数据成功',
      success: true,
      details: `版本: ${uploadData.dataVersion}, 等级: ${uploadData.level}`  // 新增更多信息
    })
  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error(error.message || '上传失败')
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `更新失败: ${error.message}`,
      success: false,
      details: error.stack  // 新增错误堆栈信息
    })
  } finally {
    loading.upload = false
  }
}

// 套餐1处理函数
const handlePackage1 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package1 = true
  try {
    // 设置无限值
    gameData.value.itemData['2'] = 999999999  // 金币
    gameData.value.itemData['3'] = 999999999  // 钻石
    gameData.value.power = 999999999          // 体力

    // 记录修改的字段（不会自动清除）
    modifiedFields.value = ['gold', 'diamond', 'power']

    // 自动上传数据
    await handleUpload()

    // 显示成功提示
    ElMessage({
      type: 'success',
      message: '套餐1应用成功：金币/钻石/体力已修改为无限',
      duration: 3000,
      showClose: true
    })

    // 添加操作历史
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '套餐1：无限金币/钻石/体力',
      success: true
    })
  } catch (error) {
    console.error('套餐1应用失败:', error)
    ElMessage.error(error.message || '套餐1应用失败')

    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `套餐1应用失败: ${error.message}`,
      success: false
    })
  } finally {
    loading.package1 = false
  }
}

// 套餐2处理函数
const handlePackage2 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package2 = true
  try {
    // 设置无限值
    gameData.value.itemData['2'] = 999999999  // 金币
    gameData.value.itemData['3'] = 999999999  // 钻石
    gameData.value.power = 999999999          // 体力

    // 只修改已解锁英雄（1-9）
    gameData.value.unlockHeros = Array.from({ length: 10 }, (_, i) => i + 1)
    // 不修改拥有的英雄
    // gameData.value.arrayHeros = 保持不变

    // 设置所有装备碎片数量为999999
    if (gameData.value.toolData) {
      Object.keys(gameData.value.toolData).forEach(id => {
        gameData.value.toolData[id].c = 999999
      })
    }

    // 记录修改的字段
    modifiedFields.value = [
      'gold',
      'diamond',
      'power',
      'unlockHeros', // 只记录已解锁英雄
      ...Object.keys(gameData.value.toolData || {}).map(id => `tool_${id}`)
    ]

    // 自动上传数据
    await handleUpload()

    // 显示成功提示
    ElMessage({
      type: 'success',
      message: '套餐2应用成功：无限资源+已解锁英雄+全碎片',
      duration: 3000,
      showClose: true
    })

    // 添加操作历史
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '套餐2：无限资源+已解锁英雄+全碎片',
      success: true
    })
  } catch (error) {
    console.error('套餐2应用失败:', error)
    ElMessage.error(error.message || '套餐2应用失败')

    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `套餐2应用失败: ${error.message}`,
      success: false
    })
  } finally {
    loading.package2 = false
  }
}

// 添加套餐3处理函数
const handlePackage3 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package3 = true
  try {
    // 设置无限值
    gameData.value.itemData['2'] = 999999999  // 金币
    gameData.value.itemData['3'] = 999999999  // 钻石
    gameData.value.power = 999999999          // 体力

    // 解锁所有英雄（1-9）
    gameData.value.unlockHeros = Array.from({ length: 10 }, (_, i) => i + 1)

    // 设置所有装备碎片数量为999999
    if (gameData.value.toolData) {
      Object.keys(gameData.value.toolData).forEach(id => {
        gameData.value.toolData[id].c = 999999
      })
    }

    // 设置所有英雄碎片为9999
    if (gameData.value.heroData) {
      Object.keys(gameData.value.heroData).forEach(id => {
        gameData.value.heroData[id].c = 9999
      })
    }

    // 记录修改的字段
    modifiedFields.value = [
      'gold',
      'diamond',
      'power',
      'unlockHeros',
      ...Object.keys(gameData.value.toolData || {}).map(id => `tool_${id}`),
      ...Object.keys(gameData.value.heroData || {}).map(id => `hero_${id}`)
    ]

    // 自动上传数据
    await handleUpload()

    // 显示成功提示
    ElMessage({
      type: 'success',
      message: '套餐3应用成功：无限资源+已解锁英雄+全碎片+英雄碎片9999',
      duration: 3000,
      showClose: true
    })

    // 添加操作历史
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '套餐3：无限资源+已解锁英雄+全碎片+英雄碎片9999',
      success: true
    })
  } catch (error) {
    console.error('套餐3应用失败:', error)
    ElMessage.error(error.message || '套餐3应用失败')

    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `套餐3应用失败: ${error.message}`,
      success: false
    })
  } finally {
    loading.package3 = false
  }
}

// 修改添加英雄处理函数为一键添加所有缺少的英雄
const handleAddAllMissingHeroes = () => {
  if (!missingHeroIds.value.length) {
    ElMessage.warning('已添加所有英雄（1-10）')
    return
  }

  // 批量添加所有缺少的英雄
  missingHeroIds.value.forEach(heroId => {
    // 添加英雄数据
    gameData.value.heroData[heroId] = {
      lv: 1,      // 初始等级
      c: 9999,    // 初始碎片数量设置为9999
      tPoint: 0   // 初始天赋点
    }
  })

  ElMessage.success(`成功添加${missingHeroIds.value.length}个英雄`)

  // 添加操作历史
  operationHistory.value.unshift({
    time: new Date().toLocaleString(),
    operation: `批量添加英雄：${missingHeroIds.value.join(', ')}`,
    success: true
  })
}

// 添加删除英雄的处理函数
const handleDeleteHero = (heroId) => {
  ElMessageBox.confirm(
    `确定要删除英雄${heroId}吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 删除英雄数据
    delete gameData.value.heroData[heroId]

    // 添加操作历史
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `删除英雄：${heroId}`,
      success: true
    })

    ElMessage.success(`英雄${heroId}删除成功`)
  }).catch(() => {
    // 用户取消操作
  })
}

// 添加生命周期钩子
onMounted(() => {
  // 重写 ResizeObserver 构造函数
  const OriginalResizeObserver = window.ResizeObserver;
  window.ResizeObserver = class ResizeObserver extends OriginalResizeObserver {
    constructor(callback) {
      super((entries, observer) => {
        // 使用 requestAnimationFrame 包装回调
        window.requestAnimationFrame(() => {
          try {
            callback(entries, observer);
          } catch (e) {
            // 忽略 ResizeObserver 相关错误
            if (!e.message.includes('ResizeObserver')) {
              console.error(e);
            }
          }
        });
      });
    }
  };

  // 添加全局错误处理
  const errorHandler = (event) => {
    if (event?.message?.includes('ResizeObserver') ||
      event?.error?.message?.includes('ResizeObserver')) {
      event.preventDefault();
      event.stopPropagation();
      return false;
    }
  };

  // 添加错误监听
  window.addEventListener('error', errorHandler, true);
  window.addEventListener('unhandledrejection', errorHandler, true);

  // 重写 console.error 来过滤掉 ResizeObserver 错误
  const originalConsoleError = console.error;
  console.error = (...args) => {
    if (args.some(arg =>
      String(arg).includes('ResizeObserver') ||
      (arg instanceof Error && arg.message.includes('ResizeObserver'))
    )) {
      return;
    }
    originalConsoleError.apply(console, args);
  };

  // 组件卸载时清理
  onUnmounted(() => {
    window.removeEventListener('error', errorHandler, true);
    window.removeEventListener('unhandledrejection', errorHandler, true);
    window.ResizeObserver = OriginalResizeObserver;
    console.error = originalConsoleError;
  });
});
</script>

<style scoped>
.cant-beat-me {
  padding: 20px;
  height: 100vh;
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
  box-sizing: border-box;
}

.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  height: auto;
  min-height: calc(100vh - 40px);
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: -20px -20px 24px -20px;
  background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 20px;
  font-weight: 600;
  color: white;
  z-index: 1;
}

.title .el-icon {
  font-size: 24px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px;
  border-radius: 8px;
  backdrop-filter: blur(4px);
}

.status-tag {
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  padding: 0 12px;
  height: 28px;
  line-height: 28px;
  font-weight: 500;
  z-index: 1;
}

.input-section {
  margin-bottom: 24px;
  padding: 0 20px;
}

.action-section {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
  padding: 0 20px;
  flex-wrap: wrap;
  /* 允许按钮换行 */
}

.action-button {
  flex: 1;
  min-width: 120px;
  max-width: 180px;
  /* 稍微减小最大宽度以适应更多按钮 */
}

.game-data-section {
  margin-top: 24px;
  padding: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-tabs__content) {
  padding: 20px;
}

.el-table {
  margin-top: 16px;
}

.history-section {
  margin-top: 24px;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

:deep(.el-timeline) {
  padding: 16px;
}

:deep(.el-timeline-item__node) {
  background-color: transparent;
}

:deep(.el-timeline-item__timestamp) {
  color: var(--el-text-color-secondary);
  font-size: 12px;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .cant-beat-me {
    padding: 12px;
  }

  .action-section {
    flex-direction: column;
    gap: 8px;
  }

  .action-button {
    max-width: none;
    width: 100%;
  }

  .input-section :deep(.el-input) {
    width: 100% !important;
  }

  .history-section {
    padding: 16px;
  }

  :deep(.el-timeline) {
    padding: 8px;
  }
}

.modified-item {
  position: relative;
}

.modified-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: 2px solid #f56c6c;
  border-radius: 4px;
  pointer-events: none;
}

:deep(.modified-item .el-input-number__wrapper) {
  background-color: rgba(245, 108, 108, 0.1);
  border-color: #f56c6c;
}

:deep(.modified-item .el-form-item__label) {
  color: #f56c6c;
}

/* 添加防止布局抖动的样式 */
.el-table {
  width: 100% !important;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto !important;
  height: auto !important;
}

:deep(.el-table__inner-wrapper) {
  height: auto !important;
}

/* 确保表格内容稳定 */
:deep(.el-table__body) {
  width: 100% !important;
}

/* 移除可能导致问题的样式 */
:deep(.el-table),
:deep(.el-table__header-wrapper),
:deep(.el-table__fixed),
:deep(.el-table__fixed-right) {
  height: auto !important;
  position: static !important;
}

/* 表格样式优化 */
:deep(.el-table) {
  width: 100% !important;
  border-radius: 4px;
  overflow: hidden;
}

:deep(.el-table__header) {
  width: 100% !important;
}

:deep(.el-table__body) {
  width: 100% !important;
}

:deep(.el-table__header-wrapper),
:deep(.el-table__body-wrapper) {
  width: 100% !important;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}

:deep(.el-input-number.is-controls-right .el-input__wrapper) {
  padding-left: 8px;
  padding-right: 8px;
}

/* 确保表内容垂直居中 */
:deep(.el-table .el-table__cell) {
  vertical-align: middle;
}

.version-control :deep(.el-input-number) {
  width: 100%;
}

.version-control :deep(.el-input-number .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c !important;
  border: 2px solid #f56c6c !important;
  border-radius: 4px;
}

.version-control :deep(.el-input-number:hover .el-input__wrapper) {
  box-shadow: 0 0 0 1px #f56c6c !important;
  border: 2px solid #f56c6c !important;
}

.version-control :deep(.el-input-number.is-focus .el-input__wrapper),
.version-control :deep(.el-input-number .el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #f56c6c !important;
  border: 2px solid #f56c6c !important;
}

.version-control :deep(.el-form-item__label) {
  color: #f56c6c !important;
  font-weight: bold;
}

.version-control :deep(.el-input-number__decrease),
.version-control :deep(.el-input-number__increase) {
  border-color: #f56c6c !important;
  background: white;
}

.version-control :deep(.el-input-number__decrease:hover),
.version-control :deep(.el-input-number__increase:hover) {
  color: #f56c6c !important;
}

.version-control :deep(.el-input-number.is-disabled .el-input__wrapper) {
  box-shadow: 0 0 0 1px #fab6b6 !important;
  border: 2px solid #fab6b6 !important;
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
  .el-row {
    margin: 0 !important;
  }

  .el-col {
    padding: 0 !important;
  }

  .version-control {
    margin-top: 12px;
  }
}
</style>