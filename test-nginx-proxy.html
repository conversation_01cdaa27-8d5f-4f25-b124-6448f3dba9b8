<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>nginx代理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .loading {
            color: #6c757d;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
        .url-display {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 5px;
            border-radius: 3px;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>nginx代理配置测试</h1>
        
        <div class="test-item info">
            <h3>测试说明</h3>
            <p>这个页面用于测试nginx代理配置是否正确工作，特别是"根本打不过"游戏的代理设置。</p>
        </div>

        <div class="test-item">
            <h3>1. 测试nginx配置状态</h3>
            <button onclick="testNginxStatus()">测试nginx配置</button>
            <div id="nginxStatusResult"></div>
        </div>

        <div class="test-item">
            <h3>2. 测试"看谁能打过"代理（对比）</h3>
            <button onclick="testWhoBeatMe()">测试看谁能打过</button>
            <div id="whoBeatMeResult"></div>
        </div>

        <div class="test-item">
            <h3>3. 测试"根本打不过"代理</h3>
            <button onclick="testCantBeatMe()">测试根本打不过</button>
            <div id="cantBeatMeResult"></div>
        </div>

        <div class="test-item">
            <h3>4. 对比测试结果</h3>
            <button onclick="runComparison()">运行对比测试</button>
            <div id="comparisonResult"></div>
        </div>
    </div>

    <script>
        // 测试nginx配置状态
        async function testNginxStatus() {
            const resultDiv = document.getElementById('nginxStatusResult');
            resultDiv.innerHTML = '<div class="loading">正在测试nginx配置...</div>';
            
            try {
                const url = '/cant-beat-me/test';
                resultDiv.innerHTML += `<div class="info"><strong>测试URL:</strong> <div class="url-display">${url}</div></div>`;
                
                const response = await fetch(url);
                const responseText = await response.text();
                
                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        resultDiv.innerHTML += `
                            <div class="success">
                                <strong>✅ nginx配置正常</strong><br>
                                状态: ${data.status}<br>
                                消息: ${data.message}<br>
                                时间: ${data.timestamp}
                            </div>
                        `;
                    } catch (parseError) {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>❌ 响应格式错误</strong><br>
                                无法解析JSON: ${parseError.message}<br>
                                响应内容: ${responseText}
                            </div>
                        `;
                    }
                } else {
                    resultDiv.innerHTML += `
                        <div class="error">
                            <strong>❌ nginx配置测试失败</strong><br>
                            状态码: ${response.status}<br>
                            响应: ${responseText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="error">
                        <strong>❌ 网络请求失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试"看谁能打过"代理
        async function testWhoBeatMe() {
            const resultDiv = document.getElementById('whoBeatMeResult');
            resultDiv.innerHTML = '<div class="loading">正在测试看谁能打过代理...</div>';
            
            try {
                const url = '/who-beat-me/download/68173379_Yp_Default.json?timestamp=' + Date.now();
                resultDiv.innerHTML += `<div class="info"><strong>测试URL:</strong> <div class="url-display">${url}</div></div>`;
                
                const response = await fetch(url);
                const contentType = response.headers.get('content-type');
                
                if (response.ok) {
                    const responseText = await response.text();
                    
                    if (responseText.trim().startsWith('<!') || responseText.trim().startsWith('<html')) {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>❌ 收到HTML响应</strong><br>
                                代理可能没有正确工作
                            </div>
                        `;
                    } else {
                        try {
                            const data = JSON.parse(responseText);
                            resultDiv.innerHTML += `
                                <div class="success">
                                    <strong>✅ 看谁能打过代理正常</strong><br>
                                    状态码: ${response.status}<br>
                                    内容类型: ${contentType}<br>
                                    数据大小: ${responseText.length} 字符
                                </div>
                            `;
                        } catch (parseError) {
                            resultDiv.innerHTML += `
                                <div class="error">
                                    <strong>❌ JSON解析失败</strong><br>
                                    错误: ${parseError.message}
                                </div>
                            `;
                        }
                    }
                } else {
                    resultDiv.innerHTML += `
                        <div class="error">
                            <strong>❌ 看谁能打过代理失败</strong><br>
                            状态码: ${response.status}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="error">
                        <strong>❌ 请求失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 测试"根本打不过"代理
        async function testCantBeatMe() {
            const resultDiv = document.getElementById('cantBeatMeResult');
            resultDiv.innerHTML = '<div class="loading">正在测试根本打不过代理...</div>';
            
            try {
                const url = '/cant-beat-me/download/68819796_Yp_Default.json?timestamp=' + Date.now();
                resultDiv.innerHTML += `<div class="info"><strong>测试URL:</strong> <div class="url-display">${url}</div></div>`;
                
                const response = await fetch(url);
                const contentType = response.headers.get('content-type');
                
                // 检查调试头
                const debugBackend = response.headers.get('x-debug-backend');
                const debugFilename = response.headers.get('x-debug-filename');
                const debugFullURL = response.headers.get('x-debug-full-url');
                
                if (debugBackend || debugFilename || debugFullURL) {
                    resultDiv.innerHTML += `
                        <div class="info">
                            <strong>🔍 调试信息:</strong><br>
                            Backend: ${debugBackend || '未设置'}<br>
                            Filename: ${debugFilename || '未设置'}<br>
                            Full URL: ${debugFullURL || '未设置'}
                        </div>
                    `;
                }
                
                if (response.ok) {
                    const responseText = await response.text();
                    
                    if (responseText.trim().startsWith('<!') || responseText.trim().startsWith('<html')) {
                        resultDiv.innerHTML += `
                            <div class="error">
                                <strong>❌ 收到HTML响应</strong><br>
                                代理配置可能有问题，请求被重定向到错误页面<br>
                                <details>
                                    <summary>查看HTML内容</summary>
                                    <pre>${responseText.substring(0, 500)}...</pre>
                                </details>
                            </div>
                        `;
                    } else {
                        try {
                            const data = JSON.parse(responseText);
                            resultDiv.innerHTML += `
                                <div class="success">
                                    <strong>✅ 根本打不过代理正常</strong><br>
                                    状态码: ${response.status}<br>
                                    内容类型: ${contentType}<br>
                                    数据大小: ${responseText.length} 字符
                                </div>
                            `;
                        } catch (parseError) {
                            resultDiv.innerHTML += `
                                <div class="error">
                                    <strong>❌ JSON解析失败</strong><br>
                                    错误: ${parseError.message}<br>
                                    <details>
                                        <summary>查看响应内容</summary>
                                        <pre>${responseText.substring(0, 500)}...</pre>
                                    </details>
                                </div>
                            `;
                        }
                    }
                } else {
                    const responseText = await response.text();
                    resultDiv.innerHTML += `
                        <div class="error">
                            <strong>❌ 根本打不过代理失败</strong><br>
                            状态码: ${response.status}<br>
                            响应: ${responseText}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="error">
                        <strong>❌ 请求失败</strong><br>
                        错误: ${error.message}
                    </div>
                `;
            }
        }

        // 运行对比测试
        async function runComparison() {
            const resultDiv = document.getElementById('comparisonResult');
            resultDiv.innerHTML = '<div class="loading">正在运行对比测试...</div>';
            
            const tests = [
                { name: '看谁能打过', url: '/who-beat-me/download/68173379_Yp_Default.json?timestamp=' + Date.now() },
                { name: '根本打不过', url: '/cant-beat-me/download/68819796_Yp_Default.json?timestamp=' + Date.now() }
            ];
            
            let comparisonHtml = '<div class="info"><h4>📊 对比测试结果</h4></div>';
            
            for (const test of tests) {
                try {
                    const response = await fetch(test.url);
                    const contentType = response.headers.get('content-type');
                    const responseText = await response.text();
                    
                    const isHTML = responseText.trim().startsWith('<!') || responseText.trim().startsWith('<html');
                    const isJSON = !isHTML && (() => {
                        try {
                            JSON.parse(responseText);
                            return true;
                        } catch {
                            return false;
                        }
                    })();
                    
                    const statusClass = response.ok && isJSON ? 'success' : 'error';
                    const statusIcon = response.ok && isJSON ? '✅' : '❌';
                    
                    comparisonHtml += `
                        <div class="${statusClass}">
                            <strong>${statusIcon} ${test.name}</strong><br>
                            状态码: ${response.status}<br>
                            内容类型: ${contentType || '未知'}<br>
                            响应类型: ${isHTML ? 'HTML' : isJSON ? 'JSON' : '其他'}<br>
                            数据大小: ${responseText.length} 字符
                        </div>
                    `;
                } catch (error) {
                    comparisonHtml += `
                        <div class="error">
                            <strong>❌ ${test.name}</strong><br>
                            错误: ${error.message}
                        </div>
                    `;
                }
            }
            
            resultDiv.innerHTML = comparisonHtml;
        }
    </script>
</body>
</html>
