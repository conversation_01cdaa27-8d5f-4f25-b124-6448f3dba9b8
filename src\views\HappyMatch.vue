<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="happy-match">
      <el-card class="game-card">
        <!-- 头部标题 -->
        <div class="card-header-wrapper">
          <div class="card-header">
            <el-icon class="header-icon"><Sugar /></el-icon>
            <span>消除游戏数据修改</span>
          </div>
        </div>

        <div class="card-content">
          <!-- 游戏选择 -->
          <div class="game-select-section">
            <div class="input-group">
              <div class="input-label">游戏选择:</div>
              <el-select 
                v-model="selectedGame" 
                placeholder="请选择游戏"
                size="large"
                class="game-select"
              >
                <el-option label="开心点点消" value="happy" />
                <el-option label="甜甜爱消除" value="sweet" />
              </el-select>
            </div>
          </div>

          <!-- OpenID 输入框和按钮组 -->
          <div class="openid-section">
            <div class="input-group">
              <div class="input-label">OpenID:</div>
              <el-input 
                v-model="form.openId" 
                placeholder="请输入游戏用户OpenID"
                size="large"
                clearable
                class="openid-input"
              >
                <template #prefix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </div>
            
            <div class="input-group">
              <div class="input-label">Token:</div>
              <el-input 
                v-model="form.userToken" 
                placeholder="请输入用户Token"
                size="large"
                clearable
                class="token-input"
              >
                <template #prefix>
                  <el-icon><Key /></el-icon>
                </template>
              </el-input>
            </div>
            
            <div class="button-group">
              <el-button 
                type="primary" 
                @click="handleDownload" 
                :loading="loading.download"
                :icon="Download"
                class="action-button"
              >下载数据</el-button>
              <el-button 
                type="success" 
                @click="handleUpload" 
                :loading="loading.upload"
                :icon="Upload"
                class="action-button"
                :disabled="!hasData"
              >上传数据</el-button>
              <el-tooltip content="体力瓶/星星瓶/钻石瓶各3万" placement="top">
                <el-button
                  type="warning"
                  @click="handlePackage1"
                  :loading="loading.package1"
                  :icon="Present"
                  class="action-button"
                  :disabled="!hasData"
                >套餐1</el-button>
              </el-tooltip>
              <el-tooltip content="体力瓶/星星瓶/钻石瓶各6万" placement="top">
                <el-button
                  type="info"
                  @click="handlePackage2"
                  :loading="loading.package2"
                  :icon="Present"
                  class="action-button"
                  :disabled="!hasData"
                >套餐2</el-button>
              </el-tooltip>
              <el-tooltip content="体力瓶/星星瓶/钻石瓶各10万" placement="top">
                <el-button
                  type="danger"
                  @click="handlePackage3"
                  :loading="loading.package3"
                  :icon="Present"
                  class="action-button"
                  :disabled="!hasData"
                >套餐3</el-button>
              </el-tooltip>
              <el-tooltip content="体力瓶/星星瓶/钻石瓶各50万" placement="top">
                <el-button
                  type="success"
                  @click="handlePackage4"
                  :loading="loading.package4"
                  :icon="Present"
                  class="action-button"
                  :disabled="!hasData"
                >套餐4</el-button>
              </el-tooltip>
            </div>
          </div>

          <!-- 修改资源修改区域部分 -->
          <div v-if="hasData" class="resources-grid">
            <div class="resource-cards">
              <!-- 瓶子数量卡片 -->
              <div class="resource-section">
                <div class="section-title">
                  <div class="title-content">
                    <el-icon><Present /></el-icon>
                    <span>瓶子道具</span>
                  </div>
                </div>
                <div class="resource-inputs">
                  <!-- 瓶子数量部分 -->
                  <el-form-item label="体力瓶">
                    <el-input-number 
                      v-model="form.energyBottle" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.energyBottle }"
                    />
                    <div class="item-info">
                      <span class="item-id">ID: 4405</span>
                      <span class="bottle-count">
                        (约 {{ Math.ceil(form.energyBottle / 100) }} 瓶，每瓶100体力)
                        <span class="existing-bottles" v-if="existingBottles.energy > 0">
                          当前已有 {{ existingBottles.energy }} 瓶 ({{ existingBottles.energy * 100 }}体力)
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                  <el-form-item label="星星瓶">
                    <el-input-number 
                      v-model="form.starBottle" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.starBottle }"
                    />
                    <div class="item-info">
                      <span class="item-id">ID: 5405</span>
                      <span class="bottle-count">
                        (约 {{ Math.ceil(form.starBottle / 300) }} 瓶，每瓶300星星)
                        <span class="existing-bottles" v-if="existingBottles.star > 0">
                          当前已有 {{ existingBottles.star }} 瓶 ({{ existingBottles.star * 300 }}星星)
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                  <el-form-item label="钻石瓶">
                    <el-input-number 
                      v-model="form.diamondBottle" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.diamondBottle }"
                    />
                    <div class="item-info">
                      <span class="item-id">ID: 4904</span>
                      <span class="bottle-count">
                        (约 {{ Math.ceil(form.diamondBottle / 20) }} 瓶，每瓶20钻石)
                        <span class="existing-bottles" v-if="existingBottles.diamond > 0">
                          当前已有 {{ existingBottles.diamond }} 瓶 ({{ existingBottles.diamond * 20 }}钻石)
                        </span>
                      </span>
                    </div>
                  </el-form-item>
                </div>
              </div>

              <!-- 新增特殊道具卡片 -->
              <div class="resource-section">
                <div class="section-title">
                  <div class="title-content">
                    <el-icon><Collection /></el-icon>
                    <span>特殊道具</span>
                  </div>
                </div>
                <div class="resource-inputs">
                  <el-form-item label="广告券">
                    <el-input-number 
                      v-model="form.adCard" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.adCard }"
                    />
                    <div class="item-info">
                      <span class="item-id">广告跳过券</span>
                    </div>
                  </el-form-item>
                  <el-form-item label="仓库格子">
                    <el-input-number 
                      v-model="form.boxIndex" 
                      :min="0" 
                      controls-position="right"
                      :class="{ 'modified-input': modifiedFields.boxIndex }"
                    />
                    <div class="item-info">
                      <span class="item-id">背包扩展格子</span>
                    </div>
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>

          <!-- 未下载数据时的提示 -->
          <el-empty 
            v-else
            description="请先下载数据" 
            :image-size="200"
          >
            <template #description>
              <p class="empty-text">请输入OpenID并下载数据以查看和修改游戏资源</p>
            </template>
          </el-empty>
        </div>
      </el-card>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'
import CryptoJS from 'crypto-js'
import { 
  Sugar,
  User, 
  Money, 
  Present,
  Download,
  Upload,
  Key,
  Tickets,
  Collection,
  InfoFilled
} from '@element-plus/icons-vue'

// 导入自定义的 SHA1 实现
import sha1 from '@/utils/sha1'

// 添加游戏选择
const selectedGame = ref('happy')

// 修改表单数据
const form = reactive({
  openId: '',
  userToken: '',
  adCard: 0,
  boxIndex: 0,
  energyBottle: 0,
  starBottle: 0,
  diamondBottle: 0
})

// 计算当前选择的游戏API路径
const apiPath = computed(() => {
  return selectedGame.value === 'happy' ? '/happy-match' : '/sweet-match'
})

// 添加游戏ID的计算属性
const gameId = computed(() => {
  return selectedGame.value === 'happy' ? '1000118' : '1000129'
})

// 加载状态
const loading = reactive({
  download: false,
  upload: false,
  package1: false,
  package2: false,
  package3: false,
  package4: false,
  addCard: false
})

// 是否有数据标志
const hasData = ref(false)

// 修改字段标记
const modifiedFields = reactive({
  adCard: false,
  boxIndex: false,
  energyBottle: false,
  starBottle: false,
  diamondBottle: false
})

// 添加一个变量来保存原始游戏数据
const originalGameData = ref(null)

// 添加现有瓶子数量的响应式对象
const existingBottles = reactive({
  energy: 0,
  star: 0,
  diamond: 0
})

// 修改生成签名的函数
const generateSignature = (openid, token, value = '', isUpload = false) => {
  if (isUpload) {
    // 上传数据时的签名
    const params = {
      field: 'base_data',
      game_id: gameId.value,  // 使用动态游戏ID
      isReset: 0,
      log_list: '[{"c":101501,"s":"","u":"1731048408873_1","r":[[4201,1]]}]',
      phone_model: 'android',
      token: token,
      value: value,
      version: '45.0'
    }
    
    // 按键名排序
    const sortedParams = {}
    Object.keys(params).sort().forEach(key => {
      sortedParams[key] = params[key]
    })
    
    // 构建��询字符串
    const queryStr = Object.entries(sortedParams)
      .map(([key, val]) => `${key}=${val}`)
      .join('&')
    
    // 加密钥
    const signStr = queryStr + '&QZZWI88ZGUxYOVk66hOAGiYTQ2YzMB3CZW1FlND'
    
    return sha1(signStr)
  } else {
    // 下载数据时的签名
    const signStr = `field=base_data&game_id=${gameId.value}&openid=${openid}&token=${token}&version=45.0&QZZWI88ZGUxYOVk66hOAGiYTQ2YzMB3CZW1FlND`
    return sha1(signStr)
  }
}

// 修改下载数据处理函数
const handleDownload = async () => {
  if (!form.openId || !form.userToken) {
    ElMessage.warning('请输入OpenID和用户Token')
    return
  }

  loading.download = true
  try {
    const signature = generateSignature(form.openId, form.userToken)
    
    const deviceInfo = {
      wx_version: "3.9.9",
      system: "Windows 11 x64",
      platform: "windows",
      sdk: "3.3.5",
      model: "microsoft",
      brand: "microsoft",
      net_type: "wifi"
    }

    const params = {
      field: 'base_data',
      token: form.userToken,
      version: '70.0',  // 更新版本号
      game_id: gameId.value,
      signature: signature,
      device_info: JSON.stringify(deviceInfo)
    }

    console.log('请求参数:', params)  // 添加日志

    const response = await axios({
      method: 'post',
      url: `${apiPath.value}/getData`,
      data: params,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'xweb_xhr': '1',
        'sec-fetch-site': 'cross-site',
        'sec-fetch-mode': 'cors',
        'sec-fetch-dest': 'empty',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555'
      },
      transformRequest: [(data) => {
        return Object.entries(data)
          .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
          .join('&');
      }]
    })

    if (response.data && response.data.status === true && response.data.result && response.data.result.code === 0) {
      originalGameData.value = response.data.result.data
      const gameData = parseGameData(response.data.result.data)
      
      form.adCard = gameData.userInfo['ad_card_data']?.num || 0
      form.boxIndex = gameData.userInfo.boxindex || 0
      form.energyBottle = gameData.userInfo['lv_data']?.treasures.filter(id => id === 4405).length || 0
      form.starBottle = gameData.userInfo['lv_data']?.treasures.filter(id => id === 5405).length || 0
      form.diamondBottle = gameData.userInfo['lv_data']?.treasures.filter(id => id === 4904).length || 0

      hasData.value = true
      ElMessage.success('数据下载成功')
    } else {
      throw new Error(response.data?.result?.msg || '下载失败，服务器返回错误')
    }
  } catch (error) {
    console.error('下载数据失败:', error)
    ElMessage.error(error.message || '数据下载失败')
  } finally {
    loading.download = false
  }
}

// 修改上传数据处理函数
const handleUpload = async () => {
  if (!hasData.value || !originalGameData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.upload = true
  try {
    const gameData = JSON.parse(originalGameData.value.base_data)
    
    gameData.userInfo['ad_card_data'] = { num: form.adCard }
    gameData.userInfo.boxindex = form.boxIndex
    
    const additionalBottles = {
      energy: Math.ceil(form.energyBottle / 100),
      star: Math.ceil(form.starBottle / 300),
      diamond: Math.ceil(form.diamondBottle / 20)
    }
    
    const treasures = [
      ...gameData.userInfo['lv_data'].treasures,
      ...Array(additionalBottles.energy).fill(4405),
      ...Array(additionalBottles.star).fill(5405),
      ...Array(additionalBottles.diamond).fill(4904)
    ]
    
    gameData.userInfo['lv_data'].treasures = treasures

    const gameDataStr = JSON.stringify(gameData)
    
    const params = {
      field: 'base_data',
      value: gameDataStr,
      phone_model: 'android',
      log_list: '[{"c":101501,"s":"","u":"1731048408873_1","r":[[4201,1]]}]',
      isReset: 0,
      token: form.userToken,
      version: '45.0',
      game_id: gameId.value,  // 使用动态游戏ID
      device_info: JSON.stringify({
        wx_version: "3.9.9",
        system: "Windows 11 x64",
        platform: "windows",
        sdk: "3.3.5",
        model: "microsoft",
        brand: "microsoft",
        net_type: "wifi"
      })
    }
    
    const signature = generateSignature(form.openId, form.userToken, gameDataStr, true)
    params.signature = signature

    const response = await axios({
      method: 'post',
      url: `${apiPath.value}/saveData`,
      data: params,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8',
        'Accept': 'application/json, text/plain, */*',
        'xweb_xhr': '1'
      },
      transformRequest: [(data) => {
        const formData = new URLSearchParams();
        for (const [key, value] of Object.entries(data)) {
          formData.append(key, value);
        }
        return formData.toString();
      }]
    })

    if (response.data && response.data.status === true && response.data.result && response.data.result.code === 0) {
      ElMessage.success('数据上传成功')
      Object.keys(modifiedFields).forEach(key => {
        modifiedFields[key] = false
      })
    } else {
      throw new Error(response.data?.result?.msg || '上传失败')
    }
  } catch (error) {
    console.error('上传数据失败:', error)
    ElMessage.error(error.message || '数据上传失败')
  } finally {
    loading.upload = false
  }
}

// 修改套餐1处理函数
const handlePackage1 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package1 = true
  try {
    const updates = {
      energyBottle: 30000,  // 3万
      starBottle: 30000,    // 3万
      diamondBottle: 30000  // 3万
    }

    Object.entries(updates).forEach(([field, value]) => {
      form[field] = value
      modifiedFields[field] = true
    })

    await handleUpload()
    ElMessage.success('套餐1应用成功')
  } catch (error) {
    console.error('套餐1应用失败:', error)
    ElMessage.error(error.message || '套餐1应用失败')
  } finally {
    loading.package1 = false
  }
}

// 修改套餐2处理函数
const handlePackage2 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package2 = true
  try {
    const updates = {
      energyBottle: 60000,  // 6万
      starBottle: 60000,    // 6万
      diamondBottle: 60000  // 6万
    }

    Object.entries(updates).forEach(([field, value]) => {
      form[field] = value
      modifiedFields[field] = true
    })

    await handleUpload()
    ElMessage.success('套餐2应用成功')
  } catch (error) {
    console.error('套餐2应用失败:', error)
    ElMessage.error(error.message || '��餐2应用失败')
  } finally {
    loading.package2 = false
  }
}

// 修改套餐3处理函数
const handlePackage3 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package3 = true
  try {
    const updates = {
      energyBottle: 100000,  // 10万
      starBottle: 100000,    // 10万
      diamondBottle: 100000  // 10万
    }

    Object.entries(updates).forEach(([field, value]) => {
      form[field] = value
      modifiedFields[field] = true
    })

    await handleUpload()
    ElMessage.success('套餐3应用成功')
  } catch (error) {
    console.error('套餐3应用失败:', error)
    ElMessage.error(error.message || '套餐3应用失败')
  } finally {
    loading.package3 = false
  }
}

// 修改套餐4处理函数
const handlePackage4 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先下载数据')
    return
  }

  loading.package4 = true
  try {
    const updates = {
      energyBottle: 500000,  // 50万
      starBottle: 500000,    // 50万
      diamondBottle: 500000  // 50万
    }

    Object.entries(updates).forEach(([field, value]) => {
      form[field] = value
      modifiedFields[field] = true
    })

    await handleUpload()
    ElMessage.success('套餐4应用成功')
  } catch (error) {
    console.error('套餐4应用失败:', error)
    ElMessage.error(error.message || '套餐4应用失败')
  } finally {
    loading.package4 = false
  }
}

// 解析卡牌数据
const parseCardData = (gameData) => {
  const unlockItemsNew = gameData.userInfo.unlockItemsNew || []
  return unlockItemsNew.map(cardId => {
    const [id, level] = cardId.split('_')
    return {
      cardId: id,
      level: parseInt(level) || 1,
      status: 1,
      type: getCardType(id),
      isNew: true,
      loading: false
    }
  })
}

// 修改数据解析函数
const parseGameData = (data) => {
  try {
    if (!data || !data.base_data) {
      throw new Error('无效的游戏数据')
    }

    const gameData = JSON.parse(data.base_data)
    if (!gameData || !gameData.userInfo) {
      throw new Error('无效的用户信息')
    }

    // 获取现有的瓶子数量，如果不存在则默认为 0
    const treasures = gameData.userInfo['lv_data']?.treasures || []
    existingBottles.energy = treasures.filter(id => id === 4405).length || 0
    existingBottles.star = treasures.filter(id => id === 5405).length || 0
    existingBottles.diamond = treasures.filter(id => id === 4904).length || 0

    // 转换为显示值（每种瓶子的总数值），确保不存在时显示 0
    form.energyBottle = (existingBottles.energy * 100) || 0  // 体力瓶 * 100
    form.starBottle = (existingBottles.star * 300) || 0      // 星星瓶 * 300
    form.diamondBottle = (existingBottles.diamond * 20) || 0 // 钻石瓶 * 20

    // 确保其他字段也有默认值
    form.adCard = gameData.userInfo['ad_card_data']?.num || 0
    form.boxIndex = gameData.userInfo.boxindex || 0

    return gameData
  } catch (error) {
    console.error('数据解析失败:', error)
    throw new Error(`数据解析失败: ${error.message}`)
  }
}

// 卡牌类型判断
const getCardType = (cardId) => {
  const id = parseInt(cardId)
  if (id >= 1000 && id < 2000) return 'attack'
  if (id >= 2000 && id < 3000) return 'defense'
  return 'support'
}

// 获取卡牌类型名称
const getCardTypeName = (type) => {
  const types = {
    attack: '攻击卡',
    defense: '防御卡',
    support: '辅助卡'
  }
  return types[type] || '未知'
}

// 获取卡牌类型标签样式
const getCardTypeTag = (type) => {
  const types = {
    attack: 'danger',
    defense: 'info',
    support: 'success'
  }
  return types[type] || ''
}

// 处理卡牌变更
const handleCardChange = (card) => {
  modifiedFields.cards = true
  // 在这里可以添加其他处理逻辑
}

// 处理卡牌升级
const handleCardUpgrade = async (card) => {
  card.loading = true
  try {
    card.level = Math.min(card.level + 1, 10)
    handleCardChange(card)
    ElMessage.success('卡牌升级成功')
  } catch (error) {
    ElMessage.error('卡牌升级失败')
  } finally {
    card.loading = false
  }
}

// 处理卡牌移除
const handleCardRemove = async (card) => {
  try {
    await ElMessageBox.confirm('确定要移除这张卡牌吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    const index = unlockedCards.value.indexOf(card)
    if (index > -1) {
      unlockedCards.value.splice(index, 1)
      handleCardChange(card)
      ElMessage.success('卡牌移除成功')
    }
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('卡牌移除失败')
    }
  }
}

// 批量升级
const handleBatchUpgrade = async () => {
  try {
    await ElMessageBox.confirm('确定要批量升级中的卡牌吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 实现批量升级逻辑
    ElMessage.success('批量升级成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量升级失败')
    }
  }
}

// 批量激活
const handleBatchActivate = async () => {
  try {
    await ElMessageBox.confirm('确定��批量激活选中的卡牌吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 实现批量激活逻辑
    ElMessage.success('批量激活成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('批量激活失败')
    }
  }
}

// 添加卡牌处理函数
const handleAddCard = async () => {
  if (!newCard.id) {
    ElMessage.warning('请输入卡牌ID')
    return
  }

  loading.addCard = true
  try {
    // 创建新卡牌对象
    const card = {
      cardId: newCard.id.toString(),
      level: newCard.level,
      status: 1,
      type: getCardType(newCard.id),
      isNew: true,
      loading: false
    }

    // 添加到卡牌列表
    unlockedCards.value.push(card)
    
    // 标记卡牌数据已修改
    modifiedFields.cards = true

    // 清空输入
    newCard.id = null
    newCard.level = 1

    ElMessage.success('卡牌添加成功')
  } catch (error) {
    console.error('添加卡牌失败:', error)
    ElMessage.error('添加卡牌失败')
  } finally {
    loading.addCard = false
  }
}

const getCardCounts = () => {
  const counts = {}
  unlockedCards.value.forEach(card => {
    counts[card.cardId] = (counts[card.cardId] || 0) + 1
  })
  return counts
}

const getDuplicateCount = () => {
  const counts = getCardCounts()
  return Object.values(counts).reduce((sum, count) => sum + (count > 1 ? count - 1 : 0), 0)
}
</script>

<style scoped>
.happy-match {
  padding: 15px;
  max-width: 1200px;
  margin: 0 auto;
}

.card-header-wrapper {
  background: linear-gradient(135deg, #FF69B4 0%, #FFB6C1 100%);
  padding: 12px 20px;
  border-radius: 8px 8px 0 0;
  margin-bottom: 15px;
  box-shadow: 0 2px 12px rgba(255, 105, 180, 0.2);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.header-icon {
  font-size: 20px;
  animation: bounce 1s infinite;
}

.card-content {
  padding: 15px;
}

.openid-section {
  margin-bottom: 20px;
}

.openid-input {
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.openid-input:hover {
  transform: translateY(-2px);
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.action-button {
  flex: 1;
  max-width: 200px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.resources-grid {
  padding: 15px;
}

.resource-cards {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
}

.resource-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  transition: all 0.3s ease;
  border: 1px solid #ebeef5;
  height: 100%;
}

.resource-section:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.resource-inputs {
  display: grid;
  gap: 15px;
  grid-template-columns: 1fr;
}

.section-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #ebeef5;
  font-weight: 600;
}

.title-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.card-count {
  font-size: 14px;
  color: #606266;
}

.count-number {
  color: #F56C6C;
  font-weight: bold;
  margin: 0 2px;
}

/* Element Plus 组件样式调整 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-form-item__label) {
  font-size: 14px;
  color: #606266;
  margin-bottom: 4px;
}

/* 动画效果 */
@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

/* 响应调整 */
@media (max-width: 768px) {
  .happy-match {
    padding: 10px;
  }

  .button-group {
    flex-direction: column;
    align-items: stretch;
  }

  .action-button {
    width: 100%;
    max-width: none;
  }
}

/* 修改输入框样式 */
:deep(.modified-input) .el-input__wrapper {
  box-shadow: 0 0 0 1px #f56c6c inset !important;
}

:deep(.modified-input:hover) .el-input__wrapper {
  box-shadow: 0 0 0 1px #f56c6c inset !important;
}

:deep(.modified-input) .el-input-number__decrease,
:deep(.modified-input) .el-input-number__increase {
  border-color: #f56c6c !important;
}

/* 空状态样式 */
.empty-text {
  color: #909399;
  font-size: 14px;
  margin-top: 10px;
}

/* 添加 token 输入框样式 */
.token-input {
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.token-input:hover {
  transform: translateY(-2px);
}

.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.input-label {
  min-width: 70px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
}

/* 修改输入框样式以适应新布局 */
.openid-input,
.token-input {
  flex: 1;
  margin-bottom: 0;
}

/* 添加新样式 */
.item-id {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 4px;
}

.item-id {
  font-size: 12px;
  color: #909399;
}

.bottle-count {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #67C23A;
}

.existing-bottles {
  color: #F56C6C;  /* 使用红色显示现有瓶子数量 */
  margin-left: 8px;
  font-weight: 500;
}

.card-management {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.card-operations {
  display: flex;
  justify-content: flex-end;  /* 改为右对齐 */
  align-items: center;
  margin-top: 15px;
}

:deep(.el-table) {
  --el-table-border-color: #dcdfe6;
  --el-table-header-bg-color: #f5f7fa;
}

:deep(.el-tag) {
  margin-left: 5px;
}

:deep(.el-input-number) {
  width: 100px;
}

/* 添加分隔线样式 */
.divider {
  height: 1px;
  background: #ebeef5;
  margin: 15px 0;
  width: 100%;
}

/* 调整资源输入区域的间距 */
.resource-inputs {
  display: grid;
  gap: 15px;
  grid-template-columns: 1fr;
}

/* 调整表单项的样式 */
:deep(.el-form-item) {
  margin-bottom: 0;
}

/* 确保输入框宽度一致 */
:deep(.el-input-number) {
  width: 100%;
}

/* 添加卡牌区域样式 */
.add-card-section {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 4px;
  margin-bottom: 15px;
  border: 1px dashed #dcdfe6;
}

.add-card-section:hover {
  border-color: #409EFF;
}

/* 调整表单项间距 */
:deep(.el-form--inline .el-form-item) {
  margin-right: 15px;
  margin-bottom: 0;
}

/* 确保按钮垂直对齐 */
:deep(.el-form--inline .el-form-item__content) {
  display: flex;
  align-items: center;
}

.card-stats {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #606266;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.info-icon {
  color: #909399;
  cursor: pointer;
  font-size: 16px;
}

.info-icon:hover {
  color: #409EFF;
}

.card-stats-tooltip {
  min-width: 200px;
  max-width: 300px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 8px;
  font-weight: bold;
}

.stats-total {
  font-size: 12px;
  color: #909399;
}

.stats-list {
  padding: 4px 8px;
}

.stat-item-detail {
  display: flex;
  align-items: center;
  padding: 4px 0;
  gap: 8px;
}

.card-id {
  min-width: 60px;
  font-family: monospace;
}

.card-count {
  color: #F56C6C;
  font-weight: 500;
  min-width: 45px;
}

:deep(.el-tooltip__trigger) {
  display: inline-flex;
}

:deep(.el-popper.is-light) {
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.status-option {
  display: flex;
  align-items: center;
  width: 100%;
}

:deep(.card-status-select) {
  min-width: 150px !important;
}

:deep(.card-status-select .el-select-dropdown__item) {
  padding: 8px 12px;
  height: 36px;
  line-height: 20px;
}

:deep(.el-select-dropdown__item .el-tag) {
  width: 100%;
  text-align: center;
}

:deep(.el-select__wrapper),
:deep(.el-tooltip__trigger) {
  width: 100px !important;
}

:deep(.el-select .el-input__wrapper) {
  width: 100px !important;
}

:deep(.el-select) {
  width: 100px !important;
}

/* 添加游戏选择样式 */
.game-select-section {
  margin-bottom: 20px;
}

.game-select {
  width: 150px !important;
}

:deep(.game-select .el-input__wrapper) {
  width: 150px !important;
}

:deep(.game-select .el-select__wrapper) {
  width: 150px !important;
}

/* 更新输入组样式 */
.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  gap: 10px;
}

.input-label {
  min-width: 70px;
  color: #606266;
  font-size: 14px;
  font-weight: 500;
  text-align: right;
}
</style> 