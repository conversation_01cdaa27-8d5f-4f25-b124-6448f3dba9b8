<template>
  <div class="ai-analyzer">
    <div class="analysis-section">
      <div class="label">AI分析:</div>
      <div v-if="analyzing" class="analysis-loading">
        <div class="typing-indicator">
          <div class="dot"></div>
          <div class="dot"></div>
          <div class="dot"></div>
        </div>
        <span class="loading-text">AI正在分析中...</span>
      </div>
      <div v-else-if="analysisResult" class="content analysis-content">
        {{ analysisResult }}
      </div>
    </div>

    <!-- AI设置对话框 -->
    <el-dialog v-model="showSettingsDialog" title="AI设置" width="50%">
      <el-form :model="tempSettings" label-width="120px">
        <el-form-item label="API Key" required>
          <el-input v-model="tempSettings.apiKey" type="password" show-password placeholder="请输入Groq API Key" />
        </el-form-item>

        <el-form-item label="模型">
          <el-select v-model="tempSettings.model" style="width: 100%" placeholder="请选择模型">
            <el-option-group v-for="group in MODEL_OPTIONS" :key="group.label" :label="group.label">
              <el-option v-for="item in group.options" :key="item.value" :label="item.label" :value="item.value"
                @mouseenter="showTooltip($event, item.description)" @mouseleave="hideTooltip">
                <span style="float: left">{{ item.label }}</span>
              </el-option>
            </el-option-group>
          </el-select>
          <div class="form-tip">
            <template v-if="tempSettings.model.includes('gpt-4')">
              注意: GPT-4 模型的费用较高，请谨慎使用
            </template>
            <template v-else-if="tempSettings.model.includes('16k')">
              支持更长的上下文，最支持16k tokens
            </template>
            <template v-else>
              GPT-3.5-turbo 模型性价比最高，适合大多数场景
            </template>
          </div>
        </el-form-item>

        <el-form-item label="Temperature">
          <el-slider v-model="tempSettings.temperature" :min="0" :max="2" :step="0.1" show-input />
          <div class="form-tip">值越高，回答越随机创造性；值越低，回答越确定精确</div>
        </el-form-item>

        <el-form-item label="最大Token">
          <el-input-number v-model="tempSettings.maxTokens" :min="100" :max="4000" :step="100" style="width: 100%" />
          <div class="form-tip">控制回答的最大长度，建议保持在2000左右</div>
        </el-form-item>

        <el-form-item label="系统提示词">
          <el-input v-model="tempSettings.systemPrompt" type="textarea" :rows="3" placeholder="设置AI的角色和任务描述" />
          <div class="form-tip">定义AI助手的角色和任务说明</div>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetSettings">重置默认值</el-button>
          <el-button @click="showSettingsDialog = false">取消</el-button>
          <el-button type="primary" @click="saveSettings">保存</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模型说明提示框 -->
    <div v-show="tooltipVisible" class="model-tooltip" :style="tooltipStyle">
      {{ tooltipContent }}
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  content: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['analysis-complete'])

// 模型选项配置
const MODEL_OPTIONS = [
  {
    label: 'Gemini',
    options: [
      {
        label: 'Gemma-7b-it',
        value: 'gemma-7b-it',
        description: 'Gemini 7B 指令微调版本'
      },
      {
        label: 'Gemma-2-9b-it',
        value: 'gemma2-9b-it',
        description: 'Gemini 9B 增强版本'
      }
    ]
  },
  {
    label: 'LLaMA3',
    options: [
      {
        label: 'LLaMA-3.1-70B',
        value: 'llama-3.1-70b-versatile',
        description: '通用型大模型'
      },
      {
        label: 'LLaMA-3.1-8B',
        value: 'llama-3.1-8b-instant',
        description: '快速响应版本'
      }
    ]
  },
  {
    label: 'LLaMA3.2',
    options: [
      {
        label: 'LLaMA-3.2-90B Text',
        value: 'llama-3.2-90b-text-preview',
        description: '规模文本处理预览版（推荐用于代码分析）'
      },
      {
        label: 'LLaMA-3.2-11B Text',
        value: 'llama-3.2-11b-text-preview',
        description: '文本处理预览版'
      }
    ]
  }
]

// 状态变量
const analyzing = ref(false)
const analysisResult = ref('')
const showSettingsDialog = ref(false)
const tooltipVisible = ref(false)
const tooltipContent = ref('')
const tooltipStyle = ref({
  top: '0px',
  left: '0px'
})

// AI配置
const aiConfig = reactive({
  apiKey: localStorage.getItem('groq_api_key') || '',
  model: localStorage.getItem('groq_model') || 'llama-3.2-90b-text-preview',
  temperature: Number(localStorage.getItem('groq_temperature')) || 0.3,
  maxTokens: Number(localStorage.getItem('groq_max_tokens')) || 4096,
  systemPrompt: localStorage.getItem('groq_system_prompt') || '你是一个专业的程序员和代码分析专家。请使用中文回复分析代码时，请详细说明代码的结构、功能、潜在问题和优化建议。如果是JSON数据，请解释其格式和字段含义。',
  apiEndpoint: 'https://api.groq.com/openai/v1/chat/completions'
})

const tempSettings = reactive({ ...aiConfig })

// 方法
const analyze = async () => {
  if (!aiConfig.apiKey) {
    ElMessage.warning('请先设置API Key')
    return
  }

  analyzing.value = true
  try {
    const result = await analyzeWithAI(props.content)
    analysisResult.value = result
    emit('analysis-complete', result)
  } catch (error) {
    ElMessage.error('分析失败: ' + error.message)
    analysisResult.value = '分析失败: ' + error.message
  } finally {
    analyzing.value = false
  }
}

const analyzeWithAI = async (text, retries = 3) => {
  for (let i = 0; i < retries; i++) {
    try {
      const response = await fetch(aiConfig.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${aiConfig.apiKey}`
        },
        body: JSON.stringify({
          model: aiConfig.model,
          messages: [{
            role: 'system',
            content: aiConfig.systemPrompt
          }, {
            role: 'user',
            content: `请分析这段数据:\n${text}`
          }],
          temperature: aiConfig.temperature,
          max_tokens: aiConfig.maxTokens,
          n: 1,
          stream: false
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error?.message || `请求失败: ${response.status}`)
      }

      const data = await response.json()
      return data.choices[0].message.content
    } catch (error) {
      console.error(`第${i + 1}次调用失败:`, error)
      if (i === retries - 1) throw error
      await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
    }
  }
}

const showTooltip = (event, description) => {
  tooltipContent.value = description
  tooltipVisible.value = true
  tooltipStyle.value = {
    top: `${event.clientY - 10}px`,
    left: `${event.clientX}px`,
    transform: 'translate(-50%, -100%)'
  }
}

const hideTooltip = () => {
  tooltipVisible.value = false
}

const saveSettings = () => {
  if (!tempSettings.apiKey) {
    ElMessage.warning('请输入API Key')
    return
  }

  Object.assign(aiConfig, tempSettings)
  
  localStorage.setItem('groq_api_key', tempSettings.apiKey)
  localStorage.setItem('groq_model', tempSettings.model)
  localStorage.setItem('groq_temperature', tempSettings.temperature.toString())
  localStorage.setItem('groq_max_tokens', tempSettings.maxTokens.toString())
  localStorage.setItem('groq_system_prompt', tempSettings.systemPrompt)

  showSettingsDialog.value = false
  ElMessage.success('设置已保存')
}

const resetSettings = () => {
  Object.assign(tempSettings, {
    model: 'llama-3.2-90b-text-preview',
    temperature: 0.3,
    maxTokens: 4096,
    systemPrompt: '你是一个专业的程序员和代码分析专家。请使用中文回复。分析代码时，请详细说明代码的结构、功能、潜在问题和优化建议。如果是JSON数据，请解释其格式和字段含义。',
    apiEndpoint: 'https://api.groq.com/openai/v1/chat/completions'
  })
}

// 导出方法供父组件调用
defineExpose({
  analyze,
  showSettings: () => showSettingsDialog.value = true
})
</script>

<style scoped>
.analysis-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.label {
  color: #86868b;
  font-size: 0.9em;
  margin-bottom: 4px;
}

.analysis-content {
  white-space: pre-wrap;
  line-height: 1.5;
  color: #444;
  background: #f5f5f7;
  padding: 12px;
  border-radius: 6px;
}

.analysis-loading {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f5f7fa;
  border-radius: 6px;
}

.loading-text {
  color: #606266;
  font-size: 14px;
}

.typing-indicator {
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #409eff;
  animation: bounce 1.4s infinite ease-in-out;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1); }
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.model-tooltip {
  position: fixed;
  z-index: 9999;
  background: linear-gradient(145deg, #2c3e50, #3498db);
  color: #ffffff;
  padding: 10px 15px;
  border-radius: 6px;
  font-size: 14px;
  pointer-events: none;
  max-width: 300px;
  word-wrap: break-word;
  box-shadow: 0 4px 20px rgba(52, 152, 219, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.model-tooltip::after {
  content: '';
  position: absolute;
  bottom: -5px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 5px 5px 0;
  border-style: solid;
  border-color: #2c3e50 transparent transparent;
}
</style>
