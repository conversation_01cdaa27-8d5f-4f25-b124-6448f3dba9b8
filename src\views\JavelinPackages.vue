<template>
  <el-scrollbar height="calc(100vh - 84px)">
    <div class="javelin-packages">
      <div class="page-header">
        <h2>标枪王者套餐</h2>
        <div class="subtitle">V小程序可充 | 充你的号上 | 稳定防封</div>
      </div>

      <div class="package-list">
        <div v-for="(pkg, index) in packages" :key="index" :class="['package-card', pkg.highlight ? 'highlight' : '']">
          <div class="package-header">
            <div class="name-with-icon">
              <el-icon class="package-icon" :class="pkg.iconClass">
                <component :is="pkg.icon" />
              </el-icon>
              <span class="package-name">{{ pkg.name }}</span>
            </div>
            <el-tag :type="pkg.tagType" effect="dark">{{ pkg.tag }}</el-tag>
          </div>

          <div class="package-content">
            <div class="price-section">
              <span class="currency">¥</span>
              <span class="amount">{{ pkg.price }}</span>
            </div>
            <div class="benefits-section">
              <div class="benefit-item">
                <span class="highlight-amount">{{ pkg.mainBenefit }}</span>
                <span class="separator">|</span>
                <span class="other-benefits">{{ pkg.otherBenefits.join(' | ') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-scrollbar>
</template>

<script setup>
import { ref } from 'vue'
import {
  Discount,
  Medal,
  GoldMedal,
  TrendCharts,
  Trophy,
  Star
} from '@element-plus/icons-vue'

const packages = ref([
  {
    name: '入门套餐',
    price: '1',
    tag: '体验',
    tagType: 'info',
    mainBenefit: '2000钻石',
    otherBenefits: ['稳定到账', '快速充值'],
    icon: Discount,
    iconClass: 'icon-starter'
  },
  {
    name: '经验套餐',
    price: '1',
    tag: '实惠',
    tagType: 'success',
    mainBenefit: '100经验石',
    otherBenefits: ['即时到账', '安全可靠'],
    icon: Medal,
    iconClass: 'icon-exp'
  },
  {
    name: '钻石大礼包',
    price: '10',
    tag: '热门',
    tagType: 'warning',
    highlight: true,
    mainBenefit: '70,000钻石',
    otherBenefits: ['超值优惠', '稳定防封'],
    icon: GoldMedal,
    iconClass: 'icon-diamond'
  },
  {
    name: '豪华钻石包',
    price: '20',
    tag: '推荐',
    tagType: 'danger',
    mainBenefit: '150,000钻石',
    otherBenefits: ['极速到账', '安全保障'],
    icon: TrendCharts,
    iconClass: 'icon-deluxe'
  },
  {
    name: '经验大礼包',
    price: '10',
    tag: '热卖',
    tagType: 'warning',
    mainBenefit: '3,500经验石',
    otherBenefits: ['性价比高', '安全可靠'],
    icon: Trophy,
    iconClass: 'icon-exp-plus'
  },
  {
    name: '至尊无限包',
    price: '30',
    tag: 'VIP',
    tagType: 'danger',
    highlight: true,
    mainBenefit: '无限钻石+经验石',
    otherBenefits: ['尊贵特权'],
    icon: Star,
    iconClass: 'icon-vip'
  }
])
</script>

<style scoped>
.javelin-packages {
  padding: 12px;
  background: #f5f7fa;
  min-height: 100%;
}

.page-header {
  text-align: center;
  margin-bottom: 20px;
  color: #303133;
}

.page-header h2 {
  font-size: 24px;
  margin-bottom: 8px;
  background: linear-gradient(45deg, #409EFF, #36D1DC);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.subtitle {
  color: #909399;
  font-size: 14px;
}

.package-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.package-card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  transition: all 0.3s;
}

.package-card.highlight {
  border: 2px solid #409EFF;
  background: linear-gradient(to right, #ffffff, #f0f9ff);
}

.package-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.package-name {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
}

.package-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.price-section {
  display: flex;
  align-items: baseline;
  min-width: 80px;
}

.currency {
  font-size: 14px;
  color: #409EFF;
  margin-right: 2px;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
}

.benefits-section {
  flex: 1;
}

.benefit-item {
  display: flex;
  align-items: center;
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
}

.highlight-amount {
  color: #f56c6c;
  font-weight: bold;
  font-size: 16px;
}

.separator {
  margin: 0 8px;
  color: #dcdfe6;
}

.other-benefits {
  color: #909399;
  overflow: hidden;
  text-overflow: ellipsis;
}

.package-card.highlight .highlight-amount {
  background: linear-gradient(45deg, #f56c6c, #ff9a9e);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 17px;
}

@media (max-width: 768px) {
  .javelin-packages {
    padding: 8px;
  }

  .page-header h2 {
    font-size: 20px;
  }

  .package-card {
    padding: 12px;
  }

  .package-name {
    font-size: 15px;
  }

  .amount {
    font-size: 20px;
  }

  .benefit-item {
    font-size: 13px;
  }

  /* 确保内容在一行显示 */
  .package-content {
    flex-wrap: nowrap;
  }

  /* 价格部分固定宽度 */
  .price-section {
    min-width: 60px;
  }

  /* 好处描述部分自适应 */
  .benefits-section {
    flex: 1;
    min-width: 0;
    /* 确保文本可以正确截断 */
  }

  .highlight-amount {
    font-size: 15px;
  }
  
  .package-card.highlight .highlight-amount {
    font-size: 16px;
  }
  
  .separator {
    margin: 0 4px;
  }
  
  .other-benefits {
    font-size: 13px;
  }
}

/* 添加滚动条样式 */
:deep(.el-scrollbar__wrap) {
  overflow-x: hidden !important;
}

/* 优化触摸滚动 */
.javelin-packages {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.name-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.package-icon {
  font-size: 20px;
  padding: 6px;
  border-radius: 8px;
  transition: all 0.3s;
}

/* 图标样式 */
.icon-starter {
  background: #ecf5ff;
  color: #409EFF;
}

.icon-exp {
  background: #f0f9eb;
  color: #67c23a;
}

.icon-diamond {
  background: #fdf6ec;
  color: #e6a23c;
}

.icon-deluxe {
  background: #fef0f0;
  color: #f56c6c;
}

.icon-exp-plus {
  background: #f4f4f5;
  color: #909399;
}

.icon-vip {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: white;
}

/* 悬浮效果 */
.package-card:hover .package-icon {
  transform: scale(1.1) rotate(5deg);
}

@media (max-width: 768px) {
  .package-icon {
    font-size: 18px;
    padding: 4px;
  }

  .name-with-icon {
    gap: 6px;
  }
}
</style>