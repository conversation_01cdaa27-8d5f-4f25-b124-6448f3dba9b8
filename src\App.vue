<template>
  <el-container class="app-container">
    <el-aside :width="isCollapse ? '64px' : '200px'" class="aside">
      <div class="logo-container">
        <img src="@/assets/logo.png" alt="Logo" class="logo" v-if="!isCollapse">
        <el-icon v-else>
          <Money />
        </el-icon>
      </div>
      <el-menu :default-active="$route.path" class="el-menu-vertical" :collapse="isCollapse" :router="true"
        background-color="#304156" text-color="#bfcbd9" active-text-color="#409EFF">
        <el-menu-item index="/">
          <el-icon>
            <HomeFilled />
          </el-icon>
          <span>仪表盘</span>
        </el-menu-item>

        <el-sub-menu index="/recharge">
          <template #title>
            <el-icon>
              <Money />
            </el-icon>
            <span>充值管理</span>
          </template>
          <el-menu-item index="/recharge/gunsoulsniping">
            <el-icon>
              <Aim />
            </el-icon>
            <span>枪魂狙击</span>
          </el-menu-item>
          <el-menu-item index="/recharge/smallshelter">
            <el-icon>
              <House />
            </el-icon>
            <span>小小庇护所</span>
          </el-menu-item>
          <el-menu-item index="/recharge/bubble-game">
            <el-icon>
              <Lollipop />
            </el-icon>
            <span>一起泡泡龙</span>
          </el-menu-item>
          <el-menu-item index="/recharge/used-car-game">
            <el-icon>
              <Van />
            </el-icon>
            <span>二手车模拟器</span>
          </el-menu-item>
          <el-menu-item index="/recharge/tower-defense">
            <el-icon>
              <House />
            </el-icon>
            <span>塔塔守卫战</span>
          </el-menu-item>
          <el-menu-item index="/recharge/cat-survival">
            <el-icon>
              <MagicStick />
            </el-icon>
            <span>喵桑活下去/欢乐勇士</span>
          </el-menu-item>
          <el-menu-item index="/recharge/javelin-king">
            <el-icon>
              <Position />
            </el-icon>
            <span>标枪王者</span>
          </el-menu-item>
          <el-menu-item index="/recharge/happy-match">
            <el-icon>
              <Sugar />
            </el-icon>
            <span>开心点点消</span>
          </el-menu-item>
          <el-menu-item index="/recharge/cant-beat-me">
            <el-icon>
              <KnifeFork />
            </el-icon>
            <span>砍不过我呀</span>
          </el-menu-item>
          <el-menu-item index="/recharge/crazy-blacksmith">
            <el-icon>
              <Tools />
            </el-icon>
            <span>疯狂铁匠</span>
          </el-menu-item>
          <el-menu-item index="/recharge/gun-battle">
            <el-icon>
              <Aim />
            </el-icon>
            <span>枪战王者</span>
          </el-menu-item>
          <el-menu-item index="/recharge/panda-adventure">
            <el-icon>
              <Platform />
            </el-icon>
            <span>熊猫冒险传奇</span>
          </el-menu-item>
          <el-menu-item index="/recharge/horror-hide-and-seek">
            <el-icon>
              <View />
            </el-icon>
            <span>恐怖躲猫猫</span>
          </el-menu-item>
          <el-menu-item index="/recharge/lazy-brother">
            <el-icon>
              <MoonNight />
            </el-icon>
            <span>躺平小弟</span>
          </el-menu-item>
          <el-menu-item index="/recharge/cultivator-simulator">
            <el-icon>
              <VideoPlay />
            </el-icon>
            <span>散修生活模拟器</span>
          </el-menu-item>
          <el-menu-item index="/recharge/oracle-war">
            <el-icon>
              <Connection />
            </el-icon>
            <span>甲骨文战争</span>
          </el-menu-item>
          <el-menu-item index="/recharge/three-kingdoms-auto-chess">
            <el-icon>
              <Trophy />
            </el-icon>
            <span>三国争霸自走棋</span>
          </el-menu-item>
          <el-menu-item index="/recharge/slow-piggy">
            <el-icon>
              <Chicken />
            </el-icon>
            <span>慢豚豚的生活</span>
          </el-menu-item>
          <el-menu-item index="/recharge/who-beat-me">
            <el-icon>
              <Trophy />
            </el-icon>
            <span>看谁能打过</span>
          </el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/decoder">
          <template #title>
            <el-icon>
              <Tools />
            </el-icon>
            <span>解码工具</span>
          </template>
          <el-menu-item index="/decoder/base64">
            <el-icon>
              <Key />
            </el-icon>
            <span>大模型解码bs6</span>
          </el-menu-item>
          <el-menu-item index="/decoder/custom-base64">
            <el-icon>
              <Lock />
            </el-icon>
            <span>非标准bs6解密</span>
          </el-menu-item>
        </el-sub-menu>

        <el-sub-menu index="/promotion">
          <template #title>
            <el-icon>
              <Promotion />
            </el-icon>
            <span>宣传中心</span>
          </template>
          <el-menu-item index="/packages">
            <el-icon>
              <Present />
            </el-icon>
            <span>小小庇护所套餐</span>
          </el-menu-item>
          <el-menu-item index="/javelin-packages">
            <el-icon>
              <Position />
            </el-icon>
            <span>标枪王者套餐</span>
          </el-menu-item>
          <el-menu-item index="/used-car-packages">
            <el-icon>
              <Van />
            </el-icon>
            <span>二手车模拟器套餐</span>
          </el-menu-item>
          <el-menu-item index="/cant-beat-me-packages">
            <el-icon>
              <KnifeFork />
            </el-icon>
            <span>砍不过我呀套餐</span>
          </el-menu-item>
          <el-menu-item index="/gun-battle-packages">
            <el-icon>
              <Aim />
            </el-icon>
            <span>枪战王者套餐</span>
          </el-menu-item>
          <el-menu-item index="/lazy-brother-packages">
            <el-icon>
              <MoonNight />
            </el-icon>
            <span>躺平小弟套餐</span>
          </el-menu-item>
          <el-menu-item index="/cultivator-simulator-packages">
            <el-icon>
              <Avatar />
            </el-icon>
            <span>散修生活模拟器套餐</span>
          </el-menu-item>
          <el-menu-item index="/oracle-war-packages">
            <el-icon>
              <Connection />
            </el-icon>
            <span>甲骨文战争套餐</span>
          </el-menu-item>
          <el-menu-item index="/promotion/three-kingdoms">
            <el-icon>
              <Trophy />
            </el-icon>
            <span>三国争霸自走棋</span>
          </el-menu-item>
          <el-menu-item index="/slow-piggy-packages">
            <el-icon>
              <Food />
            </el-icon>
            <span>慢豚豚的生活套餐</span>
          </el-menu-item>
          <el-menu-item index="/who-beat-packages">
            <el-icon>
              <Trophy />
            </el-icon>
            <span>看谁能打过套餐</span>
          </el-menu-item>
        </el-sub-menu>

        <el-menu-item index="/about">
          <el-icon>
            <InfoFilled />
          </el-icon>
          <span>关于</span>
        </el-menu-item>
      </el-menu>
    </el-aside>
    <el-container :class="['main-container', { 'is-collapsed': isCollapse }]">
      <el-header class="header">
        <div class="header-left">
          <el-button @click="toggleCollapse" class="toggle-btn">
            <el-icon>
              <Fold v-if="!isCollapse" />
              <Expand v-else />
            </el-icon>
          </el-button>
          <h1>管理平台</h1>
        </div>
        <div class="user-info">
          <span>管理员</span>
          <el-avatar :size="32" src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png"></el-avatar>
        </div>
      </el-header>
      <el-main>
        <router-view />
      </el-main>
    </el-container>
  </el-container>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import {
  HomeFilled,
  Money,
  Tools,
  InfoFilled,
  Key,
  Lock,
  Aim,
  House,
  Setting,
  Document,
  Plus,
  Star,
  MagicStick,
  Fold,
  Expand,
  Promotion,
  Present,
  Lollipop,
  Van,
  Position,
  Sugar,
  KnifeFork,
  Platform,
  View,
  MoonNight,
  VideoPlay,
  Avatar,
  Connection,
  Trophy,
  Chicken,
  Food
} from '@element-plus/icons-vue'

const isCollapse = ref(false)

// 添加触摸事件配置
const touchOptions = {
  passive: true
}

const handleResize = () => {
  isCollapse.value = window.innerWidth <= 768
}

onMounted(() => {
  handleResize() // 初始化时执行一次
  window.addEventListener('resize', handleResize)

  // 添加触摸事件监听器
  const mainContent = document.querySelector('.el-main')
  if (mainContent) {
    mainContent.addEventListener('touchstart', () => { }, touchOptions)
    mainContent.addEventListener('touchmove', () => { }, touchOptions)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)

  // 移除触摸事件监听器
  const mainContent = document.querySelector('.el-main')
  if (mainContent) {
    mainContent.removeEventListener('touchstart', () => { }, touchOptions)
    mainContent.removeEventListener('touchmove', () => { }, touchOptions)
  }
})

const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value
}
</script>

<style scoped>
html,
body {
  margin: 0;
  padding: 0;
}

.app-container {
  min-height: 100vh;
  display: flex;
}

.aside {
  background-color: #304156;
  transition: width 0.3s ease;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  margin-left: 200px;
  transition: margin-left 0.3s ease;
  min-height: 100vh;
}

.main-container.is-collapsed {
  margin-left: 64px;
}

.logo-container {
  flex-shrink: 0;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #2b2f3a;
}

.logo {
  height: 32px;
}

.el-menu {
  border-right: none !important;
}

.el-menu-vertical {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
}

.el-menu-vertical::-webkit-scrollbar {
  width: 6px;
}

.el-menu-vertical::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.el-menu-vertical::-webkit-scrollbar-track {
  background: transparent;
}

.el-menu-vertical:hover::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
}

.header {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 999;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-left h1 {
  margin: 0 0 0 20px;
  font-size: 18px;
  color: #303133;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-info span {
  margin-right: 8px;
  font-size: 14px;
  color: #606266;
}

.el-main {
  padding: 0;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

@media (max-width: 768px) {
  .aside {
    transform: translateX(0);
  }

  .aside.collapsed {
    transform: translateX(-100%);
  }

  .main-container {
    margin-left: 0 !important;
  }

  .main-container.is-collapsed {
    margin-left: 64px !important;
  }

  .header {
    left: 0 !important;
    width: 100% !important;
    transition: left 0.3s ease;
  }

  .header-left h1 {
    font-size: 16px;
  }

  .toggle-btn {
    padding: 4px 8px;
  }

  .user-info span {
    display: none;
  }
}

.el-collapse {
  border: none;
  background-color: transparent;
}

.el-collapse-item__header {
  background-color: #304156;
  color: #bfcbd9;
  border: none;
  padding: 0 20px;
  height: 56px;
  line-height: 56px;
}

.el-collapse-item__content {
  background-color: #1f2d3d;
  padding: 0;
}

.el-menu-item {
  background-color: #1f2d3d;
  color: #bfcbd9;
}

.el-menu-item:hover {
  background-color: #001528;
}

.el-menu-item.is-active {
  color: #409EFF;
}

.el-collapse-item__arrow {
  margin-right: 0;
}

.el-menu--collapse .el-collapse-item__header {
  padding: 0;
  text-align: center;
}

.el-menu--collapse .el-collapse-item__arrow {
  display: none;
}

.el-menu-vertical:not(.el-menu--collapse) {
  width: 200px;
}

.el-menu-item,
.el-sub-menu__title {
  height: 56px;
  line-height: 56px;
}

.el-sub-menu .el-menu-item {
  height: 50px;
  line-height: 50px;
}

.el-menu--collapse .el-sub-menu__title span {
  display: none;
}

.el-menu--collapse {
  width: 64px;
}

.menu-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

@media (max-width: 768px) {
  .menu-overlay.show {
    display: block;
  }
}
</style>
