<template>
  <div class="custom-base64-decoder">
    <el-card class="decoder-card">
      <template #header>
        <div class="card-header">
          <h3>非标准bs6解密工具</h3>
          <!-- 修改 AI 设置按钮 -->
          <el-button type="primary" @click="openAISettings">
            <el-icon><Setting /></el-icon>AI设置
          </el-button>
        </div>
      </template>

      <el-form>
        <el-form-item label="输入数据">
          <el-input
            v-model="inputText"
            type="textarea"
            :rows="8"
            placeholder="请输入要处理的数据"
          />
        </el-form-item>

        <el-form-item>
          <div class="button-group">
            <el-button type="primary" @click="handleEncode">
              <el-icon><Lock /></el-icon>加密
            </el-button>
            <el-button type="success" @click="handleDecode">
              <el-icon><Key /></el-icon>解密
            </el-button>
            <el-button @click="handleClear">
              <el-icon><Delete /></el-icon>清空
            </el-button>
          </div>
        </el-form-item>

        <!-- 添加 v-if 条件，只在有输出结果时显示 -->
        <template v-if="outputText">
          <el-form-item label="输出结果">
            <el-input
              v-model="outputText"
              type="textarea"
              :rows="8"
              readonly
            />
          </el-form-item>
          
          <el-form-item>
            <div class="button-group">
              <el-button type="primary" @click="handleCopy">
                <el-icon><DocumentCopy /></el-icon>复制结果
              </el-button>
              <el-button 
                type="success" 
                @click="startAnalysis"
              >
                <el-icon><Connection /></el-icon>AI分析
              </el-button>
            </div>
          </el-form-item>

          <!-- 确保 AIAnalyzer 组件正确放置 -->
          <AIAnalyzer
            ref="aiAnalyzer"
            :content="outputText"
            @analysis-complete="handleAnalysisComplete"
          />
        </template>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, watch, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Lock, Key, Delete, DocumentCopy, Setting, Connection } from '@element-plus/icons-vue'
import AIAnalyzer from '@/components/AIAnalyzer.vue'
import { debounce } from 'lodash-es'  // 添加 debounce 导入

const inputText = ref('')
const outputText = ref('')

// 自定义字符映射表
const CHAR_MAP = ["S", "Q", "v", "X", "C", "Z", "i", "J", "x", "c", "6", "G", "b", "A", "r", "F", 
                 "8", "V", "y", "p", "B", "l", "7", "5", "4", "n", "2", "R", "u", "M", "Y", "3", 
                 "d", "D", "t", "s", "P", "9", "g", "k", "q", "e", "o", "H", "W", "1", "a", "0", 
                 "h", "U", "K", "+", "T", "j", "m", "/", "L", "I", "E", "f", "z", "w", "O", "N"]

// 创建反向映射表
const CHAR_INDEX = {}
CHAR_MAP.forEach((char, index) => {
  CHAR_INDEX[char] = index
})

// 使用 ref 来引用 AIAnalyzer 组件
const aiAnalyzer = ref(null)

// 防抖处理的输入验证
const validateInput = debounce((value) => {
  if (!value) return
  
  try {
    JSON.parse(value)
  } catch (error) {
    ElMessage.warning('输入的不是有效的JSON格式')
  }
}, 500)

// 监听输入变化
watch(inputText, (newValue) => {
  validateInput(newValue)
})

// 防抖处理的AI分析
const debouncedAnalysis = debounce(() => {
  if (!outputText.value) {
    ElMessage.warning('请先解密数据')
    return
  }
  aiAnalyzer.value?.analyze()
}, 800)

// 修改 startAnalysis 方法
const startAnalysis = () => {
  debouncedAnalysis()
}

// 在组件卸载时取消防抖
onBeforeUnmount(() => {
  validateInput.cancel()
  debouncedAnalysis.cancel()
})

const handleEncode = () => {
  try {
    if (!inputText.value) {
      ElMessage.warning('请输入要加密的数据')
      return
    }
    const jsonData = JSON.parse(inputText.value)
    const jsonStr = JSON.stringify(jsonData)
    const buffer = new TextEncoder().encode(jsonStr)
    const base64Str = encodeBufferBase64(buffer)
    outputText.value = "NM" + base64Str + "SL"
    ElMessage.success('加密成功')
  } catch (error) {
    ElMessage.error('加密失败: ' + error.message)
  }
}

const handleDecode = async () => {
  try {
    if (!inputText.value) {
      ElMessage.warning('请输入要解密的数据')
      return
    }
    const data = inputText.value.trim()
    if (!data.startsWith('NM') || !data.endsWith('SL')) {
      throw new Error('无效的加密数据格式')
    }
    const base64Str = data.slice(2, -2)
    const buffer = decodeArrayBufferBase64(base64Str)
    const jsonStr = new TextDecoder().decode(buffer)
    const jsonData = JSON.parse(jsonStr)
    outputText.value = JSON.stringify(jsonData, null, 2)
    ElMessage.success('解密成功')
    
    // 添加自动AI分析
    // 使用 nextTick 确保 outputText 已更新
    nextTick(() => {
      startAnalysis()
    })
  } catch (error) {
    ElMessage.error('解密失败: ' + error.message)
  }
}

const encodeBufferBase64 = (buffer) => {
  const length = buffer.length
  let result = ''
  
  for (let i = 0; i < length; i += 3) {
    const byte1 = buffer[i]
    const byte2 = i + 1 < length ? buffer[i + 1] : 0
    const byte3 = i + 2 < length ? buffer[i + 2] : 0

    result += CHAR_MAP[byte1 >> 2]
    result += CHAR_MAP[((byte1 & 3) << 4) | (byte2 >> 4)]
    
    if (i + 1 < length) {
      result += CHAR_MAP[((byte2 & 15) << 2) | (byte3 >> 6)]
      if (i + 2 < length) {
        result += CHAR_MAP[byte3 & 63]
      } else {
        result += '='
      }
    } else {
      result += '=='
    }
  }
  
  return result
}

const decodeArrayBufferBase64 = (base64Str) => {
  const length = base64Str.length
  const paddingLength = base64Str.endsWith('==') ? 2 : base64Str.endsWith('=') ? 1 : 0
  const arrayLength = (length * 3 / 4) - paddingLength
  
  const buffer = new Uint8Array(arrayLength)
  let position = 0
  
  for (let i = 0; i < length; i += 4) {
    const char1 = CHAR_INDEX[base64Str[i]]
    const char2 = CHAR_INDEX[base64Str[i + 1]]
    const char3 = base64Str[i + 2] === '=' ? 0 : CHAR_INDEX[base64Str[i + 2]]
    const char4 = base64Str[i + 3] === '=' ? 0 : CHAR_INDEX[base64Str[i + 3]]
    
    buffer[position++] = (char1 << 2) | (char2 >> 4)
    if (position < arrayLength) {
      buffer[position++] = ((char2 & 15) << 4) | (char3 >> 2)
    }
    if (position < arrayLength) {
      buffer[position++] = ((char3 & 3) << 6) | char4
    }
  }
  
  return buffer
}

const handleClear = () => {
  inputText.value = ''
  outputText.value = ''
  ElMessage.info('已清空输入和输出')
}

const handleCopy = async () => {
  try {
    await navigator.clipboard.writeText(outputText.value)
    ElMessage.success('复制成功')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 修改 openAISettings 方法
const openAISettings = () => {
  // 确保 aiAnalyzer 存在后再调用 showSettings
  if (aiAnalyzer.value) {
    aiAnalyzer.value.showSettings()
  } else {
    ElMessage.warning('AI 分析器未正确初始化')
  }
}

const handleAnalysisComplete = (result) => {
  console.log('AI分析完成:', result)
}
</script>

<style scoped>
.custom-base64-decoder {
  padding: 20px 30px;  /* 减少上下内边距 */
}

.decoder-card {
  max-width: 1000px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;  /* 减少标题下方间距 */
}

.card-header h3 {
  margin: 0;
  font-size: 1.5em;
}

:deep(.el-form-item__content) {
  justify-content: center;
}

.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin: 15px 0;  /* 减少按钮组上下间距 */
}

.el-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  font-size: 1.1em;
}

.el-icon {
  margin-right: 6px;
  font-size: 1.2em;
}

:deep(.el-textarea__inner) {
  font-size: 1.1em;
  line-height: 1.5;
  padding: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 15px;  /* 减少表单项之间的间距 */
}

:deep(.el-card__header) {
  padding: 10px 20px;  /* 减少卡片标题区域的内边距 */
}

:deep(.el-card__body) {
  padding-top: 10px;  /* 减少卡片内容区域的上内边距 */
}

/* 调整标题栏样式以适应新按钮 */
.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
}

.card-header h3 {
  margin: 0;
  font-size: 1.5em;
}

/* 确保按钮组样式正确 */
.button-group {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin: 15px 0;
}

.button-group .el-button {
  display: flex;
  align-items: center;
  gap: 8px;
}
</style>
