import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'

// 按需导入 Element Plus 组件
import {
  ElButton,
  ElSelect,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElCard,
  ElMenu,
  ElMenuItem,
  ElSubMenu,
  ElContainer,
  ElHeader,
  ElMain,
  ElAside,
  ElRow,
  ElCol,
  ElStatistic,
  ElIcon,
  ElAvatar,
  ElSteps,
  ElStep,
  ElTabs,
  ElTabPane,
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElTimeline,
  ElTimelineItem,
  ElScrollbar,
  ElEmpty,
  ElMessage,
  ElDialog,
  ElLoading,
  ElTooltip,
  ElOption,
  ElOptionGroup,
  ElSlider,
  ElBacktop,
  ElResult,
  ElTag,
  ElAlert,
  ElDescriptions,
  ElDescriptionsItem
} from 'element-plus'
import 'element-plus/dist/index.css'

import {
  Money,
  User,
  Download,
  Refresh,
  Timer,
  TrendCharts,
  Bell,
  Close,
  Plus,
  Star,
  MagicStick,
  ShoppingCart,
  CaretTop  // 在这里添加 CaretTop
} from '@element-plus/icons-vue'

// 其他 import 语句...

// 添加全局的 ResizeObserver polyfill
if (!window.ResizeObserver) {
  window.ResizeObserver = class ResizeObserver {
    constructor(callback) {
      this.callback = callback;
      this.observables = [];
      this.rafId = null;
      this.timeoutId = null;
    }

    observe(target) {
      if (this.observables.some(obs => obs.target === target)) return;
      const observable = {
        target,
        size: {
          height: target.clientHeight,
          width: target.clientWidth
        }
      };
      this.observables.push(observable);
      this.checkForResize();
    }

    unobserve(target) {
      this.observables = this.observables.filter(obs => obs.target !== target);
      if (this.observables.length === 0) {
        this.stopChecking();
      }
    }

    disconnect() {
      this.observables = [];
      this.stopChecking();
    }

    checkForResize() {
      if (this.rafId || this.timeoutId) return;

      this.rafId = requestAnimationFrame(() => {
        this.rafId = null;
        const changedEntries = this.observables.filter(obs => {
          const newWidth = obs.target.clientWidth;
          const newHeight = obs.target.clientHeight;
          if (newWidth !== obs.size.width || newHeight !== obs.size.height) {
            const oldSize = { ...obs.size };
            obs.size.width = newWidth;
            obs.size.height = newHeight;
            return { target: obs.target, contentRect: obs.size, oldSize };
          }
          return false;
        });

        if (changedEntries.length > 0) {
          this.timeoutId = setTimeout(() => {
            this.timeoutId = null;
            this.callback(changedEntries, this);
            this.checkForResize();
          }, 100);  // 限制回调频率为每100ms一次
        } else {
          this.checkForResize();
        }
      });
    }

    stopChecking() {
      if (this.rafId) {
        cancelAnimationFrame(this.rafId);
        this.rafId = null;
      }
      if (this.timeoutId) {
        clearTimeout(this.timeoutId);
        this.timeoutId = null;
      }
    }
  }
}

// 添加 createResizeObserverPolyfill 函数
const createResizeObserverPolyfill = () => {
  return new ResizeObserver((entries) => {
    for (let entry of entries) {
      const { width, height } = entry.contentRect;
      // 在这里处理大小变化
      console.log(`Element size: ${width} x ${height}`);
    }
  });
};

// 添加全局触摸事件配置
const touchOptions = {
  passive: true
}

// 配置全局触摸事件
document.addEventListener('touchstart', () => { }, touchOptions)
document.addEventListener('touchmove', () => { }, touchOptions)
document.addEventListener('touchend', () => { }, touchOptions)

// 添加全局的事件监听器配置
const addPassiveSupport = () => {
  let supportsPassive = false;
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get: function () {
        supportsPassive = true;
        return true;
      }
    });
    window.addEventListener('testPassive', null, opts);
    window.removeEventListener('testPassive', null, opts);
  } catch (e) { }

  const wheelOpt = supportsPassive ? { passive: true } : false;
  const wheelEvent = 'onwheel' in document.createElement('div') ? 'wheel' :
    document.onmousewheel !== undefined ? 'mousewheel' :
      'DOMMouseScroll';

  // 添加全局的触摸和滚轮事件监听器
  window.addEventListener('touchstart', () => { }, wheelOpt);
  window.addEventListener('touchmove', () => { }, wheelOpt);
  window.addEventListener(wheelEvent, () => { }, wheelOpt);
}

// 在创建 Vue 应用之前调用
addPassiveSupport();

// 添加全局事件处理
document.addEventListener('DOMContentLoaded', () => {
  // 创建一个 MutationObserver 来监视 DOM 变化
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.addedNodes.length) {
        // 为新添加的元素添加被动事件监听器
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === 1) { // 元素节点
            node.addEventListener('touchstart', () => { }, { passive: true });
            node.addEventListener('touchmove', () => { }, { passive: true });
            node.addEventListener('touchend', () => { }, { passive: true });
          }
        });
      }
    });
  });

  // 开始观察整个文档
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 为现有元素添加被动事件监听器
  document.addEventListener('touchstart', () => { }, { passive: true });
  document.addEventListener('touchmove', () => { }, { passive: true });
  document.addEventListener('touchend', () => { }, { passive: true });
});

// 创建 Vue 应用
const app = createApp(App)

// 添加全局 mixin 来处理 addEquipmentDialogVisible
app.mixin({
  data() {
    return {
      addEquipmentDialogVisible: false
    }
  }
})

// 将 createResizeObserverPolyfill 函数添加为全局属性
app.config.globalProperties.$createResizeObserverPolyfill = createResizeObserverPolyfill;

// 增强ResizeObserver错误处理
// 1. 拦截全局错误
app.config.errorHandler = (err, vm, info) => {
  if (err.message && typeof err.message === 'string' &&
    (err.message.includes('ResizeObserver loop') ||
      err.message.includes('ResizeObserver loop completed with undelivered notifications'))) {
    // 忽略这个特定的错误
    return;
  }
  console.error(err, vm, info);
};

// 2. 拦截控制台错误输出
const originalConsoleError = console.error;
console.error = (...args) => {
  if (args[0] && typeof args[0] === 'string' &&
    (args[0].includes('ResizeObserver loop') ||
      args[0].includes('ResizeObserver loop completed with undelivered notifications') ||
      (args[0].message && args[0].message.includes('ResizeObserver')))) {
    // 忽略 ResizeObserver 错误
    return;
  }
  originalConsoleError.apply(console, args);
};

// 3. 修补ResizeObserver防止循环
const patchResizeObserver = () => {
  const resizeObserverError = new Error('ResizeObserver防护措施');
  const originalResizeObserver = window.ResizeObserver;

  // 替换原生ResizeObserver
  window.ResizeObserver = class PatchedResizeObserver extends ResizeObserver {
    constructor(callback) {
      const patchedCallback = (entries, observer) => {
        // 防止嵌套调用触发循环
        window.requestAnimationFrame(() => {
          try {
            callback(entries, observer);
          } catch (e) {
            if (e.message && e.message.includes('ResizeObserver')) {
              // 忽略ResizeObserver相关错误
              console.warn('已抑制ResizeObserver错误');
            } else {
              throw e;
            }
          }
        });
      };

      super(patchedCallback);
    }
  };
};

// 应用ResizeObserver补丁
if (typeof window !== 'undefined' && window.ResizeObserver) {
  patchResizeObserver();
}

// 注册 Element Plus 组件
const components = [
  ElButton,
  ElSelect,
  ElForm,
  ElFormItem,
  ElInput,
  ElInputNumber,
  ElCard,
  ElMenu,
  ElMenuItem,
  ElSubMenu,
  ElContainer,
  ElHeader,
  ElMain,
  ElAside,
  ElRow,
  ElCol,
  ElStatistic,
  ElIcon,
  ElAvatar,
  ElSteps,
  ElStep,
  ElTabs,
  ElTabPane,
  ElTable,
  ElTableColumn,
  ElSwitch,
  ElTimeline,
  ElTimelineItem,
  ElScrollbar,
  ElEmpty,
  ElMessage,
  ElDialog,
  ElTooltip,
  ElOption,
  ElOptionGroup,
  ElSlider,
  ElBacktop,
  ElResult,
  ElTag,
  ElAlert,
  ElDescriptions,
  ElDescriptionsItem
]

// 注册组件
components.forEach(component => {
  app.component(component.name, component)
})

// 单独注册 loading 指令
app.use(ElLoading)

// 注册图标组件
app.component('Money', Money)
app.component('User', User)
app.component('Download', Download)
app.component('Refresh', Refresh)
app.component('Timer', Timer)
app.component('TrendCharts', TrendCharts)
app.component('Bell', Bell)
app.component('Close', Close)
app.component('ShoppingCart', ShoppingCart)
app.component('CaretTop', CaretTop)  // 添加这行

app.config.globalProperties.$message = ElMessage

app.use(store).use(router)
app.mount('#app')
