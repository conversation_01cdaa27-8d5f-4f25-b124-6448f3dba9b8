<template>
  <div class="gun-battle-packages">
    <div class="watermark">
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
      <div class="watermark-text">跳跳鱼小游戏</div>
    </div>
    <div class="content-wrapper">
      <div class="page-header">
        <div class="title-wrapper">
          <h2>
            <el-icon><Aim /></el-icon>
            枪战王者套餐
          </h2>
          <div class="title-decoration"></div>
        </div>
        <div class="tags-container">
          <el-tag type="success" effect="dark" class="support-tag">
            <el-icon><Check /></el-icon>
            正版充值
          </el-tag>
          <el-tag type="warning" effect="dark" class="support-tag">
            <el-icon><Timer /></el-icon>
            保留进度
          </el-tag>
        </div>
      </div>

      <div class="package-grid">
        <!-- 套餐A -->
        <div class="package-card basic" @click="handlePackageClick">
          <div class="package-content">
            <div class="package-icon">
              <el-icon><Money /></el-icon>
            </div>
            <h3>套餐A</h3>
            <ul class="features">
              <li><el-icon><Coin /></el-icon>3万钻石</li>
              <li><el-icon><GoldMedal /></el-icon>3万金币</li>
              <li><el-icon><Lightning /></el-icon>3万体力</li>
            </ul>
            <div class="price">¥5</div>
          </div>
        </div>

        <!-- 套餐B -->
        <div class="package-card premium" @click="handlePackageClick">
          <div class="package-content">
            <div class="package-icon">
              <el-icon><Star /></el-icon>
            </div>
            <h3>套餐B</h3>
            <ul class="features">
              <li><el-icon><Coin /></el-icon>150万钻石</li>
              <li><el-icon><GoldMedal /></el-icon>150万金币</li>
              <li><el-icon><Lightning /></el-icon>150万体力</li>
            </ul>
            <div class="price">¥10</div>
          </div>
        </div>

        <!-- 套餐C -->
        <div class="package-card supreme" @click="handlePackageClick">
          <div class="package-content">
            <div class="package-icon">
              <el-icon><Trophy /></el-icon>
            </div>
            <h3>套餐C</h3>
            <ul class="features">
              <li><el-icon><Coin /></el-icon>25亿钻石</li>
              <li><el-icon><GoldMedal /></el-icon>25亿金币</li>
              <li><el-icon><Lightning /></el-icon>25亿体力</li>
            </ul>
            <div class="price">¥15</div>
          </div>
        </div>

        <!-- 终极套餐 -->
        <div class="package-card ultimate" @click="handlePackageClick">
          <div class="package-content">
            <div class="package-icon supreme">
              <el-icon><Medal /></el-icon>
            </div>
            <div class="title-row">
              <h3>终极套餐</h3>
              <span class="special-tag">
                <el-icon><Discount /></el-icon>
                特惠
              </span>
            </div>
            <ul class="features">
              <li><el-icon><Coin /></el-icon>无限钻石</li>
              <li><el-icon><GoldMedal /></el-icon>无限金币</li>
              <li><el-icon><Lightning /></el-icon>无限体力</li>
              <li class="special"><el-icon><Present /></el-icon>直接全部拉满</li>
            </ul>
            <div class="price">
              <span class="original">¥20</span>
              ¥18
            </div>
          </div>
        </div>
      </div>

      <div class="bottom-notice">
        套餐目前可刷，如后续查查可免费换号补或换店内其他游戏，下单默认同意，不退款
      </div>
    </div>
  </div>
</template>

<script setup>
import { 
  Star, Money, Lightning, Present, Check, Timer,
  Aim, Coin, GoldMedal, Trophy, Medal, Discount 
} from '@element-plus/icons-vue'

const handlePackageClick = () => {
  // 处理点击事件
}
</script>

<style scoped>
.gun-battle-packages {
  background: linear-gradient(135deg, #1a1c2a 0%, #2d3047 100%);
  padding: 10px;
  color: #fff;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
}

.watermark {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(4, 1fr);
  color: rgba(255, 255, 255, 0.15);
  font-size: 24px;
  pointer-events: none;
  z-index: 100;
  user-select: none;
  transform: rotate(-30deg);
  place-items: center;
  font-weight: 500;
}

.watermark-text {
  white-space: nowrap;
  text-shadow: 0 0 1px rgba(255, 255, 255, 0.1);
}

.content-wrapper {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 5px 10px 10px;
  max-width: 1000px;
  margin: 0 auto;
  width: 100%;
}

.page-header {
  text-align: center;
  margin-bottom: 10px;
  width: 100%;
}

.title-wrapper {
  position: relative;
  display: inline-block;
  margin-bottom: 5px;
}

.title-wrapper h2 {
  font-size: 24px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.title-decoration {
  position: absolute;
  bottom: -5px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #ff6b6b, #4ecdc4);
  border-radius: 3px;
}

.tags-container {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-top: 10px;
}

.support-tag {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 10px;
  font-size: 14px;
  border-radius: 20px;
}

.package-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(280px, 1fr));
  gap: 15px;
  margin: 0 auto;
  width: 100%;
  max-width: 800px;
}

.package-card {
  position: relative;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  padding: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
}

.package-card:active {
  transform: scale(0.98);
}

.package-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.package-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  font-size: 20px;
  background: rgba(255, 255, 255, 0.15);
}

.package-icon.supreme {
  background: linear-gradient(135deg, #ff6b6b, #ffd93d);
}

.package-card h3 {
  margin: 0 0 8px;
  font-size: 22px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: #fff;
}

.features {
  list-style: none;
  padding: 0;
  margin: 8px 0;
  font-size: 16px;
  font-weight: 500;
}

.features li {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  margin-bottom: 6px;
  color: rgba(255, 255, 255, 0.95);
}

.features .el-icon {
  font-size: 20px;
}

.price {
  margin-top: 8px;
  font-size: 28px;
  font-weight: bold;
  color: #ff6b6b;
}

.original {
  font-size: 16px;
  text-decoration: line-through;
  color: rgba(255, 255, 255, 0.7);
  margin-right: 8px;
}

.special {
  color: #ff6b6b !important;
  font-weight: 600;
  font-size: 19px;
}

.title-row {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 8px;
}

.special-tag {
  display: flex;
  align-items: center;
  gap: 2px;
  background: linear-gradient(135deg, #ff6b6b, #ffd93d);
  color: white;
  padding: 2px 8px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(255, 107, 107, 0.3);
}

.special-tag .el-icon {
  font-size: 14px;
}

.bottom-notice {
  text-align: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 12px;
  padding: 10px;
  margin-top: 10px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
  width: 100%;
}

@media (max-width: 768px) {
  .content-wrapper {
    padding: 5px;
    gap: 8px;
  }
  
  .package-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  
  .package-card {
    padding: 10px;
  }

  .package-card h3 {
    font-size: 20px;
    margin: 0 0 6px;
  }
  
  .features {
    font-size: 15px;
    margin: 6px 0;
  }
  
  .features li {
    margin-bottom: 4px;
  }
  
  .price {
    font-size: 24px;
    margin-top: 6px;
  }
  
  .original {
    font-size: 14px;
  }

  .special-tag {
    font-size: 12px;
    padding: 1px 6px;
  }

  .special-tag .el-icon {
    font-size: 12px;
  }

  .bottom-notice {
    font-size: 11px;
    padding: 8px;
    margin-top: 8px;
  }
}
</style> 