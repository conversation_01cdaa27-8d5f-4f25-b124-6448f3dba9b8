<template>
  <div class="oracle-war">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <Connection />
              </el-icon>
              <span>甲骨文战争</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 数据输入区域 -->
          <div class="data-input-section card-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon>
                  <Document />
                </el-icon>
                <span>游戏数据</span>
              </div>
              <div class="section-actions">
                <el-button type="info" size="small" @click="toggleInputCollapse" class="toggle-button">
                  <el-icon>
                    <component :is="isInputCollapsed ? 'ArrowDown' : 'ArrowUp'" />
                  </el-icon>
                  {{ isInputCollapsed ? '展开' : '收起' }}
                </el-button>
                <el-button type="danger" size="small" class="clear-button" @click="handleClearData" :icon="Delete">
                  清空数据
                </el-button>
              </div>
            </div>

            <!-- 大型输入框 - 添加折叠效果 -->
            <div class="input-container" :class="{ 'is-collapsed': isInputCollapsed }" @click="handleContainerClick">
              <el-input v-model="dataInput" type="textarea" :rows="isInputCollapsed ? 1 : 8" placeholder="请输入游戏数据"
                :disabled="loading.parse || loading.upload" resize="none" class="data-textarea"
                @focus="handleInputFocus" />
            </div>

            <!-- 数据结构信息 -->
            <div v-if="dataStructureInfo" class="data-structure-info">
              <el-alert :title="dataStructureInfo" :type="dataStructureType" :closable="false" show-icon />
            </div>

            <!-- 操作按钮 -->
            <div class="action-section">
              <el-button type="primary" :loading="loading.parse" @click="handleParseData" :icon="DataAnalysis"
                class="action-button">
                解析数据
              </el-button>
              <el-button type="success" :loading="loading.upload" @click="handleUpload" :icon="Upload"
                :disabled="!hasData" class="action-button">
                上传数据
              </el-button>
            </div>

            <!-- 套餐快速设置按钮 -->
            <div class="package-buttons" v-if="hasData">
              <el-divider content-position="center">套餐快速设置</el-divider>
              <div class="package-btn-container">
                <el-button type="danger" @click="applyPackageOne" :disabled="!hasData">
                  <el-icon>
                    <Present />
                  </el-icon>
                  套餐一: 元宝20亿 + 神珠999
                </el-button>
                <el-button type="warning" @click="applyPackageTwo" :disabled="!hasData">
                  <el-icon>
                    <GoodsFilled />
                  </el-icon>
                  套餐二: 元宝/神珠 + 练兵符/广告卷
                </el-button>
                <el-button type="primary" @click="applyPackageThree" :disabled="!hasData">
                  <el-icon>
                    <TrophyBase />
                  </el-icon>
                  套餐三: 全满配置 + VIP满级
                </el-button>
              </div>
            </div>
          </div>

          <!-- 数据展示区域 -->
          <div v-if="hasData" class="data-display-section">
            <el-tabs type="border-card" class="data-tabs" @tab-change="handleTabChange"
              :before-leave="handleBeforeTabLeave">
              <!-- 基本信息标签页 -->
              <el-tab-pane label="基本信息">
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="OpenID">{{ gameData.openid }}</el-descriptions-item>
                  <el-descriptions-item label="Session Key">{{ gameData.session_key }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{ gameData.created_at }}</el-descriptions-item>

                  <!-- 只在code字段存在时显示状态码 -->
                  <el-descriptions-item v-if="gameData.code !== undefined" label="状态码">{{ gameData.code
                    }}</el-descriptions-item>
                  <el-descriptions-item v-else label="状态码">-</el-descriptions-item>

                  <!-- 只在success字段存在时显示状态 -->
                  <el-descriptions-item label="状态">
                    <el-tag v-if="gameData.success !== undefined" :type="gameData.success ? 'success' : 'danger'">
                      {{ gameData.success ? '成功' : '失败' }}
                    </el-tag>
                    <el-tag v-else type="success">已解析</el-tag>
                  </el-descriptions-item>

                  <!-- 只在reportbfb字段存在时显示报告率 -->
                  <el-descriptions-item label="报告率">
                    <span v-if="gameData.reportbfb !== undefined">{{ gameData.reportbfb }}%</span>
                    <span v-else>-</span>
                  </el-descriptions-item>
                </el-descriptions>

                <el-divider content-position="left">Token信息</el-divider>
                <div class="token-section">
                  <el-input type="textarea" :rows="2" :model-value="gameData.token" readonly class="token-input" />
                </div>
              </el-tab-pane>

              <!-- 游戏资源标签页 -->
              <el-tab-pane label="游戏资源">
                <el-form label-width="120px" class="resource-form">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="元宝">
                        <el-input-number :model-value="Number(gameData.so_key_yuanbao)"
                          @update:model-value="val => gameData.so_key_yuanbao = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="金币">
                        <el-input-number :model-value="Number(gameData.so_key_gold)"
                          @update:model-value="val => gameData.so_key_gold = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="血量">
                        <el-input-number :model-value="Number(gameData.so_key_hp)"
                          @update:model-value="val => gameData.so_key_hp = String(val)" :min="1"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="经验">
                        <el-input-number :model-value="Number(gameData.so_key_exp)"
                          @update:model-value="val => gameData.so_key_exp = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="衣服碎片">
                        <el-input-number :model-value="Number(gameData.so_key_item3001)"
                          @update:model-value="val => gameData.so_key_item3001 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="复活次数">
                        <el-input-number :model-value="Number(gameData.so_key_fuhuo_time)"
                          @update:model-value="val => gameData.so_key_fuhuo_time = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="神珠">
                        <el-input-number :model-value="Number(gameData.so_key_tianfu)"
                          @update:model-value="val => gameData.so_key_tianfu = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="练兵符（道具4）">
                        <el-input-number :model-value="Number(gameData.so_key_item4)"
                          @update:model-value="val => gameData.so_key_item4 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="广告卷（道具6）">
                        <el-input-number :model-value="Number(gameData.so_key_item6)"
                          @update:model-value="val => gameData.so_key_item6 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="妖王挑战卷（道具7）">
                        <el-input-number :model-value="Number(gameData.so_key_item7)"
                          @update:model-value="val => gameData.so_key_item7 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="每日挑战卷（道具8）">
                        <el-input-number :model-value="Number(gameData.so_key_item8)"
                          @update:model-value="val => gameData.so_key_item8 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="换一批（道具3）">
                        <el-input-number :model-value="Number(gameData.so_key_item3)"
                          @update:model-value="val => gameData.so_key_item3 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 游戏进度标签页 -->
              <el-tab-pane label="游戏进度">
                <el-form label-width="120px" class="progress-form">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="当前章节">
                        <el-input-number :model-value="Number(gameData.so_curr_play_chapter)"
                          @update:model-value="val => gameData.so_curr_play_chapter = String(val)" :min="1"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="最远章节">
                        <el-input-number :model-value="Number(gameData.so_far_play_chapter)"
                          @update:model-value="val => gameData.so_far_play_chapter = String(val)" :min="1"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="当前战斗ID">
                        <el-input v-model="gameData.so_currBattleId" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="最远战斗ID">
                        <el-input v-model="gameData.so_farBattleId" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="游戏天数">
                        <el-input-number :model-value="Number(gameData.so_playdays)"
                          @update:model-value="val => gameData.so_playdays = String(val)" :min="1"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="当前天数">
                        <el-input-number :model-value="Number(gameData.so_playday_now)"
                          @update:model-value="val => gameData.so_playday_now = String(val)" :min="1"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 角色系统标签页 -->
              <el-tab-pane label="角色系统">
                <div v-if="parsedUnlockHero" class="hero-section">
                  <el-divider content-position="left">已解锁角色</el-divider>
                  <div class="table-wrapper">
                    <el-table :data="heroList" :stripe="false" :border="true" :show-header="true"
                      v-loading="!heroList.length" size="small" class="custom-table">
                      <el-table-column prop="id" label="角色ID" min-width="120" />
                      <el-table-column prop="unlocked" label="状态" min-width="100">
                        <template #default="scope">
                          <el-tag :type="scope.row.unlocked === 1 ? 'success' : 'info'">
                            {{ scope.row.unlocked === 1 ? '已解锁' : '未解锁' }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" min-width="100" align="center">
                        <template #default="scope">
                          <el-button size="small" :type="scope.row.unlocked === 1 ? 'danger' : 'success'"
                            @click="toggleHeroUnlock(scope.row)">
                            {{ scope.row.unlocked === 1 ? '锁定' : '解锁' }}
                          </el-button>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
                <el-empty v-else description="无角色数据或格式不正确" />
              </el-tab-pane>

              <!-- 技能系统标签页 -->
              <el-tab-pane label="技能系统">
                <div v-if="parsedShentongLv" class="skill-section">
                  <el-divider content-position="left">神通等级</el-divider>
                  <div class="table-wrapper">
                    <el-table :data="shentongList" :stripe="false" :border="true" :show-header="true"
                      v-loading="!shentongList.length" size="small" class="custom-table">
                      <el-table-column prop="id" label="神通ID" width="120" />
                      <el-table-column prop="level" label="等级" min-width="180">
                        <template #default="scope">
                          <el-input-number :model-value="Number(scope.row.level)"
                            @update:model-value="val => updateShentongLevel({ ...scope.row, level: val })" :min="0"
                            controls-position="right" size="small" />
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
                <el-empty v-else description="无技能数据或格式不正确" />
              </el-tab-pane>

              <!-- 其他属性标签页 -->
              <el-tab-pane label="其他属性">
                <el-form label-width="180px" class="other-form">
                  <el-form-item v-for="(value, key) in otherProperties" :key="key" :label="key">
                    <!-- 根据值的类型选择不同的输入方式 -->
                    <template v-if="typeof value === 'boolean'">
                      <el-select :model-value="String(value)"
                        @update:model-value="val => gameData[key] = val === 'true'" placeholder="请选择">
                        <el-option label="是" value="true" />
                        <el-option label="否" value="false" />
                      </el-select>
                    </template>
                    <template v-else>
                      <el-input v-model="gameData[key]" />
                    </template>
                  </el-form-item>
                </el-form>
              </el-tab-pane>

              <!-- 商店系统标签页 -->
              <el-tab-pane label="商店系统">
                <el-form label-width="180px" class="shop-form">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="广告次数">
                        <el-input-number :model-value="Number(gameData.so_ad_times)"
                          @update:model-value="val => gameData.so_ad_times = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="免费抽卡次数">
                        <el-input-number :model-value="Number(gameData.so_key_free_card)"
                          @update:model-value="val => gameData.so_key_free_card = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="免费抽卡刷新次数">
                        <el-input-number :model-value="Number(gameData.so_free_card_refresh_times)"
                          @update:model-value="val => gameData.so_free_card_refresh_times = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="使用刷新券">
                        <el-input-number :model-value="Number(gameData.so_use_refresh_quan)"
                          @update:model-value="val => gameData.so_use_refresh_quan = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="神秘商店601">
                        <el-input-number :model-value="Number(gameData.so_buy_shenmi_601)"
                          @update:model-value="val => gameData.so_buy_shenmi_601 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="神秘商店606">
                        <el-input-number :model-value="Number(gameData.so_buy_shenmi_606)"
                          @update:model-value="val => gameData.so_buy_shenmi_606 = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="单抽卡时间">
                        <el-input-number :model-value="Number(gameData.so_OneCard_chouka_time)"
                          @update:model-value="val => gameData.so_OneCard_chouka_time = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="最大卡牌数量">
                        <el-input-number :model-value="Number(gameData.so_max_card_num)"
                          @update:model-value="val => gameData.so_max_card_num = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-divider content-position="left">每日商店</el-divider>
                  <div class="table-wrapper">
                    <el-table :data="dailyShopData" :stripe="false" :border="true" :show-header="true"
                      v-loading="loading.parse" size="small" class="custom-table">
                      <el-table-column prop="index" label="商店编号" width="100" />
                      <el-table-column prop="id" label="商店ID" width="150">
                        <template #default="scope">
                          <template v-if="scope.row.editing">
                            <el-input v-model="scope.row.id" size="small" />
                          </template>
                          <template v-else>
                            {{ scope.row.id }}
                          </template>
                        </template>
                      </el-table-column>
                      <el-table-column prop="num" label="数量" width="150">
                        <template #default="scope">
                          <template v-if="scope.row.editing">
                            <el-input v-model="scope.row.num" size="small" />
                          </template>
                          <template v-else>
                            {{ scope.row.num }}
                          </template>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作" width="200" align="center">
                        <template #default="scope">
                          <template v-if="scope.row.editing">
                            <el-button size="small" type="success" @click="handleSave(scope.row)">保存</el-button>
                            <el-button size="small" @click="handleCancel(scope.row)">取消</el-button>
                          </template>
                          <template v-else>
                            <el-button size="small" type="primary" @click="handleEdit(scope.row)">编辑</el-button>
                          </template>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </el-form>
              </el-tab-pane>

              <!-- VIP系统标签页 -->
              <el-tab-pane label="VIP系统">
                <el-form label-width="180px" class="vip-form">
                  <el-divider content-position="left">VIP状态设置</el-divider>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="VIP提示1">
                        <el-select :model-value="String(gameData.so_openviptip1 || '0')"
                          @update:model-value="val => gameData.so_openviptip1 = String(val)" placeholder="请选择">
                          <el-option label="关闭" value="0" />
                          <el-option label="开启" value="1" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="VIP提示2">
                        <el-select :model-value="String(gameData.so_openviptip2 || '0')"
                          @update:model-value="val => gameData.so_openviptip2 = String(val)" placeholder="请选择">
                          <el-option label="关闭" value="0" />
                          <el-option label="开启" value="1" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="VIP等级标记">
                        <el-input-number :model-value="Number(gameData.so_oncevipzero || '0')"
                          @update:model-value="val => gameData.so_oncevipzero = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="每日VIP奖励">
                        <el-select :model-value="String(gameData['3_7_daily_viprewards'] || '0')"
                          @update:model-value="val => gameData['3_7_daily_viprewards'] = String(val)" placeholder="请选择">
                          <el-option label="未领取" value="0" />
                          <el-option label="已领取" value="1" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="总视频观看次数">
                        <el-input-number :model-value="Number(gameData.so_totalvtimes || '0')"
                          @update:model-value="val => gameData.so_totalvtimes = String(val)" :min="0"
                          controls-position="right" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 原始数据标签页 -->
              <el-tab-pane label="原始数据">
                <div class="raw-data">
                  <el-button @click="copyRawData" type="primary" size="small" class="copy-button">
                    <el-icon>
                      <DocumentCopy />
                    </el-icon> 复制数据
                  </el-button>
                  <pre class="json-view">{{ JSON.stringify(gameData, null, 2) }}</pre>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, nextTick, provide } from 'vue'
import { Delete, DataAnalysis, Upload, Document, Connection, DocumentCopy, ArrowDown, ArrowUp, Present, GoodsFilled, TrophyBase } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox, ElDivider, ElAlert } from 'element-plus'

// ================ 完全解决ResizeObserver警告 ================
// 原始错误处理函数
const nativeConsoleError = window.console.error;

// 处理未传递的ResizeObserver通知
const RESIZE_OBSERVER_ERRS = [
  'ResizeObserver loop limit exceeded',
  'ResizeObserver loop completed with undelivered notifications',
];

// 拦截ResizeObserver错误
window.console.error = (...args) => {
  if (
    args.length > 0 &&
    typeof args[0] === 'string' &&
    RESIZE_OBSERVER_ERRS.some(errMsg => args[0].includes(errMsg))
  ) {
    // 不打印ResizeObserver相关错误
    return;
  }
  return nativeConsoleError.apply(window.console, args);
};

// 为Element Plus提供设置，禁用严格模式可能会减少警告
provide('el-table.props.stripe.default', false);
provide('el-table.props.border.default', false);
provide('el-table-column.props.fixed.default', false);

// 动态调整表格
const tableHeightLimit = 400; // 表格最大高度
const adjustTableLayout = () => {
  try {
    const tables = document.querySelectorAll('.el-table__body-wrapper');
    if (tables.length) {
      tables.forEach(table => {
        if (table.scrollHeight > tableHeightLimit) {
          table.style.maxHeight = `${tableHeightLimit}px`;
          table.style.overflowY = 'auto';
        } else {
          table.style.overflowY = 'visible';
        }
      });
    }
  } catch (e) {
    // 忽略错误
  }
};
// ====================================================

// 数据和加载状态
const dataInput = ref('')
const gameData = ref(null)
const loading = ref({
  parse: false,
  upload: false
})

// 数据结构相关信息
const dataStructureInfo = ref('')
const dataStructureType = ref('info')

// 输入框折叠状态
const isInputCollapsed = ref(true)

// 切换输入框折叠状态
const toggleInputCollapse = () => {
  isInputCollapsed.value = !isInputCollapsed.value
}

// 输入框获得焦点时自动展开
const handleInputFocus = () => {
  if (isInputCollapsed.value) {
    isInputCollapsed.value = false
  }
}

// 点击输入容器时展开
const handleContainerClick = () => {
  if (isInputCollapsed.value) {
    isInputCollapsed.value = false
  }
}

// 计算属性 - 是否有数据
const hasData = computed(() => {
  return gameData.value !== null
})

// 计算属性 - 解析角色数据
const parsedUnlockHero = computed(() => {
  if (!gameData.value?._so_unlock_hero) return false
  try {
    return typeof JSON.parse(gameData.value._so_unlock_hero) === 'object'
  } catch (e) {
    return false
  }
})

// 计算属性 - 角色列表
const heroList = computed(() => {
  if (!parsedUnlockHero.value) return []

  const unlockHero = JSON.parse(gameData.value._so_unlock_hero)
  return Object.entries(unlockHero).map(([id, unlocked]) => ({
    id,
    unlocked
  }))
})

// 计算属性 - 解析神通等级数据
const parsedShentongLv = computed(() => {
  if (!gameData.value?._so_shentong_lv) return false
  try {
    return typeof JSON.parse(gameData.value._so_shentong_lv) === 'object'
  } catch (e) {
    return false
  }
})

// 计算属性 - 神通等级列表
const shentongList = computed(() => {
  if (!parsedShentongLv.value) return []

  try {
    const shentongLvObj = JSON.parse(gameData.value._so_shentong_lv)
    return Object.entries(shentongLvObj).map(([id, level]) => ({
      id,
      level: typeof level === 'string' ? Number(level) : level
    }))
  } catch (e) {
    console.error('解析神通等级数据失败:', e)
    return []
  }
})

// 计算属性 - 每日商店数据
const dailyShopData = computed(() => {
  if (!gameData.value) return []

  const shopData = []
  const idPattern = /^3_7_daily_dshop_id(\d+)$/

  try {
    // 遍历所有属性
    Object.entries(gameData.value).forEach(([key, value]) => {
      const idMatch = key.match(idPattern)
      if (idMatch) {
        const index = idMatch[1]
        const numKey = `3_7_daily_dshop_num${index}`
        shopData.push({
          index,
          id: value,
          num: gameData.value[numKey] || '0',
          editing: false
        })
      }
    })

    // 添加调试信息
    console.log(`找到 ${shopData.length} 个商店数据项`)

    // 处理没有找到商店数据的情况，添加一个默认空项
    if (shopData.length === 0) {
      shopData.push({
        index: '101',
        id: '',
        num: '0',
        editing: false
      })
    }

    // 按index排序
    return shopData.sort((a, b) => Number(a.index) - Number(b.index))
  } catch (error) {
    console.error('处理商店数据出错:', error)
    // 出错时返回空数组，但不显示加载状态
    return []
  }
})

// 计算属性 - 其他属性（排除已经在特定标签页中显示的属性）
const otherProperties = computed(() => {
  if (!gameData.value) return {}

  // 已在其他标签页中显示的属性
  const excludedKeys = [
    'openid', 'session_key', 'created_at', 'token', 'sodata',
    'so_key_yuanbao', 'so_key_gold', 'so_key_hp', 'so_key_exp',
    'so_key_item3001', 'so_key_fuhuo_time', 'so_key_tianfu',
    'so_key_item4', 'so_key_item6', 'so_key_item7', 'so_key_item8', 'so_key_item3',
    'so_curr_play_chapter', 'so_far_play_chapter',
    'so_currBattleId', 'so_farBattleId',
    'so_playdays', 'so_playday_now',
    '_so_unlock_hero', '_so_shentong_lv',
    'code', 'success', 'reportbfb', 'updatetime',
    // 商店系统相关属性
    'so_ad_times', 'so_key_free_card', 'so_free_card_refresh_times',
    'so_use_refresh_quan', 'so_buy_shenmi_601', 'so_buy_shenmi_606',
    'so_OneCard_chouka_time', 'so_max_card_num',
    // 每日商店相关属性
    ...Object.keys(gameData.value).filter(key =>
      key.startsWith('3_7_daily_dshop_id') ||
      key.startsWith('3_7_daily_dshop_num')
    ),
    // VIP系统相关属性
    'so_openviptip1', 'so_openviptip2', 'so_oncevipzero', '3_7_daily_viprewards', 'so_totalvtimes'
  ]

  // 过滤属性
  const filtered = {}
  Object.entries(gameData.value).forEach(([key, value]) => {
    if (!excludedKeys.includes(key)) {
      filtered[key] = value
    }
  })

  return filtered
})

// 在 script setup 部分添加新的响应式变量
const editingShopData = ref([])

// 辅助函数 - 设置游戏数据属性（如果不存在则创建）
const setGameDataProperty = (key, value) => {
  if (typeof gameData.value !== 'object' || gameData.value === null) {
    gameData.value = {}
  }
  gameData.value[key] = value
}

// 方法 - 切换角色解锁状态
const toggleHeroUnlock = (hero) => {
  try {
    // 解析现有数据
    const unlockHero = JSON.parse(gameData.value._so_unlock_hero)

    // 切换状态
    unlockHero[hero.id] = hero.unlocked === 1 ? 0 : 1

    // 更新状态
    hero.unlocked = unlockHero[hero.id]

    // 将更新后的数据序列化回去
    gameData.value._so_unlock_hero = JSON.stringify(unlockHero)

    ElMessage.success(`角色 ${hero.id} ${hero.unlocked === 1 ? '已解锁' : '已锁定'}`)
  } catch (error) {
    console.error('更新角色解锁状态失败:', error)
    ElMessage.error('更新角色解锁状态失败')
  }
}

// 方法 - 更新神通等级
const updateShentongLevel = (shentong) => {
  try {
    // 解析现有数据
    const shentongLvObj = JSON.parse(gameData.value._so_shentong_lv)

    // 更新等级
    shentongLvObj[shentong.id] = shentong.level

    // 将更新后的数据序列化回去
    gameData.value._so_shentong_lv = JSON.stringify(shentongLvObj)

    ElMessage.success(`神通 ${shentong.id} 等级已更新为 ${shentong.level}`)
  } catch (error) {
    console.error('更新神通等级失败:', error)
    ElMessage.error('更新神通等级失败')
  }
}

// 方法 - 复制原始数据
const copyRawData = () => {
  try {
    const jsonStr = JSON.stringify(gameData.value, null, 2)
    navigator.clipboard.writeText(jsonStr)
    ElMessage.success('数据已复制到剪贴板')
  } catch (error) {
    console.error('复制数据失败:', error)
    ElMessage.error('复制数据失败')
  }
}

// 方法 - 清空数据
const handleClearData = () => {
  dataInput.value = ''
  gameData.value = null
  ElMessage.success('数据已清空')
}

// 方法 - 解析数据
const handleParseData = async () => {
  if (!dataInput.value.trim()) {
    ElMessage.warning('请先输入游戏数据')
    return
  }

  try {
    loading.value.parse = true
    // 重置数据结构信息
    dataStructureInfo.value = ''

    // 先清空当前数据，避免新旧数据冲突
    gameData.value = null

    // 给UI一点时间更新
    await new Promise(resolve => setTimeout(resolve, 100))

    // 解析JSON数据
    let jsonData
    try {
      jsonData = JSON.parse(dataInput.value)
    } catch (error) {
      ElMessage.error('JSON格式错误，请检查输入的数据格式')
      console.error('JSON解析错误:', error)
      loading.value.parse = false
      dataStructureInfo.value = 'JSON格式错误，请检查输入的数据格式'
      dataStructureType.value = 'error'
      return
    }

    // 准备标准化的数据结构
    let standardizedData = { ...jsonData }

    // 如果有嵌套的sodata字段，提取所需信息后移除
    if (standardizedData.sodata) {
      // 提取一些可能在嵌套中的字段，但仅当顶层没有时才使用
      const necessaryKeys = ['_so_shentong_lv', '_so_unlock_hero', 'token', 'openid', 'session_key', 'created_at']

      necessaryKeys.forEach(key => {
        if (!standardizedData[key] && standardizedData.sodata[key]) {
          standardizedData[key] = standardizedData.sodata[key]
        }
      })

      // 保留对sodata的引用，但创建新结构
      const originalSodata = standardizedData.sodata

      // 移除嵌套的sodata，避免循环引用和重复数据
      delete standardizedData.sodata

      // 创建一个新的sodata对象，仅包含必要的信息
      standardizedData.sodata = {
        // 保留必要的标识字段，用于数据追踪
        _id: originalSodata._id,
        so_sindex: originalSodata.so_sindex
      }

      dataStructureInfo.value = '数据已优化，仅使用顶层属性'
      dataStructureType.value = 'success'
    } else {
      dataStructureInfo.value = '已使用顶层数据'
      dataStructureType.value = 'success'
    }

    // 确保数字类型的字段在UI中正确显示
    Object.keys(standardizedData).forEach(key => {
      if (key !== 'sodata' && typeof standardizedData[key] === 'number') {
        standardizedData[key] = String(standardizedData[key])
      }
    })

    // 暂停DOM观察，避免大量DOM更新触发ResizeObserver错误
    const disableMutationTemporarily = () => {
      // 临时禁用ResizeObserver警告
      const originalObserver = window.ResizeObserver;
      window.ResizeObserver = class MockResizeObserver {
        constructor(callback) {
          this.callback = callback;
        }
        observe() { }
        unobserve() { }
        disconnect() { }
      };

      // 稍后恢复
      setTimeout(() => {
        window.ResizeObserver = originalObserver;
      }, 1000);
    };

    // 禁用ResizeObserver
    disableMutationTemporarily();

    // 保存解析后的数据，使用setTimeout确保DOM更新顺序
    setTimeout(() => {
      // 设置数据
      gameData.value = standardizedData;

      // 等待DOM更新
      setTimeout(() => {
        // 触发表格重新计算
        adjustTableLayout();

        // 完成加载
        loading.value.parse = false;
        ElMessage.success('数据解析成功');

        // 解析成功后自动折叠输入框
        setTimeout(() => {
          isInputCollapsed.value = true;
        }, 500);
      }, 300);
    }, 200);
  } catch (error) {
    console.error('解析数据出错:', error)
    ElMessage.error('解析数据失败，请检查数据格式')
    dataStructureInfo.value = `解析出错: ${error.message || '未知错误'}`
    dataStructureType.value = 'error'
    loading.value.parse = false
  }
}

// 方法 - 上传数据
const handleUpload = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先解析数据')
    return
  }

  try {
    loading.value.upload = true

    // 尝试上传，最多重试3次
    let maxRetries = 3
    let currentRetry = 0
    let success = false

    while (!success && currentRetry < maxRetries) {
      // 创建完整的上传数据对象的深拷贝，防止修改原数据
      const uploadData = JSON.parse(JSON.stringify(gameData.value))

      // 确保顶层的so_sindex存在且增加
      let topLevelSIndex = parseInt(uploadData.so_sindex) || 0
      // 增加重试次数计算新索引
      const newTopLevelSIndex = String(topLevelSIndex + 1 + currentRetry)
      uploadData.so_sindex = newTopLevelSIndex

      // 更新时间戳
      uploadData.updatetime = Date.now()

      console.log('准备上传数据:', uploadData)

      // 发送数据到服务器
      const response = await fetch('https://j796tcgy43.hzh.sealos.run/setso', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': '*/*',
        },
        body: JSON.stringify(uploadData)
      })

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`)
      }

      // 解析响应
      const result = await response.json()
      console.log('上传响应:', result)

      // 如果响应值是{data: -1}，则继续重试
      if (result && result.data === -1) {
        currentRetry++
        ElMessage.info(`索引冲突，正在重试(${currentRetry}/${maxRetries})...`)
      } else {
        // 其他响应视为成功
        success = true

        // 成功后更新本地数据中的so_sindex值
        gameData.value.so_sindex = newTopLevelSIndex

        // 更新本地数据的updatetime
        gameData.value.updatetime = uploadData.updatetime
      }
    }

    if (success) {
      ElMessage.success('数据上传成功')

      // 如果用户有输入新数据，更新输入框内容为最新的完整数据
      if (dataInput.value) {
        dataInput.value = JSON.stringify(gameData.value, null, 2)
        ElMessage.info('游戏数据已更新')
      }
    } else {
      ElMessage.error(`上传失败，已尝试${maxRetries}次`)
    }
  } catch (error) {
    console.error('上传数据出错:', error)
    ElMessage.error(`上传数据失败: ${error.message || '请检查网络连接'}`)
  } finally {
    loading.value.upload = false
  }
}

// 方法 - 标签切换前处理
const handleBeforeTabLeave = (activeName, oldActiveName) => {
  // 临时禁用ResizeObserver
  const disableResizeObserver = () => {
    const originalObserver = window.ResizeObserver;
    window.ResizeObserver = class MockResizeObserver {
      observe() { }
      unobserve() { }
      disconnect() { }
    };

    // 在DOM更新后恢复
    setTimeout(() => {
      window.ResizeObserver = originalObserver;
    }, 500);
  };

  // 在标签切换前临时禁用ResizeObserver
  disableResizeObserver();

  return true; // 允许标签切换
}

// 处理标签页切换，防止ResizeObserver错误
const handleTabChange = (tab) => {
  // 暂时隐藏表格
  const tables = document.querySelectorAll('.custom-table');
  tables.forEach(table => {
    table.style.visibility = 'hidden';
  });

  // 延迟触发resize事件，让DOM完全渲染
  setTimeout(() => {
    // 恢复表格可见性
    tables.forEach(table => {
      table.style.visibility = 'visible';
    });

    window.dispatchEvent(new Event('resize'));
    adjustTableLayout(); // 调整表格布局
  }, 300);
}

// 添加编辑相关方法
const handleEdit = (row) => {
  row.editing = true
}

const handleSave = (row) => {
  // 更新游戏数据
  const idKey = `3_7_daily_dshop_id${row.index}`
  const numKey = `3_7_daily_dshop_num${row.index}`

  gameData.value[idKey] = String(row.id)
  gameData.value[numKey] = String(row.num)

  row.editing = false
  ElMessage.success('保存成功')
}

const handleCancel = (row) => {
  // 恢复原始数据
  const idKey = `3_7_daily_dshop_id${row.index}`
  const numKey = `3_7_daily_dshop_num${row.index}`

  row.id = gameData.value[idKey]
  row.num = gameData.value[numKey] || '0'
  row.editing = false
}

// 方法 - 应用套餐一
const applyPackageOne = () => {
  if (!hasData.value) {
    ElMessage.warning('请先解析数据')
    return
  }

  try {
    // 设置元宝为20亿
    setGameDataProperty('so_key_yuanbao', '2000000000')

    // 设置神珠为999
    setGameDataProperty('so_key_tianfu', '999')

    ElMessage.success('套餐一已应用: 元宝20亿 + 神珠999')
  } catch (error) {
    console.error('应用套餐一失败:', error)
    ElMessage.error('应用套餐一失败')
  }
}

// 方法 - 应用套餐二
const applyPackageTwo = () => {
  if (!hasData.value) {
    ElMessage.warning('请先解析数据')
    return
  }

  try {
    // 设置元宝为20亿
    setGameDataProperty('so_key_yuanbao', '2000000000')

    // 设置神珠为999
    setGameDataProperty('so_key_tianfu', '999')

    // 设置练兵符为99,999
    setGameDataProperty('so_key_item4', '99999')

    // 设置广告卷为9,999
    setGameDataProperty('so_key_item6', '9999')

    ElMessage.success('套餐二已应用: 元宝20亿 + 神珠999 + 练兵符99,999 + 广告卷9,999')
  } catch (error) {
    console.error('应用套餐二失败:', error)
    ElMessage.error('应用套餐二失败')
  }
}

// 方法 - 应用套餐三
const applyPackageThree = () => {
  if (!hasData.value) {
    ElMessage.warning('请先解析数据')
    return
  }

  try {
    // 设置元宝为20亿
    setGameDataProperty('so_key_yuanbao', '2000000000')

    // 设置神珠为999
    setGameDataProperty('so_key_tianfu', '999')

    // 设置练兵符为99,999
    setGameDataProperty('so_key_item4', '99999')

    // 设置广告卷为9,999
    setGameDataProperty('so_key_item6', '9999')

    // 设置妖王挑战卷为9,999
    setGameDataProperty('so_key_item7', '9999')

    // 设置每日挑战卷为9,999
    setGameDataProperty('so_key_item8', '9999')

    // 设置换一批为9,999
    setGameDataProperty('so_key_item3', '9999')

    // 设置总视频观看次数为6999（VIP满级）
    setGameDataProperty('so_totalvtimes', '6999')

    ElMessage.success('套餐三已应用: 元宝20亿 + 神珠999 + 练兵符/广告卷/挑战卷/换一批 + VIP满级')
  } catch (error) {
    console.error('应用套餐三失败:', error)
    ElMessage.error('应用套餐三失败')
  }
}

// 在组件挂载时添加错误处理
onMounted(() => {
  // 设置MutationObserver来监听DOM变化
  const observer = new MutationObserver(() => {
    adjustTableLayout();
  });

  observer.observe(document.body, {
    childList: true,
    subtree: true
  });

  // 触发一次resize事件，帮助表格重新计算尺寸
  nextTick(() => {
    window.dispatchEvent(new Event('resize'));
    adjustTableLayout();
  });

  // 添加窗口大小改变事件监听
  window.addEventListener('resize', adjustTableLayout);
})

// 在组件卸载前清理
onBeforeUnmount(() => {
  // 恢复原始console.error
  if (nativeConsoleError && typeof nativeConsoleError === 'function') {
    window.console.error = nativeConsoleError;
  }

  // 移除事件监听器
  window.removeEventListener('resize', adjustTableLayout);
})
</script>

<style scoped>
.oracle-war {
  padding: 20px;
}

.main-card {
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.title .el-icon {
  margin-right: 8px;
  font-size: 20px;
  color: var(--el-color-primary);
}

.status-tag {
  font-size: 12px;
  padding: 0 8px;
}

.card-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.section-actions {
  display: flex;
  gap: 8px;
}

.section-title .el-icon {
  margin-right: 8px;
  font-size: 18px;
  color: var(--el-color-primary);
}

.input-container {
  transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1);
  overflow: hidden;
  max-height: 300px;
}

.input-container.is-collapsed {
  max-height: 40px;
  opacity: 0.8;
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}

.input-container.is-collapsed:hover {
  opacity: 1;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.data-textarea {
  margin-bottom: 15px;
  transition: all 0.3s ease;
}

.data-structure-info {
  margin: 10px 0;
}

:deep(.el-alert) {
  margin-bottom: 10px;
  border-radius: 4px;
}

:deep(.el-alert__title) {
  font-size: 13px;
  line-height: 1.4;
}

:deep(.el-alert__icon) {
  font-size: 16px;
  margin-right: 8px;
}

:deep(.el-textarea__inner) {
  font-family: monospace;
  border-radius: 4px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.6;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color);
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:focus) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.toggle-button {
  display: flex;
  align-items: center;
}

.toggle-button .el-icon {
  margin-right: 4px;
}

.action-section {
  display: flex;
  gap: 15px;
  margin-top: 15px;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
}

.data-display-section {
  margin-top: 20px;
  padding: 15px;
  background-color: var(--el-bg-color-page);
  border-radius: 4px;
  border: 1px solid var(--el-border-color-light);
  overflow: hidden;
  /* 防止溢出 */
}

.data-tabs {
  box-shadow: none;
  border: none;
}

:deep(.el-tabs__content) {
  padding: 20px;
  overflow: hidden;
  /* 防止内容溢出 */
}

:deep(.el-tabs__nav) {
  position: relative;
  z-index: 10;
}

.token-section {
  margin-top: 15px;
}

.token-input {
  font-family: monospace;
  font-size: 13px;
}

.resource-form,
.progress-form {
  margin-top: 20px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-input-number) {
  width: 100%;
}

.hero-section,
.skill-section {
  margin-top: 10px;
}

.table-wrapper {
  max-height: 400px;
  overflow: auto;
  position: relative;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-top: 20px;
}

:deep(.custom-table) {
  margin-top: 0 !important;
  width: 100%;
  border: none !important;
  border-radius: 0 !important;
  background-color: transparent;
  table-layout: fixed !important;
  /* 强制使用固定表格布局 */
}

:deep(.custom-table th.el-table__cell),
:deep(.custom-table td.el-table__cell) {
  text-align: center !important;
}

:deep(.el-table__body),
:deep(.el-table__header) {
  width: 100% !important;
  table-layout: fixed !important;
  /* 强制使用固定表格布局 */
}

.other-form {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 15px;
}

.raw-data {
  position: relative;
}

.copy-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

.package-buttons {
  margin-top: 20px;
}

.package-btn-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: center;
}

.package-btn-container .el-button {
  min-width: 240px;
  margin: 5px;
  padding: 12px 20px;
  font-weight: 600;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s;
}

.package-btn-container .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.package-btn-container .el-icon {
  font-size: 18px;
}

.json-view {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.6;
  white-space: pre-wrap;
  overflow-x: auto;
  margin-top: 10px;
  border: 1px solid #e9ecef;
  max-height: 600px;
  overflow-y: auto;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .action-section {
    flex-direction: column;
  }

  .action-button {
    width: 100%;
  }
}

:deep(.el-tabs__item) {
  padding: 0 16px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
}

:deep(.el-tabs__header) {
  margin-bottom: 0;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-divider__text) {
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
  font-weight: 600;
}

/* 防止ResizeObserver错误的关键样式 */
:deep(.el-table--scrollable-x .el-table__body-wrapper),
:deep(.el-table--scrollable-y .el-table__body-wrapper) {
  overflow: visible !important;
}

:deep(.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell) {
  background-color: #f5f7fa !important;
}

:deep(.el-table__empty-block) {
  min-height: 60px;
  text-align: center;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

:deep(.el-table__empty-text) {
  line-height: 60px;
  width: 50%;
  color: #909399;
}

/* 表格样式调整 */
:deep(.el-table) {
  table-layout: fixed !important;
  margin-top: 10px;
  border-radius: 4px;
  overflow: hidden;
}
</style>