export class NumberEncoder {
  static random(min, max) {
    return Math.floor(Math.random() * (max - min + 1) + min);
  }

  static _getCharByDigit(digit, base) {
    digit += base;
    return String.fromCharCode(digit);
  }

  static _getRandomCharStr(length) {
    let result = "";
    for (let i = 0; i < length; i++) {
      let n = this.random(1, 9);
      result += this._getCharByDigit(n, 70);
    }
    return result;
  }

  static encodeNumber(value) {
    if (value == null || value < 0) {
      return value;
    }
    
    // 随机生成1-5的数e
    let e = this.random(1, 5);
    // 随机生成1-9的数i
    let i = this.random(1, 9);
    
    // 计算过程
    value *= e;
    value += i;
    value *= 10;
    value += e;
    value *= 10;
    value += i;
    
    return value;
  }

  static encodeIntNumber(value, salt) {
    // 随机生成2-9的数i和n
    let i = this.random(2, 9);
    let n = this.random(2, 9);
    
    // 计算过程
    value += salt;
    value += i;
    value *= 3;
    value -= n;
    
    // 生成字符串
    return this._getCharByDigit(i, 70) + 
           this._getCharByDigit(n, 80) + 
           this._getRandomCharStr(i) + 
           (value + salt) + 
           this._getRandomCharStr(n);
  }

  // 添加解码方法
  static decodeNumber(t) {
    if (null == t || t < 100) {
      console.log('Invalid value for decodeNumber:', t);
      return 0;
    }
    
    const e = t % 10;        // 获取个位数
    t = Math.floor(t/10);    // 去掉个位
    
    const i = t % 10;        // 获取十位数
    t = Math.floor(t/10);    // 去掉十位
    
    t -= e;                  // 减去个位数
    if(!t) {
      console.log('Zero value after processing in decodeNumber');
      return 0;
    }
    
    t /= i;                  // 除以十位数
    const result = Math.floor(t);
    
    console.log('decodeNumber process:', {
      input: t,
      e: e,
      i: i,
      result: result
    });
    
    return result;
  }

  static _getDigitByChar(char, base) {
    const charCode = char.charCodeAt(0);
    return base ? charCode - base : charCode % 10;
  }

  static decodeIntNumber(str, salt) {
    if (!str || str.length <= 2) {
      console.log('Invalid string for decodeIntNumber:', str);
      return NaN;
    }

    const i = str[0];
    const n = str[1];
    const r = this._getDigitByChar(i, 70);
    const o = this._getDigitByChar(n, 80);
    
    let a = str.slice(r + 2, str.length - o);
    console.log('Extracted value:', {
      str: str,
      i: i,
      n: n,
      r: r,
      o: o,
      a: a
    });

    a = parseInt(a);
    if (!a) {
      console.log('Failed to parse number in decodeIntNumber');
      return 0;
    }

    a -= salt;
    a += o;
    a /= 3;
    a -= r;
    a -= salt;
    
    const result = Math.floor(a);
    console.log('decodeIntNumber result:', {
      input: str,
      salt: salt,
      result: result
    });
    
    return result;
  }

  // 添加编码数据的方法
  static encodeIntData(value, key) {
    const salt = this.getDataKey(key);
    return {
      v: this.encodeNumber(value),
      k: this.encodeIntNumber(value, salt)
    };
  }

  // 添加解码数据的方法
  static decodeIntData(data, key, checkK = true, defaultValue = 0) {
    try {
      const salt = this.getDataKey(key);
      if (!data || !data.v || !data.k) {
        console.warn('Invalid data format:', data);
        return defaultValue;
      }

      const decodedV = this.decodeNumber(data.v);
      console.log('Decoded V:', {
        original: data.v,
        decoded: decodedV,
        key: key,
        salt: salt
      });
      
      if (checkK) {
        const decodedK = this.decodeIntNumber(data.k, salt);
        console.log('Decoded K:', {
          original: data.k,
          decoded: decodedK,
          key: key,
          salt: salt
        });

        if (decodedV !== decodedK) {
          console.warn('Data tampering detected:', {
            v: decodedV,
            k: decodedK,
            key: key,
            salt: salt,
            originalData: data
          });
          // 在检测到不一致时,返回 v 值而不是默认值
          return decodedV;
        }
      }
      
      return decodedV;
    } catch (error) {
      console.error('Error decoding data:', error, data);
      return defaultValue;
    }
  }

  // 添加设置数据的方法
  static setIntData(data, key, value, salt, save = false) {
    if (!data[key] || save) {
      // 没有数据或强制保存时,直接设置新值
      data[key] = this.encodeIntData(value, salt);
    } else {
      // 有数据时,先验证再更新
      const currentData = data[key];
      const decodedV = this.decodeNumber(currentData.v);
      const decodedK = this.decodeIntNumber(currentData.k, salt);
      
      if (decodedV !== decodedK) {
        // 数据被篡改,只更新v值保持k不变
        console.warn('Data tampering detected when setting:', key, {
          v: decodedV,
          k: decodedK
        });
        data[key] = {
          v: this.encodeNumber(value),
          k: currentData.k
        };
      } else {
        // 数据正常,更新v和k
        data[key] = this.encodeIntData(value, salt);
      }
    }
    
    return data[key];
  }

  // 添加获取数据的方法
  static getIntData(data, key, defaultValue = 0, salt, checkK = true) {
    const value = data[key];
    if (!value) {
      data[key] = this.encodeIntData(defaultValue, salt);
      return defaultValue;
    }
    return this.decodeIntData(value, salt, checkK, defaultValue);
  }

  // 添加 getDataKey 方法
  static getDataKey(t) {
    const keyMap = {
      // 基础数据
      data_version: 0,
      module_version: 1,
      timestamp: 2,
      is_locked: 3,
      
      // 能量相关
      energy: 4,
      max_energy: 5,
      timer_start_time: 6,
      
      // 兑换次数
      daily_exchange_times_gem: 7,
      daily_exchange_times_video: 8,
      
      // 原有的映射
      ad_ticket: 6,
      card_1: 7,
      card_2: 8,
      card_3: 9,
      card_4: 10,
      card_5: 11,
      card_6: 12,
      card_7: 13,
      card_8: 14,
      card_9: 15,
      card_10: 16,
      card_11: 17,
      card_12: 18,
      coin: 4,
      gem: 5,
      
      // 其他映射
      b_ad: 6,
      bi: 8,
      brt: 4,
      cc: 9,
      dl: 5,
      drc_ad: 7,
      rc: 10,
      sl: 11
    };
    
    // 添加调试日志
    console.log('Getting key for:', t, 'Value:', keyMap[t]);
    
    // 如果找不到映射，返回 null
    if (!(t in keyMap)) {
      console.warn('No mapping found for key:', t);
    }
    
    return keyMap[t] || null;
  }

  // 添加生成更新数据的方法
  static generateUpdateData(originalData, moduleData) {
    const result = { ...originalData };
    
    // 遍历所有模块数据
    Object.entries(moduleData).forEach(([manager, data]) => {
      if (!data || typeof data !== 'object') return;
      
      // 遍历模块中的每个数据项
      Object.entries(data).forEach(([key, value]) => {
        if (!value || typeof value !== 'object' || !('displayValue' in value)) return;
        
        // 如果值被修改了,重新生成加密数据
        if (value.displayValue !== value.originalValue) {
          const salt = this.getDataKey(key);
          const newData = {
            v: this.encodeNumber(value.displayValue),
            k: this.encodeIntNumber(value.displayValue, salt)
          };
          
          // 更新数据
          if (!result.d[manager]) result.d[manager] = {};
          result.d[manager][key] = newData;
        }
      });
    });
    
    return result;
  }
}
