<template>
  <div class="package-table">
    <h1 class="table-title">小小庇护所/小小生存者小程序保存进度</h1>
    
    <!-- 套餐1 -->
    <div class="package-row">
      <div class="package-name blue">
        <h2>庇护套餐1</h2>
        <div class="tag">基础套餐</div>
      </div>
      <div class="package-content">
        <div class="feature single-line">
          <span class="emoji">💰</span> 无限金币
          <span class="divider">|</span>
          <span class="emoji">💎</span> 无限钻石
        </div>
      </div>
      <div class="package-price">
        <span class="price-tag">10元</span>
      </div>
    </div>

    <!-- 套餐2 -->
    <div class="package-row">
      <div class="package-name green">
        <h2>庇护套餐2</h2>
        <div class="tag">进阶套餐</div>
      </div>
      <div class="package-content">
        <div class="feature single-line">
          <span class="emoji">💰</span> 无限金币
          <span class="divider">|</span>
          <span class="emoji">💎</span> 无限钻石
          <span class="divider">|</span>
          <span class="emoji">🎁</span> 无限免广卷礼盒
        </div>
      </div>
      <div class="package-price">
        <span class="price-tag">15元</span>
      </div>
    </div>

    <!-- 套餐3 -->
    <div class="package-row">
      <div class="package-name orange">
        <h2>庇护套餐3</h2>
        <div class="tag">豪华套餐</div>
      </div>
      <div class="package-content">
        <div class="feature single-line">
          <span class="emoji">💰</span> 无限金币
          <span class="divider">|</span>
          <span class="emoji">💎</span> 无限钻石
          <span class="divider">|</span>
          <span class="emoji">🎁</span> 无限免广卷礼盒
          <span class="divider">|</span>
          <span class="emoji">🎭</span> 无限英雄碎片
        </div>
      </div>
      <div class="package-price">
        <span class="price-tag">20元</span>
      </div>
    </div>

    <!-- 修改好评送体力部分的内容 -->
    <div class="package-row">
      <div class="package-name purple">
        <h2>好评送体力</h2>
        <div class="tag">限时活动</div>
      </div>
      <div class="package-content">
        <div class="feature single-line">
          <span class="emoji">⚡</span> 送体力值(200点) 
          <span class="divider">|</span>
          <span class="emoji">⭐</span> 好评截图
        </div>
      </div>
      <div class="package-price">
        <span class="price-tag free">免费</span>
      </div>
    </div>

    <!-- 修改免责声明部分 -->
    <div class="disclaimer-info">
      <p class="warning">⚠️ 充值不保证游戏以后是否查封,介意勿拍!!! | 封号可以换号补充或者换店铺游戏</p>
    </div>

    <!-- 服务说明 -->
    <div class="service-info">
      <h3>💫 服务说明</h3>
      <ul>
        <li>✅ 即时到账，无需等待</li>
        <li>✅ 安全加密，账号无忧</li>
        <li>✅ 专业客服，售后保障</li>
      </ul>
    </div>

    <!-- 联系方式 -->
    <div class="contact-info">
      <p>📞 客服咨询：请联系淘宝客服</p>
      <p>⏰ 服务时间：9:00-22:00</p>
    </div>
  </div>
</template>

<style scoped>
.package-table {
  max-width: 600px;
  margin: 0 auto;
  padding: 15px;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.table-title {
  text-align: center;
  color: #333;
  font-size: 1.3em;
  margin-bottom: 2px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  line-height: 1.4;
}

.package-row {
  display: flex;
  margin-bottom: 8px;
  border: 1px solid #eee;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  align-items: stretch;
}

.package-name {
  width: 120px;
  padding: 15px;
  color: white;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
}

.package-name h2 {
  margin: 0;
  font-size: 1.1em;
}

.tag {
  font-size: 0.8em;
  padding: 3px 8px;
  background: rgba(255,255,255,0.2);
  border-radius: 12px;
  margin-top: 5px;
}

.blue { background: linear-gradient(135deg, #36D1DC, #5B86E5); }
.green { background: linear-gradient(135deg, #11998e, #38ef7d); }
.orange { background: linear-gradient(135deg, #FF8008, #FFC837); }
.purple { background: linear-gradient(135deg, #834d9b, #d04ed6); }

.package-content {
  flex: 1;
  padding: 15px;
  background: white;
}

.feature {
  margin-bottom: 8px;
  font-size: 0.95em;
  color: #333;
  display: flex;
  align-items: center;
}

.feature:last-child {
  margin-bottom: 0;
}

.emoji {
  margin-right: 8px;
  font-size: 1.2em;
}

.service-info {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.service-info h3 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 1.1em;
}

.service-info ul {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.service-info li {
  margin-bottom: 5px;
  color: #666;
  font-size: 0.9em;
}

.contact-info {
  margin-top: 20px;
  padding: 15px;
  background: #fff;
  border: 1px solid #eee;
  border-radius: 8px;
  text-align: center;
}

.contact-info p {
  margin: 5px 0;
  color: #666;
  font-size: 0.9em;
}

.package-price {
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-left: 1px solid #eee;
  padding: 10px;
}

.price-tag {
  font-size: 1.1em;
  font-weight: bold;
  color: #ff6b6b;
  background: white;
  padding: 5px 10px;
  border-radius: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.price-tag.free {
  color: #38ef7d;
}

/* 调整免责声明样式 */
.disclaimer-info {
  margin-top: 15px;
  padding: 12px;
  background: #fff3f3;
  border: 1px solid #ffebeb;
  border-radius: 8px;
  text-align: center;
}

.warning {
  margin: 5px 0;
  color: #ff4d4f;
  font-size: 0.9em;
  font-weight: 500;
  white-space: nowrap; /* 确保文本不换行 */
  overflow: auto; /* 在小屏幕上允许横向滚动 */
}

/* 添加单行布局相关样式 */
.single-line {
  display: flex;
  align-items: center;
  flex-wrap: nowrap;
  gap: 8px;
  margin-bottom: 0;
  padding: 8px 0;
}

.divider {
  color: #ddd;
  margin: 0 4px;
}

/* 调整移动端显示 */
@media (max-width: 480px) {
  .package-row {
    flex-direction: column;
  }
  
  .package-name {
    width: 100%;
    padding: 10px;
  }
  
  .package-content {
    padding: 12px;
  }

  .package-price {
    width: 100%;
    padding: 10px;
    border-left: none;
    border-top: 1px solid #eee;
  }

  .table-title {
    font-size: 1.1em;
    padding: 10px;
  }

  .single-line {
    flex-wrap: wrap;
    justify-content: center;
    text-align: center;
    gap: 12px;
    padding: 8px;
  }
  
  .divider {
    display: none;  /* 在移动端隐藏分隔符 */
  }

  .feature {
    margin-bottom: 0;  /* 移除多余的下边距 */
  }
}
</style> 