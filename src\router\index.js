import { createRouter, createWebHashHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'
import RechargeManagement from '../views/RechargeManagement.vue'
import UserInformation from '../views/UserInformation.vue'
import Base64Decoder from '@/components/Base64Decoder.vue'
import BubbleGame from '../views/BubbleGame.vue'
import UsedCarGame from '@/views/UsedCarGame.vue'
import HappyMatch from '../views/HappyMatch.vue'
import CantBeatMe from '@/views/CantBeatMe.vue'
import CrazyBlacksmith from '../views/CrazyBlacksmith.vue'
import GunBattle from '../views/GunBattle.vue'
import WhoBeatMe from '@/views/WhoBeatMe.vue'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: Dashboard
  },
  {
    path: '/recharge',
    name: 'RechargeManagement',
    component: RechargeManagement,
    children: [
      {
        path: 'gunsoulsniping',
        name: 'GunSoulSniping',
        component: () => import('../views/GunSoulSniping.vue')
      },
      {
        path: 'smallshelter',
        name: 'SmallShelter',
        component: () => import('../views/SmallShelter.vue')
      },
      {
        path: 'bubble-game',
        name: 'BubbleGame',
        component: BubbleGame,
        meta: {
          title: '一起泡泡龙'
        }
      },
      {
        path: 'used-car-game',
        name: 'UsedCarGame',
        component: UsedCarGame,
        meta: {
          title: '二手车模拟器',
          icon: 'Car'
        }
      },
      {
        path: 'tower-defense',
        name: 'TowerDefense',
        component: () => import('../views/TowerDefense.vue'),
        meta: {
          title: '塔塔守卫战',
          icon: 'House'
        }
      },
      {
        path: 'cat-survival',
        name: 'CatSurvival',
        component: () => import('@/views/CatSurvival.vue'),
        meta: {
          title: '喵桑活下去充值'
        }
      },
      {
        path: 'javelin-king',
        name: 'JavelinKing',
        component: () => import('../views/JavelinKing.vue')
      },
      {
        path: 'happy-match',
        name: 'HappyMatch',
        component: HappyMatch,
        meta: {
          title: '开心点点消充值管理'
        }
      },
      {
        path: 'cant-beat-me',
        name: 'CantBeatMe',
        component: CantBeatMe,
        meta: {
          title: '砍不过我呀'
        }
      },
      {
        path: 'crazy-blacksmith',
        name: 'CrazyBlacksmith',
        component: CrazyBlacksmith
      },
      {
        path: 'gun-battle',
        name: 'GunBattle',
        component: GunBattle,
        meta: {
          title: '枪战王者'
        }
      },
      {
        path: 'panda-adventure',
        name: 'PandaAdventure',
        component: () => import('../views/PandaAdventure.vue'),
        meta: {
          title: '熊猫冒险传奇'
        }
      },
      {
        path: 'horror-hide-and-seek',
        name: 'HorrorHideAndSeek',
        component: () => import('../views/HorrorHideAndSeek.vue'),
        meta: {
          title: '恐怖躲猫猫'
        }
      },
      {
        path: 'lazy-brother',
        name: 'LazyBrother',
        component: () => import('../views/LazyBrotherView.vue'),
        meta: {
          title: '躺平小弟'
        }
      },
      {
        path: 'cultivator-simulator',
        name: 'CultivatorSimulator',
        component: () => import('../views/CultivatorSimulator.vue'),
        meta: {
          title: '散修生活模拟器'
        }
      },
      {
        path: 'oracle-war',
        name: 'OracleWar',
        component: () => import('../views/OracleWar.vue'),
        meta: {
          title: '甲骨文战争'
        }
      },
      {
        path: 'three-kingdoms-auto-chess',
        name: 'ThreeKingdomsAutoChess',
        component: () => import('../views/ThreeKingdomsAutoChess.vue'),
        meta: {
          title: '三国争霸自走棋',
          icon: 'Chess'
        }
      },
      {
        path: 'slow-piggy',
        name: 'SlowPiggy',
        component: () => import('../views/SlowPiggy.vue'),
        meta: {
          title: '慢豚豚的生活',
          icon: 'Chicken'
        }
      },
      {
        path: 'who-beat-me',
        name: 'WhoBeatMe',
        component: WhoBeatMe,
        meta: {
          title: '看谁能打过',
          icon: 'Trophy'
        }
      }
    ]
  },
  {
    path: '/user',
    name: 'UserInformation',
    component: UserInformation
  },
  {
    path: '/about',
    name: 'About',
    component: () => import(/* webpackChunkName: "about" */ '../views/AboutView.vue')
  },
  {
    path: '/decoder',
    name: 'Decoder',
    component: () => import('../views/DecoderLayout.vue'), // 添加一个布局组件
    redirect: '/decoder/base64',
    children: [
      {
        path: 'base64',
        name: 'Base64Decoder',
        component: Base64Decoder
      },
      {
        path: 'custom-base64',
        name: 'CustomBase64Decoder',
        component: () => import('../views/CustomBase64Decoder.vue')
      }
    ]
  },
  {
    path: '/packages',
    name: 'PackageTable',
    component: () => import('@/views/PackageTableView.vue')
  },
  {
    path: '/javelin-packages',
    name: 'JavelinPackages',
    component: () => import('../views/JavelinPackages.vue'),
    meta: {
      title: '标枪王者套餐'
    }
  },
  {
    path: '/used-car-packages',
    name: 'UsedCarPackages',
    component: () => import('@/views/UsedCarPackages.vue'),
    meta: {
      title: '二手车模拟器套餐'
    }
  },
  {
    path: '/cant-beat-me-packages',
    name: 'CantBeatMePackages',
    component: () => import('@/views/CantBeatMePackages.vue'),
    meta: {
      title: '砍不过我呀套餐'
    }
  },
  {
    path: '/gun-battle-packages',
    name: 'GunBattlePackages',
    component: () => import('../views/GunBattlePackages.vue'),
    meta: {
      title: '枪战王者套餐'
    }
  },
  {
    path: '/lazy-brother-packages',
    name: 'LazyBrotherPackages',
    component: () => import('../views/LazyBrotherPackages.vue'),
    meta: {
      title: '躺平小弟套餐'
    }
  },
  {
    path: '/cultivator-simulator-packages',
    name: 'CultivatorSimulatorPackages',
    component: () => import('../views/CultivatorSimulatorPackages.vue'),
    meta: {
      title: '散修生活模拟器套餐'
    }
  },
  {
    path: '/oracle-war-packages',
    name: 'OracleWarPackages',
    component: () => import('../views/OracleWarPackages.vue'),
    meta: {
      title: '甲骨文战争套餐'
    }
  },
  {
    path: '/promotion/three-kingdoms',
    name: 'ThreeKingdomsPromotion',
    component: () => import('../views/ThreeKingdomsPromotion.vue'),
    meta: {
      title: '三国争霸自走棋宣传'
    }
  },
  {
    path: '/slow-piggy-packages',
    name: 'SlowPiggyPackages',
    component: () => import('../views/SlowPiggyPackages.vue'),
    meta: {
      title: '慢豚豚的生活套餐'
    }
  },
  {
    path: '/who-beat-packages',
    name: 'WhoBeatPackages',
    component: () => import('../views/WhoBeatPackages.vue'),
    meta: {
      title: '看谁能打过套餐'
    }
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes
})

export default router
