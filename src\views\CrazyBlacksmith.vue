<template>
  <div class="crazy-blacksmith">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <Tools />
              </el-icon>
              <span>疯狂铁匠</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 输入区域 -->
          <div class="input-section">
            <el-form ref="formRef" :model="form" :rules="rules" label-position="top">
              <el-row :gutter="20">
                <el-col :xs="24" :sm="12">
                  <el-form-item prop="loginCode" label="游戏ID">
                    <el-input v-model="form.loginCode" placeholder="请输入游戏ID" :disabled="loading.download" clearable
                      readonly />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item prop="token" label="Token">
                    <el-input v-model="form.token" placeholder="请输入Token(选填)" :disabled="loading.download" clearable
                      readonly type="text" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>

          <!-- 修改 DATA 数据输入区域 -->
          <div class="data-input-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon><DataLine /></el-icon>
                <span>DATA数据</span>
              </div>
              <el-button 
                type="warning" 
                size="small" 
                :icon="Delete"
                @click="handleClearData"
                class="clear-button"
              >
                清空数据
              </el-button>
            </div>
            <el-input
              v-model="dataInput"
              type="textarea"
              :rows="3"
              placeholder="请输入DATA数据"
              :disabled="loading.download"
              resize="none"
              class="data-textarea"
            />
          </div>

          <!-- 作按钮 -->
          <div class="action-section">
            <el-button 
              type="primary" 
              :loading="loading.download" 
              @click="handleParseData" 
              :icon="DataAnalysis"
              class="action-button"
            >
              解析数据
            </el-button>
            <el-button 
              type="success" 
              :loading="loading.upload" 
              @click="handleUpload" 
              :icon="Upload"
              :disabled="!hasData || !dataInput.trim()" 
              class="action-button"
            >
              上传数据
            </el-button>
            <el-tooltip 
              content="修改金币/钻石为999999" 
              placement="top" 
              effect="dark"
            >
              <el-button 
                type="danger" 
                :loading="loading.package1" 
                @click="handlePackage1" 
                :icon="Present"
                :disabled="!hasData || !dataInput.trim()" 
                class="action-button"
              >
                套餐1
              </el-button>
            </el-tooltip>
          </div>

          <!-- 添加数据显示区域 -->
          <div v-if="hasData" class="game-data-section">
            <div class="section-title">
              <el-icon>
                <Document />
              </el-icon>
              <span>游戏数据</span>
            </div>

            <el-tabs type="border-card">
              <!-- 基础属性 -->
              <el-tab-pane label="基础属性">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <!-- 游戏资源 -->
                    <el-col :span="8">
                      <el-form-item label="金币" :class="{ 'modified-item': modifiedFields.includes('gold') }">
                        <el-input-number v-model="gameData.itemData['2']" :min="0" :max="999999999"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="钻石" :class="{ 'modified-item': modifiedFields.includes('diamond') }">
                        <el-input-number v-model="gameData.itemData['3']" :min="0" :max="999999999"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="体力" :class="{ 'modified-item': modifiedFields.includes('power') }">
                        <el-input-number v-model="gameData.power" :min="0" :max="999999999" controls-position="right"
                          style="width: 100%" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 装备系统 -->
              <el-tab-pane label="装备系统">
                <el-table :data="toolsList" border stripe style="width: 100%">
                  <el-table-column label="装备ID" prop="id" width="100" align="center" />
                  <el-table-column label="等级" width="150" align="center">
                    <template #default="scope">
                      <el-input-number v-model="gameData.toolData[scope.row.id].lv" :min="0" :max="999"
                        controls-position="right" style="width: 100%" />
                    </template>
                  </el-table-column>
                  <el-table-column label="碎片数量" width="150" align="center">
                    <template #default="scope">
                      <el-input-number v-model="gameData.toolData[scope.row.id].c" :min="0" :max="999999"
                        controls-position="right" style="width: 100%" />
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>

              <!-- 其他设置 -->
              <el-tab-pane label="其他设置">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-form-item label="游戏速度">
                        <el-input-number v-model="gameData.timeScale" :min="0.5" :max="2" :step="0.1"
                          controls-position="right" style="width: 100%" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="音乐">
                        <el-switch v-model="gameData.isMMute" active-text="静音" inactive-text="开启" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="8">
                      <el-form-item label="音效">
                        <el-switch v-model="gameData.isSMute" active-text="静音" inactive-text="开启" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 货币系统 -->
              <el-tab-pane label="货币系统">
                <el-table :data="coinsList" border stripe style="width: 100%">
                  <el-table-column label="货币ID" prop="id" width="100" align="center" />
                  <el-table-column label="货币类型" width="120" align="center">
                    <template #default="scope">
                      <el-tag :type="getCoinTypeTag(scope.row.id)">
                        {{ getCoinTypeName(scope.row.id) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="数量" align="center">
                    <template #default="scope">
                      <el-input-number 
                        v-model="gameData.Mycoins[scope.row.id]" 
                        :min="0"
                        :max="999999999"
                        controls-position="right"
                        style="width: 180px"
                        @change="handleCoinChange(scope.row.id)"
                      />
                    </template>
                  </el-table-column>
                  <el-table-column label="说明" align="center">
                    <template #default="scope">
                      {{ getCoinDescription(scope.row.id) }}
                    </template>
                  </el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 操作历史部分 -->
          <div v-if="operationHistory.length" class="history-section">
            <div class="section-title">
              <el-icon>
                <Timer />
              </el-icon>
              <span>操作记</span>
            </div>
            <el-timeline>
              <el-timeline-item v-for="(history, index) in operationHistory.slice(0, 5)" :key="index"
                :type="history.success ? 'success' : 'danger'" :timestamp="history.time" :hollow="true" size="normal">
                {{ history.operation }}
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Tools, Upload, Document, Timer, Present, DataLine, DataAnalysis, Delete } from '@element-plus/icons-vue'

// 表单引用
const formRef = ref(null)

// 表单数
const form = reactive({
  loginCode: 'obMp55V00F0GUP9OMFWG2K0Q6utg',
  token: '8930ffd8-a569-11ef-af22-0c42a1f1e1e0',
  uid: ''
})

// 表单验证规则
const rules = {
  loginCode: [
    { required: true, message: '请输入游戏ID', trigger: 'blur' }
  ],
  token: [
    { required: false, message: '请输入Token', trigger: 'blur' }
  ]
}

// 加载状态
const loading = reactive({
  download: false,
  upload: false,
  package1: false
})

// 是否已获取数据
const hasData = ref(false)

// 游戏数据
const gameData = ref({
  itemData: {
    '2': 0,  // 金币
    '3': 0   // 钻石
  },
  power: 0,
  timeScale: 1,
  isMMute: false,
  isSMute: false,
  toolData: {},
  Mycoins: {
    '101': 0,
    '102': 0,
    '103': 0,
    '104': 0
  }
})

// 计算道具列表
const toolsList = computed(() => {
  if (!gameData.value?.toolData) return []
  return Object.entries(gameData.value.toolData).map(([id]) => ({
    id
  }))
})

// 操作历史数组
const operationHistory = ref([])

// 修改字段数组
const modifiedFields = ref([])

// 添加数据输入响应式变量
const dataInput = ref('')

// 货币列表计算属性
const coinsList = computed(() => {
  if (!gameData.value?.Mycoins) return []
  return Object.keys(gameData.value.Mycoins).map(id => ({
    id: id
  }))
})

// 货币类型名称映射
const coinTypeMap = {
  '101': '金币',
  '102': '未知货币1',
  '103': '钻石',
  '104': '未知货币2'
}

// 货币说明映射
const coinDescriptionMap = {
  '101': '游戏主要货币',
  '102': '待确认用途',
  '103': '高级货币',
  '104': '待确认用途'
}

// 获取货币类型标签样式
const getCoinTypeTag = (id) => {
  const tagMap = {
    '101': 'warning', // 金币用黄色
    '102': 'info',    // 未知货币用灰色
    '103': 'success', // 钻石用绿色
    '104': 'info'     // 未知货币用灰色
  }
  return tagMap[id] || 'info'
}

// 获取货币类型名称
const getCoinTypeName = (id) => {
  return coinTypeMap[id] || `货币${id}`
}

// 获取货币说明
const getCoinDescription = (id) => {
  return coinDescriptionMap[id] || '暂无说明'
}

// 处理货币数量变化
const handleCoinChange = (id) => {
  modifiedFields.value = [...new Set([...modifiedFields.value, `coin_${id}`])]
  
  // 添加作历史
  operationHistory.value.unshift({
    time: new Date().toLocaleString(),
    operation: `修改${getCoinTypeName(id)}数量为: ${gameData.value.Mycoins[id]}`,
    success: true
  })
}

// 修改解析数据方法
const handleParseData = async () => {
  if (!dataInput.value.trim()) {
    ElMessage.warning('请输入DATA数据')
    return
  }

  loading.download = true
  try {
    // 尝试解析DATA数据
    const parsedData = JSON.parse(dataInput.value.trim())
    
    // 检查是否包含必要的数据结构
    if (!parsedData.values) {
      throw new Error('数据格式错误：缺少 values 字段')
    }

    // 构建新的游戏数据对象
    const newGameData = {
      itemData: {
        '2': parsedData.values.items?.金币 || 0,
        '3': 0  // 其他数据
      },
      power: parsedData.values.power || 0,
      timeScale: parsedData.values.timeScale || 1,
      isMMute: parsedData.values.musicOn || false,
      isSMute: parsedData.values.soundOn || false,
      toolData: parsedData.values.toolData || {},
      Mycoins: parsedData.values.Mycoins || {
        '101': 0,
        '102': 0,
        '103': 0,
        '104': 0
      }
    }

    // 更新游戏数据
    gameData.value = newGameData
    hasData.value = true

    // 更新表单数据
    form.loginCode = parsedData.values.openId || form.loginCode
    form.token = parsedData.values.token || form.token

    ElMessage.success('数据解析成功')
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '解析DATA数据成功',
      success: true
    })

    // 打印关键数据，方便调试
    console.log('解析后的数据:', gameData.value)

  } catch (error) {
    console.error('解析失败:', error)
    ElMessage.error(`数据格式错误: ${error.message}`)
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `解析失败: ${error.message}`,
      success: false
    })
  } finally {
    loading.download = false
  }
}

// 修改上传数据方法
const handleUpload = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先解析数据')
    return
  }

  loading.upload = true
  try {
    // 构建完整的请求数据
    const formData = new URLSearchParams()
    formData.append('appid', 'wx48667cfa12792a52')
    formData.append('token', form.token)
    
    // 解析原始数据以获取当前 times 值
    const originalData = JSON.parse(dataInput.value)
    const currentTimes = originalData.times || 0
    
    // 生成新的 times 值：当前值 + 随机数(1-50)
    const newTimes = currentTimes + Math.floor(Math.random() * 50) + 1
    
    // 构建完整的游戏数据对象
    const uploadData = {
      ...originalData,
      Mycoins: gameData.value.Mycoins,
      times: newTimes, // 使用新生成的 times 值
      openId: form.loginCode,
      token: form.token
    }

    formData.append('data', JSON.stringify(uploadData))

    const response = await fetch('/kod/save', {
      method: 'POST',
      headers: {
        'content-type': 'application/x-www-form-urlencoded',
        'xweb_xhr': '1'
      },
      body: formData
    })

    if (!response.ok) {
      throw new Error('网络请求失败')
    }

    const result = await response.json()
    
    // 根据 Status 判断是否成功
    if (result.Status === 0) { // 修改这里，使用 Status 而不是 ok
      ElMessage.success('数据上传成功')
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: `更新游戏数据成功 (服务器时间: ${new Date(result.SeverTime).toLocaleString()})`,
        success: true
      })
    } else {
      throw new Error(`上传失败: Status=${result.Status}`)
    }

  } catch (error) {
    console.error('上传失败:', error)
    ElMessage.error(error.message || '上传失败')
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `更新失败: ${error.message}`,
      success: false
    })
  } finally {
    loading.upload = false
  }
}

// 修改套餐1处理函数
const handlePackage1 = async () => {
  if (!hasData.value) {
    ElMessage.warning('请先解析数据')
    return
  }

  loading.package1 = true
  try {
    // 修改游戏数据 - 只修改金币和钻石
    gameData.value = {
      ...gameData.value,
      Mycoins: {
        ...gameData.value.Mycoins,
        '101': 999999,  // 金币
        '103': 999999   // 钻石
      }
    }

    modifiedFields.value = ['coin_101', 'coin_103']

    await handleUpload()

    ElMessage({
      type: 'success',
      message: '套餐1应用成功：金币/钻石已修改为999999',
      duration: 3000,
      showClose: true
    })

    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: '套餐1：金币/钻石修改为999999',
      success: true
    })
  } catch (error) {
    console.error('套餐1应用失败:', error)
    ElMessage.error(error.message || '套餐1应用失败')
    operationHistory.value.unshift({
      time: new Date().toLocaleString(),
      operation: `套餐1应用失败: ${error.message}`,
      success: false
    })
  } finally {
    loading.package1 = false
  }
}

// 添加清空数据方法
const handleClearData = () => {
  ElMessageBox.confirm(
    '确定要清空数据吗？此操作不可恢复',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(() => {
      dataInput.value = ''
      gameData.value = {
        itemData: {
          '2': 0,
          '3': 0
        },
        power: 0,
        timeScale: 1,
        isMMute: false,
        isSMute: false,
        toolData: {},
        Mycoins: {
          '101': 0,
          '102': 0,
          '103': 0,
          '104': 0
        }
      }
      hasData.value = false
      modifiedFields.value = []
      
      ElMessage({
        type: 'success',
        message: '数据已清空'
      })
      
      operationHistory.value.unshift({
        time: new Date().toLocaleString(),
        operation: '清空所有数据',
        success: true
      })
    })
    .catch(() => {
      // 取消清空操作
    })
}

// 添加生命周期钩子
onMounted(() => {
  // ResizeObserver 错误处理
  const OriginalResizeObserver = window.ResizeObserver
  window.ResizeObserver = class ResizeObserver extends OriginalResizeObserver {
    constructor(callback) {
      super((entries, observer) => {
        window.requestAnimationFrame(() => {
          try {
            callback(entries, observer)
          } catch (e) {
            if (!e.message.includes('ResizeObserver')) {
              console.error(e)
            }
          }
        })
      })
    }
  }

  const errorHandler = (event) => {
    if (event?.message?.includes('ResizeObserver') ||
      event?.error?.message?.includes('ResizeObserver')) {
      event.preventDefault()
      event.stopPropagation()
      return false
    }
  }

  window.addEventListener('error', errorHandler, true)
  window.addEventListener('unhandledrejection', errorHandler, true)

  const originalConsoleError = console.error
  console.error = (...args) => {
    if (args.some(arg =>
      String(arg).includes('ResizeObserver') ||
      (arg instanceof Error && arg.message.includes('ResizeObserver'))
    )) {
      return
    }
    originalConsoleError.apply(console, args)
  }

  onUnmounted(() => {
    window.removeEventListener('error', errorHandler, true)
    window.removeEventListener('unhandledrejection', errorHandler, true)
    window.ResizeObserver = OriginalResizeObserver
    console.error = originalConsoleError
  })
})
</script>

<style scoped>
.crazy-blacksmith {
  padding: 20px;
  height: 100vh;
  background-color: var(--el-bg-color-page);
  overflow-y: auto;
  box-sizing: border-box;
}

.main-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
  height: auto;
  min-height: calc(100vh - 40px);
}

/* 美化头部区域 */
.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  margin: -20px -20px 24px -20px;
  background: linear-gradient(135deg, #FF9A9E 0%, #FAD0C4 100%);
  border-radius: 12px 12px 0 0;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.title .el-icon {
  font-size: 28px;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 10px;
  border-radius: 12px;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-tag {
  border: none;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(4px);
  padding: 0 16px;
  height: 32px;
  line-height: 32px;
  font-weight: 500;
  font-size: 14px;
  z-index: 1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 美化按钮区域 */
.action-section {
  display: flex;
  gap: 16px;
  margin: 24px 0;
  padding: 0 20px;
  flex-wrap: wrap;
}

.action-button {
  flex: 1;
  min-width: 140px;
  max-width: 200px;
  height: 44px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.action-button .el-icon {
  font-size: 18px;
  margin-right: 6px;
}

/* 美化表单区域 */
.input-section {
  margin: 24px 0;
  padding: 0 20px;
}

:deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

:deep(.el-input__inner) {
  height: 44px;
  font-size: 15px;
}

/* 美化数据展示区域 */
.game-data-section {
  margin: 24px 0;
  padding: 0 20px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.section-title .el-icon {
  font-size: 22px;
  color: var(--el-color-primary);
  background: var(--el-color-primary-light-9);
  padding: 8px;
  border-radius: 8px;
}

/* 美化标签页 */
:deep(.el-tabs__nav) {
  border-radius: 8px 8px 0 0;
}

:deep(.el-tabs__item) {
  font-size: 15px;
  height: 44px;
  line-height: 44px;
}

:deep(.el-tabs__content) {
  padding: 24px;
}

/* 美化表格 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
}

:deep(.el-table th) {
  background-color: var(--el-color-primary-light-9);
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

/* 美化数字输入框 */
:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-input-number .el-input__wrapper) {
  padding: 0 8px;
}

/* 美化历史记录区域 */
.history-section {
  margin: 24px 0;
  padding: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  background: var(--el-color-primary-light-9);
  border-radius: 8px;
}

:deep(.el-timeline) {
  padding: 16px;
}

:deep(.el-timeline-item__node) {
  width: 16px;
  height: 16px;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 28px;
}

:deep(.el-timeline-item__content) {
  font-size: 14px;
  color: var(--el-text-color-regular);
}

:deep(.el-timeline-item__timestamp) {
  font-size: 13px;
  color: var(--el-text-color-secondary);
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .crazy-blacksmith {
    padding: 12px;
  }

  .header-section {
    padding: 16px;
  }

  .title {
    font-size: 20px;
  }

  .action-section {
    flex-direction: column;
    gap: 12px;
  }

  .action-button {
    max-width: none;
    width: 100%;
  }

  .input-section :deep(.el-input) {
    width: 100% !important;
  }

  :deep(.el-tabs__content) {
    padding: 16px;
  }
}

/* 添加动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.game-data-section,
.history-section {
  animation: fadeIn 0.3s ease-out;
}

/* 添加悬浮效果 */
.main-card {
  transition: transform 0.3s ease;
}

.main-card:hover {
  transform: translateY(-2px);
}

/* 美化修改标记 */
.modified-item {
  position: relative;
}

.modified-item::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: 2px solid var(--el-color-danger);
  border-radius: 8px;
  pointer-events: none;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

:deep(.modified-item .el-input-number__wrapper) {
  background-color: var(--el-color-danger-light-9);
  border-color: var(--el-color-danger);
}

:deep(.modified-item .el-form-item__label) {
  color: var(--el-color-danger);
  font-weight: 500;
}

/* 调整输入框间距 */
.el-row {
  margin-bottom: 0 !important;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

/* 美化密码框 */
:deep(.el-input.is-disabled .el-input__wrapper) {
  background-color: var(--el-disabled-bg-color);
}

:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px var(--el-color-primary) inset;
}

/* 响应式调整 */
@media screen and (max-width: 768px) {
  .el-col {
    margin-bottom: 12px;
  }
}

/* 数据输入区域样式 */
.data-input-section {
  margin: 16px 0;
  padding: 0 20px;
  background-color: var(--el-color-info-light-9);
  border-radius: 8px;
  padding: 12px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.data-textarea {
  margin: 0;
}

:deep(.el-textarea__inner) {
  border-radius: 4px;
  padding: 8px;
  font-family: monospace;
  font-size: 13px;
  line-height: 1.4;
  height: 120px !important;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

:deep(.el-textarea__inner:focus) {
  background-color: var(--el-bg-color);
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

:deep(.el-table .el-tag) {
  min-width: 80px;
}

:deep(.el-input-number.is-controls-right .el-input__wrapper) {
  padding-left: 15px;
  padding-right: 15px;
}

/* 添加 section-header 样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.clear-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 12px;
  height: 28px;
}

.clear-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.clear-button .el-icon {
  font-size: 12px;
}

/* 修改标题样式 */
.section-title {
  font-size: 14px;
  margin-bottom: 0;
}

.section-title .el-icon {
  font-size: 16px;
  padding: 4px;
}
</style>