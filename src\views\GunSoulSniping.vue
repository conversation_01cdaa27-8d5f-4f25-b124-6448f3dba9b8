<template>
  <div class="gun-soul-sniping">
    <el-scrollbar height="calc(100vh - 120px)">
      <div class="content-wrapper">
        <el-row :gutter="20">
          <!-- 游戏ID卡片 -->
          <el-col :span="12">
            <el-card class="box-card fixed-height-card" v-loading="isLoading">
              <template #header>
                <div class="card-header">
                  <span>游戏ID</span>
                </div>
              </template>
              <GameIdForm ref="gameIdFormRef" @update:operationHistory="updateOperationHistory" @loading="setLoading"
                @data-loaded="handleDataLoaded" />
            </el-card>
          </el-col>

          <!-- 右侧列 -->
          <el-col :span="12">
            <!-- 快捷操作卡片 -->
            <el-card class="box-card quick-actions-card">
              <template #header>
                <div class="card-header">
                  <span>快捷操作</span>
                </div>
              </template>
              <div class="quick-actions">
                <el-row :gutter="10">
                  <el-col :span="8">
                    <el-tooltip placement="top" effect="light" :show-after="100">
                      <template #content>
                        <div class="package-content">
                          <div class="package-item">
                            <span class="package-icon">💰</span>
                            <span>巡逻联盟币: <span class="package-value">20万</span></span>
                          </div>
                          <div class="package-item">
                            <span class="package-icon">🏆</span>
                            <span>金条: <span class="package-value">13000</span></span>
                          </div>
                          <div class="package-item">
                            <span class="package-icon">💎</span>
                            <span>天赋石: <span class="package-value">100</span></span>
                          </div>
                        </div>
                      </template>
                      <el-button type="primary" size="large" class="quick-action-btn" @click="handlePackageOne"
                        :disabled="!isDataLoaded">
                        <el-icon>
                          <ShoppingCart />
                        </el-icon>
                        套餐一
                      </el-button>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="8">
                    <el-tooltip placement="top" effect="light" :show-after="100">
                      <template #content>
                        <div class="package-content">
                          <div class="package-item">
                            <span class="package-icon">💰</span>
                            <span>巡逻联盟币: <span class="package-value">80万</span></span>
                          </div>
                          <div class="package-item">
                            <span class="package-icon">🏆</span>
                            <span>金条: <span class="package-value">51000</span></span>
                          </div>
                          <div class="package-item">
                            <span class="package-icon">💎</span>
                            <span>天赋石: <span class="package-value">350</span></span>
                          </div>
                        </div>
                      </template>
                      <el-button type="success" size="large" class="quick-action-btn" @click="handlePackageTwo"
                        :disabled="!isDataLoaded">
                        <el-icon>
                          <ShoppingCart />
                        </el-icon>
                        套餐二
                      </el-button>
                    </el-tooltip>
                  </el-col>
                  <el-col :span="8">
                    <el-tooltip placement="top" effect="light" :show-after="100">
                      <template #content>
                        <div class="package-content">
                          <div class="package-item">
                            <span class="package-icon">💰</span>
                            <span>巡逻联盟币: <span class="package-value">120万</span></span>
                          </div>
                          <div class="package-item">
                            <span class="package-icon">🏆</span>
                            <span>金条: <span class="package-value">13万</span></span>
                          </div>
                          <div class="package-item">
                            <span class="package-icon">💎</span>
                            <span>天赋石: <span class="package-value">999</span></span>
                          </div>
                        </div>
                      </template>
                      <el-button type="warning" size="large" class="quick-action-btn" @click="handlePackageThree"
                        :disabled="!isDataLoaded">
                        <el-icon>
                          <ShoppingCart />
                        </el-icon>
                        套餐三
                      </el-button>
                    </el-tooltip>
                  </el-col>
                </el-row>
              </div>
            </el-card>

            <!-- 操作历史卡片 -->
            <el-card class="box-card history-card">
              <template #header>
                <div class="card-header">
                  <span>操作历史</span>
                </div>
              </template>
              <RechargeHistory :initialData="operationHistory" />
            </el-card>
          </el-col>
        </el-row>
      </div>
    </el-scrollbar>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import GameIdForm from '@/components/GameIdForm.vue'
import RechargeHistory from '@/components/RechargeHistory.vue'
import { ShoppingCart, Star, MagicStick } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import md5 from 'md5'  // 添加这行

const operationHistory = ref([])
const isLoading = ref(false)
const gameIdFormRef = ref(null)
const isDataLoaded = ref(false) // 添加数据加载状态

// 添加 encryptionData 函数
const encryptionData = (data) => {
  try {
    const n = Math.floor(Date.now() / 1e3).toString();
    const md5Str = md5("NewRpggameV2" + n);

    // 将数据转换为 UTF-8 编码的字符串
    let base64Str = '';
    if (data) {
      // 将字符串转换为 UTF-8 编码的字节数组
      const utf8Str = unescape(encodeURIComponent(data));
      // 进行 Base64 编码
      base64Str = btoa(utf8Str);
    } else {
      base64Str = btoa('null');
    }

    return md5Str + base64Str + n;
  } catch (error) {
    console.error('加密数据失败:', error);
    ElMessage.error('加密数据失败');
    return null;
  }
}

// 添加数据加载状态处理函数
const handleDataLoaded = (loaded) => {
  isDataLoaded.value = loaded
}

// 添加一个新的函数来处理额外请求
const sendAdditionalRequest = async (auth, token) => {
  const additionalRequestData = {
    ActionId: 7,
    UserId: auth,
    Params: token,
    ServerId: "1", 
    Channel: 120,
    Version: "1.0.0",
    Package: "com.dtty.qhjj"
  };

  const encryptedAdditionalData = encryptionData(JSON.stringify(additionalRequestData));

  const additionalResponse = await fetch('/api/logic/newrpg_actionsV2', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json;charset=UTF-8',
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
      'xweb_xhr': '1',
      'Accept': '*/*',
      'Sec-Fetch-Site': 'cross-site',
      'Sec-Fetch-Mode': 'cors',
      'Sec-Fetch-Dest': 'empty',
      'Referer': 'https://servicewechat.com/wx73375fce167e413f/56/page-frame.html',
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept-Language': 'zh-CN,zh;q=0.9'
    },
    body: encryptedAdditionalData
  });

  if (!additionalResponse.ok) {
    throw new Error('额外请求失败');
  }
}

// 修改套餐一的处理函数
const handlePackageOne = async () => {
  try {
    if (!gameIdFormRef.value || !gameIdFormRef.value.gameData) {
      ElMessage.warning('请先下载游戏数据')
      return
    }

    const gameData = gameIdFormRef.value.gameData

    // 修改游戏数据
    gameData.money = (parseInt(gameData.money) || 0) + 200000
    gameData.gem = (parseInt(gameData.gem) || 0) + 13000
    gameData.tianFuShiNum = (parseInt(gameData.tianFuShiNum) || 0) + 100

    // 更新游戏数据
    const { token, auth } = await gameIdFormRef.value.updateGame()
    
    // 发送额外请求
    await sendAdditionalRequest(auth, token)

    // 添加操作历史
    const currentTime = new Date().toLocaleString()
    const newHistory = [{
      time: currentTime,
      function: '套餐一',
      value: '20万巡逻联盟币+13000金条+100天赋石',
      success: true
    }]
    updateOperationHistory(newHistory)

    ElMessage.success('套餐一充值成功')
  } catch (error) {
    console.error('套餐一充值失败:', error)
    ElMessage.error('套餐一充值失败: ' + error.message)
  }
}

// 修改套餐二的处理函数
const handlePackageTwo = async () => {
  try {
    if (!gameIdFormRef.value || !gameIdFormRef.value.gameData) {
      ElMessage.warning('请先下载游戏数据')
      return
    }

    const gameData = gameIdFormRef.value.gameData

    // 修改游戏数据
    gameData.money = (parseInt(gameData.money) || 0) + 800000
    gameData.gem = (parseInt(gameData.gem) || 0) + 51000
    gameData.tianFuShiNum = (parseInt(gameData.tianFuShiNum) || 0) + 350

    // 更新游戏数据
    const { token, auth } = await gameIdFormRef.value.updateGame()
    
    // 发送额外请求
    await sendAdditionalRequest(auth, token)

    // 添加操作历史
    const currentTime = new Date().toLocaleString()
    const newHistory = [{
      time: currentTime,
      function: '套餐二',
      value: '80万巡逻联盟币+51000金条+350天赋石',
      success: true
    }]
    updateOperationHistory(newHistory)

    ElMessage.success('套餐二充值成功')
  } catch (error) {
    console.error('套餐二充值失败:', error)
    ElMessage.error('套餐二充值失败: ' + error.message)
  }
}

// 修改套餐三的处理函数
const handlePackageThree = async () => {
  try {
    if (!gameIdFormRef.value || !gameIdFormRef.value.gameData) {
      ElMessage.warning('请先下载游戏数据')
      return
    }

    const gameData = gameIdFormRef.value.gameData

    // 修改游戏数据
    gameData.money = (parseInt(gameData.money) || 0) + 2000000
    gameData.gem = (parseInt(gameData.gem) || 0) + 128000
    gameData.tianFuShiNum = (parseInt(gameData.tianFuShiNum) || 0) + 800

    // 更新游戏数据
    const { token, auth } = await gameIdFormRef.value.updateGame()
    
    // 发送额外请求
    await sendAdditionalRequest(auth, token)

    // 添加操作历史
    const currentTime = new Date().toLocaleString()
    const newHistory = [{
      time: currentTime,
      function: '套餐三',
      value: '200万巡逻联盟币+128000金条+800天赋石',
      success: true
    }]
    updateOperationHistory(newHistory)

    ElMessage.success('套餐三充值成功')
  } catch (error) {
    console.error('套餐三充值失败:', error)
    ElMessage.error('套餐三充值失败: ' + error.message)
  }
}

const updateOperationHistory = (newHistory) => {
  const historyWithStatus = newHistory.map(item => ({
    ...item,
    status: item.success ? '成功' : '失败'
  }))
  operationHistory.value = [...historyWithStatus, ...operationHistory.value].slice(0, 50)
}

const setLoading = (loading) => {
  isLoading.value = loading
}
</script>

<style scoped>
.gun-soul-sniping {
  height: calc(100vh - 120px);
}

.content-wrapper {
  padding: 20px;
  min-height: 100%;
}

.box-card {
  width: 100%;
}

.fixed-height-card {
  height: calc(100vh - 160px);
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.fixed-height-card :deep(.el-card__body) {
  flex: 1;
  overflow-y: auto;
  padding-right: 8px;
}

/* 美化滚动条 */
.fixed-height-card :deep(.el-card__body::-webkit-scrollbar) {
  width: 6px;
}

.fixed-height-card :deep(.el-card__body::-webkit-scrollbar-thumb) {
  background: #dcdfe6;
  border-radius: 3px;
}

.fixed-height-card :deep(.el-card__body::-webkit-scrollbar-track) {
  background: #f5f7fa;
}

/* 右侧列样式 */
.quick-actions-card {
  height: 180px;
  margin-bottom: 20px;
}

.quick-actions {
  padding: 10px 0;
  width: 100%;
  height: calc(100% - 55px);
  display: flex;
  align-items: center;
}

.quick-action-btn {
  width: 100%;
  height: 70px;
  display: flex !important;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
}

.quick-action-btn .el-icon {
  font-size: 20px;
}

/* 操作历史卡片 */
.history-card {
  height: calc(100vh - 360px);
  margin-bottom: 20px;
  display: flex;
  flex-direction: column;
}

.history-card :deep(.el-card__body) {
  flex: 1;
  overflow: hidden;
  /* 改为 hidden隐藏超出部分 */
  padding-right: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

:deep(.el-scrollbar__wrap) {
  overflow-x: hidden;
}

@media (max-width: 768px) {
  .quick-action-btn {
    font-size: 12px;
    height: 60px;
  }

  .quick-action-btn .el-icon {
    font-size: 18px;
  }
}

/* 修改 tooltip 样式 */
:deep(.el-popper.is-light) {
  border: none !important;
  color: #606266 !important;
  font-size: 14px !important;
  padding: 8px 12px !important;
  border-radius: 4px !important;
  background: #f5f7fa !important;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.1) !important;
  min-width: 280px !important;
  line-height: 1.4 !important;
  font-weight: normal !important;
}

/* 修改箭头样式 */
:deep(.el-popper.is-light .el-popper__arrow::before) {
  background: #f5f7fa !important;
  border: none !important;
}

:deep(.el-popper.is-light .el-popper__arrow::after) {
  border-color: #f5f7fa !important;
}

/* 确保样式优先级 */
:deep(.el-popper) {
  --el-popover-padding: 8px 12px !important;
  --el-popover-border-radius: 4px !important;
  --el-popover-border-color: transparent !important;
  --el-popover-bg-color: #f5f7fa !important;
  --el-popover-hover-border-color: transparent !important;
}

/* 使用全局样式确保能覆盖默认样式 */
.el-popper.is-light {
  background: linear-gradient(145deg, #ffffff, #f5f7fa) !important;
  border: none !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  padding: 12px 16px !important;
  border-radius: 8px !important;
}

.el-popper.is-light .el-popper__arrow::before {
  background: linear-gradient(145deg, #ffffff, #f5f7fa) !important;
  border: none !important;
  box-shadow: -3px -3px 5px rgba(0, 0, 0, 0.05) !important;
}

.el-popper.is-light .package-content {
  font-size: 14px !important;
  color: #606266 !important;
  line-height: 1.6 !important;
  display: flex !important;
  flex-direction: column !important;
  gap: 4px !important;
}

.el-popper.is-light .package-item {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.el-popper.is-light .package-icon {
  width: 16px !important;
  height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.el-popper.is-light .package-value {
  color: #409EFF !important;
  font-weight: 500 !important;
}

/* 添加禁用状态的样式 */
.quick-action-btn[disabled] {
  cursor: not-allowed;
  opacity: 0.6;
}
</style>

