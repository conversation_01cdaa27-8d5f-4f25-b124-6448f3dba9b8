const { defineConfig } = require('@vue/cli-service')
const webpack = require('webpack')

module.exports = defineConfig({
  // 修改基本路径配置
  publicPath: './',
  
  // 添加输出配置
  outputDir: 'dist',
  assetsDir: 'static',
  
  // 启用依赖的转译
  transpileDependencies: true,
  configureWebpack: {
    resolve: {
      // 配置 webpack 的 fallback,用于处理一些 Node.js 核心模块
      fallback: {
        "http": require.resolve("stream-http"),
        "https": require.resolve("https-browserify"), 
        "stream": require.resolve("stream-browserify"),
        "buffer": require.resolve("buffer/"),
        "url": require.resolve("url/")
      }
    },
    plugins: [
      // 添加全局特性标志
      new webpack.DefinePlugin({
        __VUE_PROD_DEVTOOLS__: false,
        __VUE_PROD_HYDRATION_MISMATCH_DETAILS__: false,
        __VUE_OPTIONS_API__: true,
        __VUE_PROD_TIPS__: false
      })
    ],
    optimization: {
      usedExports: true
    },
    performance: {
      hints: false,      // 禁用性能提示
      maxAssetSize: 244 * 1024,
      maxEntrypointSize: 244 * 1024
    },
    stats: {
      warnings: false    // 禁用 webpack 编译警告
    }
  },
  devServer: {
    port: 8080,
    hot: true,
    host: '0.0.0.0',
    webSocketServer: {
      options: {
        host: 'localhost'
      }
    },
    client: {
      webSocketURL: {
        hostname: 'localhost',
        pathname: '/ws',
        port: 8080,
        protocol: 'ws',
      },
      overlay: {
        warnings: false,
        errors: true,
        // 自定义过滤哪些错误不在overlay中显示
        runtimeErrors: (error) => {
          if (error && error.message && error.message.includes('ResizeObserver')) {
            return false; // 不显示ResizeObserver错误
          }
          return true; // 显示其他错误
        }
      },
      logging: 'warn'
    },
    proxy: {
      // 枪魂狙击游戏 SDK 接口代理
      '/sdk': {
        target: 'https://nrpg-vx.moblazer.com',
        changeOrigin: true,
        pathRewrite: {
          '^/sdk': '/sdk'
        },
        headers: {
          'Origin': 'https://servicewechat.com'
        }
      },
      // 看谁能打过游戏API代理
      '/who-beat-me/download': {
        target: 'https://yp-storage-1318351391.cos.ap-shanghai.myqcloud.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/who-beat-me/download': '/218'
        },
        headers: {
          'Connection': 'keep-alive',
          'xweb_xhr': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Content-Type': 'application/json',
          'Accept': '*/*',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Referer': 'https://servicewechat.com/wxf6a967fa32d836c7/16/page-frame.html',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        }
      },
      '/who-beat-me/init': {
        target: 'https://sdk-dj.youpingame.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/who-beat-me/init': '/sdk/init.php?noencrypt=1'
        },
        headers: {
          'Connection': 'keep-alive',
          'xweb_xhr': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Referer': 'https://servicewechat.com/wxf6a967fa32d836c7/16/page-frame.html',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        },
        onProxyReq: function(proxyReq, req) {
          // 移除压缩相关的头，避免解码问题
          proxyReq.removeHeader('Accept-Encoding');

          if (req.body) {
            let bodyData = req.body;
            if (typeof bodyData === 'object') {
              bodyData = new URLSearchParams(bodyData).toString();
            }
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        },
        onProxyRes: function(proxyRes) {
          // 确保响应头正确设置
          proxyRes.headers['access-control-allow-origin'] = '*';
          proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
          proxyRes.headers['access-control-allow-headers'] = 'Content-Type, Authorization, X-SID';
        }
      },
      '/who-beat-me/auth': {
        target: 'https://sdk-dj.youpingame.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/who-beat-me/auth': '/api/game_save/game_saves/tencent_cos_info'
        },
        headers: {
          'Connection': 'keep-alive',
          'xweb_xhr': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Referer': 'https://servicewechat.com/wxf6a967fa32d836c7/16/page-frame.html',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 动态设置Cookie，如果请求头中包含sid
          if (req.headers['x-sid']) {
            proxyReq.setHeader('Cookie', `HUOSHUID=${req.headers['x-sid']}; path=/; domain=youpingame.com`);
          }
          // 移除压缩相关的头，避免解码问题
          proxyReq.removeHeader('Accept-Encoding');
        },
        onProxyRes: function(proxyRes, req, res) {
          // 确保响应头正确设置
          proxyRes.headers['access-control-allow-origin'] = '*';
          proxyRes.headers['access-control-allow-methods'] = 'GET, POST, PUT, DELETE, OPTIONS';
          proxyRes.headers['access-control-allow-headers'] = 'Content-Type, Authorization, X-SID';
        }
      },
      '/who-beat-me/upload': {
        target: 'https://yp-storage-1318351391.cos.ap-shanghai.myqcloud.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/who-beat-me/upload': '/218'
        },
        headers: {
          'Connection': 'keep-alive',
          'xweb_xhr': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Content-Type': 'application/json',
          'Accept': '*/*',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Referer': 'https://servicewechat.com/wxf6a967fa32d836c7/16/page-frame.html',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 修改请求方法为PUT
          proxyReq.method = 'PUT';
          
          // 如果客户端传递了Authorization头，使用它
          if (req.headers.authorization) {
            proxyReq.setHeader('Authorization', req.headers.authorization);
          }
          
          if (req.body) {
            const bodyData = JSON.stringify(req.body);
            // 设置内容长度
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // 写入请求体
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        }
      },
      // 通用 API 接口代理
      '/api': {
        target: 'https://gcloud.qmhd87.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/api': '/api'
        },
        headers: {
          'Connection': 'keep-alive',
          'xweb_xhr': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Content-Type': 'application/json',
          'Accept': '*/*',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Referer': 'https://servicewechat.com/wx2bdca0cd94206228/14/page-frame.html',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        },
        onProxyReq: function(proxyReq, req, res) {
          if (req.body) {
            const bodyData = JSON.stringify(req.body);
            // 设置正确的 Content-Type
            proxyReq.setHeader('Content-Type', 'application/json');
            // 设置内容长度
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // 写入请求体
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        },
        onError: (err, req, res) => {
          console.error('Proxy Error:', err);
        }
      },
      // OpenAI API 代理
      '/openai': {
        target: 'https://api.chatanywhere.tech',
        changeOrigin: true,
        pathRewrite: {
          '^/openai': '/v1'
        }
      },
      // 小环境 API 代理
      '/shelter': {
        target: 'https://test-api.example.com',
        changeOrigin: true,
        pathRewrite: {
          '^/shelter': '/api'
        }
      },
      // 小小庇护所数据解析接口代理
      '/pdzs': {
        target: 'https://mini.fattoy.cn:8087',
        changeOrigin: true,
        secure: false,  // 忽略 HTTPS 证书验证
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Referer': 'https://servicewechat.com/wx969813315c7ff3d0/8/page-frame.html',
          'Origin': 'https://servicewechat.com'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 设置请求头的内容类型
          proxyReq.setHeader('Content-Type', 'application/json;charset=UTF-8');
        }
      },
      // 小小庇护所数据更新接口代理
      '/jsfs': {
        target: 'https://mini.fattoy.cn:8087',
        changeOrigin: true,
        secure: false,
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Referer': 'https://servicewechat.com/wx969813315c7ff3d0/8/page-frame.html',
          'Origin': 'https://servicewechat.com'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 设置各种请求头
          proxyReq.setHeader('Content-Type', 'application/json;charset=UTF-8');
          proxyReq.setHeader('Accept', '*/*');
          proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9');
          proxyReq.setHeader('xweb_xhr', '1');
        },
        pathRewrite: {
          '^/jsfs': '/jsfs'  // 保持路径不变
        }
      },
      '/javelin-api': {
        target: 'https://javelin.mandrillvr.com',
        changeOrigin: true,
        pathRewrite: {
          '^/javelin-api': '/api'  // 改回添加 /api 前缀
        },
        headers: {
          'Referer': 'https://servicewechat.com/wxfd5e6758e91c29e6/97/page-frame.html',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Accept': '*/*',
          'Accept-Language': 'zh-CN,zh;q=0.9',
          'Content-Type': 'application/x-www-form-urlencoded',
          'xweb_xhr': '1'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 打印请求信息以便调试
          console.log('Proxying to:', proxyReq.path);
          proxyReq.setHeader('Content-Type', 'application/x-www-form-urlencoded');
          proxyReq.setHeader('xweb_xhr', '1');
        }
      },
      // 修改开心点点消的代理配置
      '/happy-match/getData': {  // 下载数据的代理
        target: 'https://gohappy.qzzgame.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/happy-match/getData': '/v1.0/data/getData'  // 重写到正确的路径
        },
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Referer': 'https://servicewechat.com/wx4a93eb6df699acf5/276/page-frame.html',
          'Origin': 'https://servicewechat.com',
          'xweb_xhr': '1',
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        }
      },
      '/happy-match/saveData': {  // 上传数据的代理
        target: 'https://gohappy.qzzgame.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/happy-match/saveData': '/v1.0/data/saveData'  // 重写到正确的路径
        },
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Referer': 'https://servicewechat.com/wx4a93eb6df699acf5/276/page-frame.html',
          'Origin': 'https://servicewechat.com',
          'xweb_xhr': '1',
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        },
        onProxyReq: function(proxyReq, req, res) {
          proxyReq.setHeader('Content-Type', 'application/x-www-form-urlencoded;charset=UTF-8');
          if (req.body) {
            let bodyData = req.body;
            if (typeof bodyData === 'object') {
              bodyData = new URLSearchParams(bodyData).toString();
            }
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        }
      },
      // 添加甜甜爱消除的代理配置
      '/sweet-match/getData': {  // 下载数据的代理
        target: 'https://gosweet.qzzgame.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/sweet-match/getData': '/v1.0/data/getData'  // 重写到正确的路径
        },
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Referer': 'https://servicewechat.com/wx4a93eb6df699acf5/276/page-frame.html',
          'Origin': 'https://servicewechat.com',
          'xweb_xhr': '1',
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        }
      },
      '/sweet-match/saveData': {  // 上传数据的代理
        target: 'https://gosweet.qzzgame.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/sweet-match/saveData': '/v1.0/data/saveData'  // 重写到正确的路径
        },
        headers: {
          'Accept': 'application/json, text/plain, */*',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Referer': 'https://servicewechat.com/wx4a93eb6df699acf5/276/page-frame.html',
          'Origin': 'https://servicewechat.com',
          'xweb_xhr': '1',
          'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
        },
        onProxyReq: function(proxyReq, req, res) {
          proxyReq.setHeader('Content-Type', 'application/x-www-form-urlencoded;charset=UTF-8');
          if (req.body) {
            let bodyData = req.body;
            if (typeof bodyData === 'object') {
              bodyData = new URLSearchParams(bodyData).toString();
            }
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        }
      },
      '/xsapi': {
        target: 'https://yiyouzan.cn',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/xsapi': '/xsapi'
        },
        headers: {
          'Referer': 'https://servicewechat.com/wx7065d302cdfa0f48/21/page-frame.html',
          'Origin': 'https://servicewechat.com',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Accept': '*/*',
          'Content-Type': 'application/json;charset=UTF-8',
          'xweb_xhr': '1'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 修改请求体处理
          if (req.body) {
            const bodyData = JSON.stringify(req.body);
            // 设置内容长度
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // 写入请求体
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        },
        onError: (err, req, res) => {
          console.error('Proxy Error:', err);
        },
        logLevel: 'debug'  // 添加调试日志
      },
      // 添加疯狂铁匠的代理配置
      '/kod': {
        target: 'https://zomzom.qimiaohaiyu.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/kod': '/kod'
        },
        headers: {
          'Referer': 'https://servicewechat.com/wx48667cfa12792a52/8/page-frame.html',
          'Origin': 'https://servicewechat.com',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Accept': '*/*',
          'Content-Type': 'application/x-www-form-urlencoded',
          'xweb_xhr': '1'
        }
      },
      // 添加根本打不过游戏的代理配置
      '/cant-beat-me/download': {
        target: 'https://yp-storage-1318351391.cos.ap-shanghai.myqcloud.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/cant-beat-me/download': '/215'
        },
        headers: {
          'Connection': 'keep-alive',
          'xweb_xhr': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': '*/*',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Referer': 'https://servicewechat.com/wx76ae3e9c8337d8d2/6/page-frame.html',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        }
      },
      '/cant-beat-me/upload': {
        target: 'https://yp-storage-1318351391.cos.ap-shanghai.myqcloud.com',
        changeOrigin: true,
        secure: false,
        pathRewrite: {
          '^/cant-beat-me/upload': '/215'
        },
        headers: {
          'Connection': 'keep-alive',
          'xweb_xhr': '1',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 MicroMessenger/7.0.20.1781(0x6700143B) NetType/WIFI MiniProgramEnv/Windows WindowsWechat/WMPF WindowsWechat(0x6309092b) XWEB/8555',
          'Content-Type': 'application/json',
          'Accept': '*/*',
          'Sec-Fetch-Site': 'cross-site',
          'Sec-Fetch-Mode': 'cors',
          'Sec-Fetch-Dest': 'empty',
          'Referer': 'https://servicewechat.com/wx76ae3e9c8337d8d2/6/page-frame.html',
          'Accept-Encoding': 'gzip, deflate, br',
          'Accept-Language': 'zh-CN,zh;q=0.9'
        },
        onProxyReq: function(proxyReq, req, res) {
          // 修改请求方法为PUT
          proxyReq.method = 'PUT';

          // 如果客户端传递了Authorization头，使用它
          if (req.headers.authorization) {
            proxyReq.setHeader('Authorization', req.headers.authorization);
          }

          if (req.body) {
            const bodyData = JSON.stringify(req.body);
            // 设置内容长度
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            // 写入请求体
            proxyReq.write(bodyData);
            proxyReq.end();
          }
        }
      }
    }
  },
  chainWebpack: config => {
    config.module
      .rule('vue')
      .use('vue-loader')
      .tap(options => {
        // 添加编译时的优化选项
        return {
          ...options,
          compilerOptions: {
            ...options.compilerOptions,
            whitespace: 'condense',
            comments: false     // 移除注释
          }
        }
      })

    // 添加新的配置来抑制警告
    config.stats({
      warnings: false,
      warningsFilter: warning => {
        return warning.includes('non-passive')
      }
    })
  }
})
