<template>
  <div class="who-beat-me">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <Trophy />
              </el-icon>
              <span>看谁能打过</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 数据输入区域 -->
          <div class="data-input-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon>
                  <Document />
                </el-icon>
                <span>游戏数据</span>
              </div>
              <el-button type="danger" size="small" class="clear-button" @click="handleClearData" :icon="Delete">
                清空数据
              </el-button>
            </div>

            <!-- 游戏选择和ID输入框 -->
            <el-form :model="formData" label-position="top">
              <el-form-item label="选择游戏">
                <el-select
                  v-model="formData.selectedGame"
                  placeholder="请选择游戏"
                  @change="handleGameChange"
                  :disabled="loading.download || loading.upload"
                  class="game-select"
                  ref="gameSelectRef"
                  :style="{ width: gameSelectWidth }">
                  <el-option
                    v-for="game in gameOptions"
                    :key="game.value"
                    :label="game.label"
                    :value="game.value">
                    <span style="float: left">{{ game.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ game.path }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="游戏ID">
                <el-input v-model="formData.gameId" placeholder="请输入游戏ID" clearable
                  :disabled="loading.download || loading.upload" />
              </el-form-item>

              <el-form-item>
                <template #label>
                  <div class="form-label-with-button">
                    <span>授权令牌</span>
                    <el-button
                      size="small"
                      class="clear-token-btn"
                      @click="clearAuthToken"
                      :disabled="loading.download || loading.upload">
                      清空
                    </el-button>
                  </div>
                </template>
                <el-input v-model="formData.authorizationToken"
                  placeholder="请输入授权令牌"
                  type="textarea"
                  :rows="3"
                  clearable
                  :disabled="loading.download || loading.upload" />
              </el-form-item>
            </el-form>



            <!-- 操作按钮 -->
            <div class="action-section">
              <el-button type="primary" :loading="loading.download" @click="handleDownload" :icon="Download"
                class="action-button">
                下载数据
              </el-button>
              <el-button type="success" :loading="loading.upload" @click="handleUpload" :icon="Upload"
                :disabled="!formData.gameId || !formData.authorizationToken" class="action-button">
                上传数据
              </el-button>
            </div>

            <!-- 套餐按钮 -->
            <div class="package-buttons">
              <el-divider content-position="center">快速套餐</el-divider>
              <div class="button-group">
                <el-tooltip content="套餐一：30亿钻石+30亿体力+30亿金币" placement="top">
                  <el-button type="primary" :disabled="!gameData" @click="applyPackage1" class="package-button">
                    套餐一
                  </el-button>
                </el-tooltip>
                <el-tooltip content="套餐二：99亿钻石+99亿体力+99亿金币+解锁全部装备+9亿装备碎片" placement="top">
                  <el-button type="warning" :disabled="!gameData" @click="applyPackage2" class="package-button">
                    套餐二
                  </el-button>
                </el-tooltip>
              </div>
            </div>
          </div>

          <!-- 解密后的数据显示区域 -->
          <div v-if="gameData" class="game-data-section">
            <el-divider>
              <el-icon>
                <DataAnalysis />
              </el-icon>
              <span>游戏数据</span>
            </el-divider>

            <el-tabs type="border-card">
              <!-- 基础信息 -->
              <el-tab-pane label="基础信息">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="玩家ID">
                        <el-input v-model="gameData.playerId" placeholder="玩家ID"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="玩家昵称">
                        <el-input v-model="gameData.playerName" placeholder="玩家昵称"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="游戏版本">
                        <el-input v-model="gameData.gameVersion" placeholder="游戏版本"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="玩家等级">
                        <el-input-number v-model="gameData.playerLevel" :min="1" :max="100"
                          placeholder="玩家等级"></el-input-number>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="段位等级">
                        <el-select v-model="gameData.rankLevel" placeholder="请选择段位">
                          <el-option v-for="item in rankOptions" :key="item.value" :label="item.label"
                            :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="注册时间">
                        <el-date-picker v-model="gameData.registerTime" type="datetime" placeholder="选择注册时间"
                          format="YYYY-MM-DD HH:mm:ss" value-format="X">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 资源信息 -->
              <el-tab-pane label="资源信息">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>金币</span>
                            <el-tag size="small" type="warning">基础货币</el-tag>
                          </div>
                        </template>
                        <el-form-item label="当前金币数量">
                          <el-input-number v-model="gameData.goldCoins" :min="0" :step="1000"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>钻石</span>
                            <el-tag size="small" type="danger">高级货币</el-tag>
                          </div>
                        </template>
                        <el-form-item label="当前钻石数量">
                          <el-input-number v-model="gameData.diamonds" :min="0" :step="100"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>体力</span>
                            <el-tag size="small" type="info">消耗品</el-tag>
                          </div>
                        </template>
                        <el-form-item label="当前体力值">
                          <el-input-number v-model="gameData.energy" :min="0" :step="10"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20" class="mt-3">
                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>经验值</span>
                          </div>
                        </template>
                        <el-form-item label="当前经验值">
                          <el-input-number v-model="gameData.experience" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>战斗力</span>
                          </div>
                        </template>
                        <el-form-item label="当前战斗力">
                          <el-input-number v-model="gameData.battlePower" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>连胜次数</span>
                          </div>
                        </template>
                        <el-form-item label="当前连胜次数">
                          <el-input-number v-model="gameData.winStreak" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 装备信息 -->
              <el-tab-pane label="装备信息">
                <el-alert title="装备解锁状态" type="info" :closable="false" class="mb-3">
                  可以解锁或修改装备属性
                </el-alert>

                <!-- 装备管理按钮区域 -->
                <div class="equipment-actions mb-3">
                  <el-button
                    type="primary"
                    @click="autoAddEquipment"
                    :disabled="!gameData || loading.download || loading.upload"
                    icon="DataAnalysis">
                    自动增加装备
                  </el-button>
                  <el-button
                    type="success"
                    @click="showManualAddDialog"
                    :disabled="!gameData || loading.download || loading.upload"
                    icon="Document">
                    手动增加
                  </el-button>
                  <el-button
                    type="warning"
                    @click="resetAllEquipment"
                    :disabled="!gameData || loading.download || loading.upload"
                    icon="Delete">
                    重置装备
                  </el-button>
                </div>

                <el-table :data="equipmentList" stripe style="width: 100%">
                  <el-table-column label="装备ID" prop="id" width="80">
                    <template #default="scope">
                      <el-tag type="info">{{ scope.row.id }}</el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column label="装备名称" prop="name" width="120"></el-table-column>
                  <el-table-column label="碎片数量" prop="fragments" width="120">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.fragments" :min="0" :max="999999999" size="small"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="等级" prop="level" width="100">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.level" :min="0" :max="10" size="small"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否解锁" prop="unlocked" width="120">
                    <template #default="scope">
                      <el-switch v-model="scope.row.unlocked" active-text="已解锁" inactive-text="未解锁" size="small"></el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column label="战斗力加成" prop="powerBonus" width="120">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.powerBonus" :min="0" size="small"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="类型" prop="type" width="80"></el-table-column>
                </el-table>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 数据未加载时显示 -->
          <div v-if="!gameData && !loading.download" class="no-data-section">
            <el-empty description="请输入游戏ID并下载数据"></el-empty>
          </div>

          <!-- 加载中显示 -->
          <div v-if="loading.download && !gameData" class="loading-section">
            <el-skeleton :rows="10" animated />
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 手动增加装备对话框 -->
    <el-dialog
      v-model="manualAddDialog.visible"
      title="手动增加装备"
      width="600px"
      :close-on-click-modal="false">
      <el-form :model="manualAddDialog.form" label-width="100px">
        <el-form-item label="选择装备">
          <el-select
            v-model="manualAddDialog.form.selectedIds"
            multiple
            placeholder="请选择要添加的装备"
            style="width: 100%">
            <el-option
              v-for="equip in availableEquipmentOptions"
              :key="equip.id"
              :label="`${equip.id} - ${equip.name} (${equip.type})`"
              :value="equip.id"
              :disabled="isEquipmentExists(equip.id)">
              <span style="float: left">{{ equip.id }} - {{ equip.name }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ equip.type }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="初始等级">
          <el-input-number
            v-model="manualAddDialog.form.level"
            :min="0"
            :max="10"
            placeholder="装备初始等级" />
        </el-form-item>

        <el-form-item label="碎片数量">
          <el-input-number
            v-model="manualAddDialog.form.fragments"
            :min="0"
            :max="999999999"
            placeholder="装备碎片数量" />
        </el-form-item>

        <el-form-item label="解锁状态">
          <el-switch
            v-model="manualAddDialog.form.unlocked"
            active-text="已解锁"
            inactive-text="未解锁" />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="manualAddDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="confirmManualAdd">确定添加</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import {
  Trophy,
  Document,
  Delete,
  Download,
  Upload,
  DataAnalysis
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 游戏选项
const gameOptions = [
  {
    value: 'who-beat-me',
    label: '看谁能打过',
    path: '/218/',
    defaultGameId: '68173379'
  },
  {
    value: 'cant-beat-me',
    label: '根本打不过',
    path: '/215/',
    defaultGameId: '68819796'
  }
]

// 表单数据
const formData = reactive({
  selectedGame: 'who-beat-me', // 默认选择"看谁能打过"
  gameId: '68173379',
  authorizationToken: 'q-sign-algorithm=sha1&q-ak=AKIDRgfPtxmMm491VryoPkEZgZDSXMvEKBmc&q-sign-time=1749025564;1749061624&q-key-time=1749025564;1749061624&q-header-list=&q-url-param-list=&q-signature=78a240f5836918e1d1a95bd336b62a66127dd4f4'
})

// 加载状态
const loading = reactive({
  download: false,
  upload: false
})

// 游戏数据
const gameData = ref(null)

// 游戏选择框引用
const gameSelectRef = ref(null)

// 计算游戏选择框宽度
const gameSelectWidth = computed(() => {
  const selectedGame = gameOptions.find(game => game.value === formData.selectedGame)
  if (selectedGame) {
    // 根据文字长度计算宽度，每个中文字符约14px，英文字符约8px
    const textLength = selectedGame.label.length
    const estimatedWidth = textLength * 14 + 40 // 40px为箭头和padding的空间
    return `${Math.max(estimatedWidth, 120)}px` // 最小120px
  }
  return '120px'
})



// 装备列表
const equipmentList = ref([
  { id: 1, name: '钢铁长剑', level: 1, unlocked: true, powerBonus: 50, type: '武器' },
  { id: 2, name: '皮革护甲', level: 1, unlocked: true, powerBonus: 30, type: '防具' },
  { id: 3, name: '神秘面具', level: 1, unlocked: false, powerBonus: 25, type: '头盔' },
  { id: 4, name: '龙鳞战靴', level: 1, unlocked: false, powerBonus: 20, type: '鞋子' },
  { id: 5, name: '黑曜石刀', level: 1, unlocked: false, powerBonus: 80, type: '武器' },
  { id: 6, name: '精灵披风', level: 1, unlocked: false, powerBonus: 35, type: '披风' },
  { id: 7, name: '星辰护腕', level: 1, unlocked: false, powerBonus: 15, type: '手腕' },
  { id: 8, name: '勇士徽章', level: 1, unlocked: false, powerBonus: 40, type: '饰品' }
])

// 手动增加装备对话框数据
const manualAddDialog = reactive({
  visible: false,
  form: {
    selectedIds: [],
    level: 1,
    fragments: 100,
    unlocked: true
  }
})

// 所有可用装备选项 (ID 1-27)
const availableEquipmentOptions = computed(() => {
  const allEquipment = []
  for (let id = 1; id <= 27; id++) {
    allEquipment.push({
      id: id,
      name: getEquipName(id),
      type: getEquipType(id)
    })
  }
  return allEquipment
})

// 检查装备是否已存在
const isEquipmentExists = (id) => {
  return equipmentList.value.some(equip => equip.id === id)
}

// 段位选项
const rankOptions = [
  { value: 0, label: '青铜' },
  { value: 1, label: '白银' },
  { value: 2, label: '黄金' },
  { value: 3, label: '铂金' },
  { value: 4, label: '钻石' },
  { value: 5, label: '大师' },
  { value: 6, label: '宗师' },
  { value: 7, label: '王者' }
]

// 处理游戏切换
const handleGameChange = (gameValue) => {
  const selectedGame = gameOptions.find(game => game.value === gameValue)
  if (selectedGame) {
    formData.gameId = selectedGame.defaultGameId
    // 清空当前游戏数据
    gameData.value = null
    ElMessage.info(`已切换到 ${selectedGame.label}`)
  }
}

// 清空授权令牌
const clearAuthToken = () => {
  if (!formData.authorizationToken) {
    ElMessage.info('授权令牌已经为空')
    return
  }

  ElMessageBox.confirm('确定要清空授权令牌吗？', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formData.authorizationToken = ''
    ElMessage.success('授权令牌已清空')
  }).catch(() => {
    // 用户取消操作
  })
}

// 处理下载数据
const handleDownload = async () => {
  if (!formData.gameId) {
    ElMessage.warning('请输入游戏ID')
    return
  }

  if (!formData.selectedGame) {
    ElMessage.warning('请选择游戏')
    return
  }

  loading.download = true

  try {
    // 根据选择的游戏构建下载URL
    const timestamp = Date.now()
    const url = `/${formData.selectedGame}/download/${formData.gameId}_Yp_Default.json?timestamp=${timestamp}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': '*/*'
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('HTTP错误响应:', errorText)
      throw new Error(`HTTP error! Status: ${response.status}`)
    }

    // 检查响应内容类型
    const contentType = response.headers.get('content-type')
    console.log('响应内容类型:', contentType)

    // 获取响应文本
    const responseText = await response.text()
    console.log('响应原始内容:', responseText.substring(0, 200) + '...')

    // 检查是否是HTML响应
    if (responseText.trim().startsWith('<!') || responseText.trim().startsWith('<html')) {
      console.error('收到HTML响应而不是JSON:', responseText.substring(0, 500))
      throw new Error('服务器返回了HTML页面而不是JSON数据，请检查代理配置或网络连接')
    }

    // 尝试解析JSON
    let result
    try {
      result = JSON.parse(responseText)
    } catch (parseError) {
      console.error('JSON解析失败:', parseError)
      console.error('响应内容:', responseText)
      throw new Error('服务器返回的数据格式不正确，无法解析为JSON')
    }

    console.log('API返回原始数据:', result)

    // 解析返回的游戏数据
    const rawData = JSON.parse(result.data)
    const playerDataGlobal = JSON.parse(rawData.playerData.global)

    // 提取装备数据
    const equipData = playerDataGlobal.equipSaveData || {}
    const equipList = []

    // 初始化装备数据列表
    Object.keys(equipData).forEach(id => {
      const equip = equipData[id]
      const equipType = getEquipType(parseInt(id))
      const fragmentCount = playerDataGlobal.propMap[id] || 0 // 从propMap获取装备碎片数量

      equipList.push({
        id: parseInt(id),
        name: getEquipName(parseInt(id)),
        level: equip.level || 0,
        unlocked: equip.isUnLock || false,
        powerBonus: getPowerBonus(parseInt(id), equip.level || 0),
        type: equipType,
        fragments: fragmentCount // 添加装备碎片数量
      })
    })

    // 设置游戏数据
    gameData.value = {
      playerId: formData.gameId,
      playerName: '玩家_' + formData.gameId.substring(0, 6),
      gameVersion: result.version || '1.0.0',
      playerLevel: 1, // 默认等级
      rankLevel: playerDataGlobal.playerJieJi || 0,
      registerTime: Math.floor(playerDataGlobal.playerStartTime / 1000) || Math.floor(Date.now() / 1000),
      goldCoins: playerDataGlobal.propMap['1001'] || 0,
      diamonds: playerDataGlobal.propMap['1002'] || 0,
      energy: playerDataGlobal.propMap['1003'] || 0,
      experience: 0, // 默认经验值
      battlePower: calculateBattlePower(playerDataGlobal),
      winStreak: 0,
      // 原始数据，以便上传时使用
      originalData: result
    }

    // 更新装备列表
    equipmentList.value = equipList

    ElMessage.success('数据下载成功')
  } catch (error) {
    console.error('下载数据失败:', error)

    // 根据错误类型提供更具体的错误信息
    let errorMessage = '下载数据失败，请重试'

    if (error.message.includes('HTML页面')) {
      errorMessage = '网络配置错误：服务器返回了错误页面，请检查网络连接或联系管理员'
    } else if (error.message.includes('JSON')) {
      errorMessage = '数据格式错误：服务器返回的数据格式不正确'
    } else if (error.message.includes('HTTP error')) {
      errorMessage = `网络错误：${error.message}`
    } else if (error.message.includes('Failed to fetch')) {
      errorMessage = '网络连接失败：无法连接到服务器，请检查网络连接'
    }

    ElMessage.error(errorMessage)
  } finally {
    loading.download = false
  }
}



// 处理上传数据
const handleUpload = async () => {
  if (!gameData.value) {
    ElMessage.warning('没有数据可以上传')
    return
  }

  if (!formData.authorizationToken) {
    ElMessage.warning('请输入授权令牌')
    return
  }

  loading.upload = true

  try {
    ElMessage.info('准备上传数据...')

    // 准备上传数据
    const originalData = gameData.value.originalData

    if (!originalData) {
      throw new Error('原始数据不存在，无法上传')
    }

    // 更新原始数据中的值
    const rawData = JSON.parse(originalData.data)
    const playerDataGlobal = JSON.parse(rawData.playerData.global)

    // 更新金币、钻石和体力
    playerDataGlobal.propMap['1001'] = gameData.value.goldCoins
    playerDataGlobal.propMap['1002'] = gameData.value.diamonds
    playerDataGlobal.propMap['1003'] = gameData.value.energy

    // 更新段位
    playerDataGlobal.playerJieJi = gameData.value.rankLevel

    // 更新装备解锁状态和碎片数量
    equipmentList.value.forEach(equip => {
      // 更新装备状态
      if (playerDataGlobal.equipSaveData[equip.id]) {
        playerDataGlobal.equipSaveData[equip.id].level = equip.level
        playerDataGlobal.equipSaveData[equip.id].isUnLock = equip.unlocked
      } else {
        playerDataGlobal.equipSaveData[equip.id] = {
          level: equip.level,
          isUnLock: equip.unlocked
        }
      }

      // 更新装备碎片数量
      if (equip.fragments !== undefined) {
        playerDataGlobal.propMap[equip.id.toString()] = equip.fragments
      }
    })

    // 转回字符串
    rawData.playerData.global = JSON.stringify(playerDataGlobal)
    rawData.playerData['playerData-release-global'] = JSON.stringify(playerDataGlobal)
    originalData.data = JSON.stringify(rawData)

    // 根据选择的游戏构建上传URL
    const uploadUrl = `/${formData.selectedGame}/upload/${formData.gameId}_Yp_Default.json`

    console.log('上传URL:', uploadUrl)
    console.log('授权信息:', formData.authorizationToken)

    const uploadResponse = await fetch(uploadUrl, {
      method: 'POST', // 代理会转换为PUT
      headers: {
        'Authorization': formData.authorizationToken,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(originalData)
    })

    if (!uploadResponse.ok) {
      throw new Error(`上传失败: ${uploadResponse.status}`)
    }

    ElMessage.success('数据上传成功')
  } catch (error) {
    console.error('上传数据失败:', error)
    ElMessage.error('上传数据失败: ' + error.message)
  } finally {
    loading.upload = false
  }
}

// 辅助函数 - 根据装备ID获取装备名称
const getEquipName = (id) => {
  const equipNames = {
    1: '初级刀',
    2: '初级盾牌',
    3: '中级刀',
    4: '初级衣',
    5: '中级衣',
    6: '高级衣',
    7: '初级头盔',
    8: '中级头盔',
    9: '初级鞋子',
    10: '中级鞋子',
    11: '高级鞋子',
    12: '传说鞋子',
    13: '初级护腕',
    14: '初级项链',
    15: '初级戒指',
    16: '中级戒指',
    17: '高级戒指',
    18: '高级刀',
    19: '初级盾',
    20: '传说刀',
    21: '中级盾',
    22: '高级盾',
    23: '高级头盔',
    24: '中级护腕',
    25: '高级护腕',
    26: '中级项链',
    27: '高级项链'
  }

  return equipNames[id] || `装备${id}`
}

// 辅助函数 - 根据装备ID获取装备类型
const getEquipType = (id) => {
  if ([1, 3, 18, 20].includes(id)) return '武器'
  if ([4, 5, 6].includes(id)) return '衣服'
  if ([7, 8, 23].includes(id)) return '头盔'
  if ([9, 10, 11, 12].includes(id)) return '鞋子'
  if ([2, 19, 21, 22].includes(id)) return '盾牌'
  if ([13, 24, 25].includes(id)) return '护腕'
  if ([14, 26, 27].includes(id)) return '项链'
  if ([15, 16, 17].includes(id)) return '戒指'

  return '其他'
}

// 辅助函数 - 计算装备战力加成
const getPowerBonus = (id, level) => {
  // 基础战力值
  const basePower = {
    1: 20, 3: 40, 18: 80, 20: 100,     // 武器
    4: 15, 5: 30, 6: 60,               // 衣服
    7: 10, 8: 20, 23: 40,              // 头盔
    9: 10, 10: 20, 11: 40, 12: 60,     // 鞋子
    2: 12, 19: 15, 21: 30, 22: 60,     // 盾牌
    13: 5, 24: 10, 25: 20,             // 护腕
    14: 5, 26: 10, 27: 20,             // 项链
    15: 5, 16: 8, 17: 12               // 戒指
  }

  const base = basePower[id] || 10
  // 每级增加10%
  return Math.floor(base * (1 + level * 0.1))
}

// 计算总战斗力
const calculateBattlePower = (playerData) => {
  let power = 100 // 基础战力

  // 添加装备战力
  if (playerData.equipSaveData) {
    Object.keys(playerData.equipSaveData).forEach(id => {
      const equip = playerData.equipSaveData[id]
      if (equip.isUnLock) {
        power += getPowerBonus(parseInt(id), equip.level || 0)
      }
    })
  }

  // 考虑段位加成
  power += (playerData.playerJieJi || 0) * 50

  return power
}

// 清空数据
const handleClearData = () => {
  if (!gameData.value && !formData.gameId && !formData.authorizationToken) return

  ElMessageBox.confirm('确定要清空当前数据和输入框内容吗？', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    gameData.value = null
    // 根据当前选择的游戏重置默认游戏ID
    const selectedGame = gameOptions.find(game => game.value === formData.selectedGame)
    if (selectedGame) {
      formData.gameId = selectedGame.defaultGameId
    } else {
      formData.gameId = '68173379' // 默认值
    }
    formData.authorizationToken = 'q-sign-algorithm=sha1&q-ak=AKIDRgfPtxmMm491VryoPkEZgZDSXMvEKBmc&q-sign-time=1749025564;1749061624&q-key-time=1749025564;1749061624&q-header-list=&q-url-param-list=&q-signature=78a240f5836918e1d1a95bd336b62a66127dd4f4'
    ElMessage.success('数据已清空，输入框已重置为默认值')
  }).catch(() => { })
}

// 应用套餐1: 30亿钻石+30亿体力+30亿金币
const applyPackage1 = () => {
  if (!gameData.value) return

  // 设置30亿资源
  gameData.value.goldCoins = 3000000000    // 30亿金币
  gameData.value.diamonds = 3000000000     // 30亿钻石
  gameData.value.energy = 3000000000       // 30亿体力

  ElMessage.success('已应用套餐一: 30亿钻石+30亿体力+30亿金币')
}

// 应用套餐2: 99亿钻石+99亿体力+99亿金币+解锁全部装备+全部装备碎片
const applyPackage2 = () => {
  if (!gameData.value) return

  // 设置99亿资源
  gameData.value.goldCoins = 9900000000   // 99亿金币
  gameData.value.diamonds = 9900000000    // 99亿钻石
  gameData.value.energy = 9900000000      // 99亿体力

  // 解锁全部装备并设置全部装备碎片
  equipmentList.value.forEach(item => {
    item.unlocked = true           // 解锁装备
    item.level = 10               // 最高等级
    item.fragments = 900000000    // 9亿装备碎片
    // 不修改 powerBonus，保持原有战力加成
  })

  // 设置最高段位
  gameData.value.rankLevel = 7 // 王者段位

  // 不修改 battlePower，保持原有战斗力

  ElMessage.success('已应用套餐二: 99亿资源+解锁全部装备+9亿装备碎片')
}

// 自动增加装备 (ID 1-27)
const autoAddEquipment = () => {
  if (!gameData.value) {
    ElMessage.warning('请先下载游戏数据')
    return
  }

  ElMessageBox.confirm('确定要自动添加所有装备(ID 1-27)吗？', '确认操作', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  }).then(() => {
    let addedCount = 0

    // 添加ID 1-27的所有装备
    for (let id = 1; id <= 27; id++) {
      // 检查装备是否已存在
      if (!isEquipmentExists(id)) {
        const newEquipment = {
          id: id,
          name: getEquipName(id),
          level: 1,
          unlocked: true,
          powerBonus: getPowerBonus(id, 1),
          type: getEquipType(id),
          fragments: 100
        }

        equipmentList.value.push(newEquipment)
        addedCount++
      }
    }

    // 按ID排序
    equipmentList.value.sort((a, b) => a.id - b.id)

    if (addedCount > 0) {
      ElMessage.success(`成功添加 ${addedCount} 件装备`)
    } else {
      ElMessage.info('所有装备已存在，无需添加')
    }
  }).catch(() => {
    // 用户取消操作
  })
}

// 显示手动增加装备对话框
const showManualAddDialog = () => {
  if (!gameData.value) {
    ElMessage.warning('请先下载游戏数据')
    return
  }

  // 重置表单
  manualAddDialog.form.selectedIds = []
  manualAddDialog.form.level = 1
  manualAddDialog.form.fragments = 100
  manualAddDialog.form.unlocked = true

  manualAddDialog.visible = true
}

// 确认手动添加装备
const confirmManualAdd = () => {
  if (manualAddDialog.form.selectedIds.length === 0) {
    ElMessage.warning('请选择要添加的装备')
    return
  }

  let addedCount = 0

  manualAddDialog.form.selectedIds.forEach(id => {
    if (!isEquipmentExists(id)) {
      const newEquipment = {
        id: id,
        name: getEquipName(id),
        level: manualAddDialog.form.level,
        unlocked: manualAddDialog.form.unlocked,
        powerBonus: getPowerBonus(id, manualAddDialog.form.level),
        type: getEquipType(id),
        fragments: manualAddDialog.form.fragments
      }

      equipmentList.value.push(newEquipment)
      addedCount++
    }
  })

  // 按ID排序
  equipmentList.value.sort((a, b) => a.id - b.id)

  if (addedCount > 0) {
    ElMessage.success(`成功添加 ${addedCount} 件装备`)
    manualAddDialog.visible = false
  } else {
    ElMessage.warning('选择的装备已存在')
  }
}

// 重置所有装备
const resetAllEquipment = () => {
  if (!gameData.value) {
    ElMessage.warning('请先下载游戏数据')
    return
  }

  ElMessageBox.confirm('确定要重置所有装备吗？这将清空当前装备列表。', '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    equipmentList.value = []
    ElMessage.success('装备列表已重置')
  }).catch(() => {
    // 用户取消操作
  })
}
</script>

<style scoped>
.who-beat-me {
  padding: 20px;
}

/* 游戏选择框样式 */
.game-select {
  display: inline-block;
}

.game-select :deep(.el-input__wrapper) {
  transition: width 0.3s ease; /* 添加宽度变化的过渡效果 */
}

/* 确保下拉选项的样式 */
.game-select :deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  white-space: nowrap;
}

/* 表单标签与按钮的布局 */
.form-label-with-button {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* 清空授权令牌按钮样式 */
.clear-token-btn {
  border: 1px dashed #d9d9d9 !important;
  background-color: #fafafa !important;
  color: #666 !important;
  font-size: 12px;
  padding: 4px 8px !important;
  height: auto !important;
  line-height: 1.2;
  margin-left: 12px;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.clear-token-btn:hover {
  border-color: #409eff !important;
  color: #409eff !important;
  background-color: #ecf5ff !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.clear-token-btn:active {
  border-color: #337ecc !important;
  color: #337ecc !important;
  background-color: #d9ecff !important;
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(64, 158, 255, 0.3);
}

.clear-token-btn:disabled {
  border-color: #e4e7ed !important;
  color: #c0c4cc !important;
  background-color: #f5f7fa !important;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.main-card {
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #ebeef5;
}

.title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.title .el-icon {
  margin-right: 10px;
  font-size: 24px;
  color: #409EFF;
}

.status-tag {
  padding: 0 10px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: bold;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.action-section {
  display: flex;
  justify-content: space-between;
  margin: 20px 0;
}

.action-button {
  flex: 1;
  margin: 0 10px;
}

.action-button:first-child {
  margin-left: 0;
}

.action-button:last-child {
  margin-right: 0;
}

.package-buttons {
  margin-top: 20px;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.package-button {
  flex: 1;
  margin: 0 10px;
}

.package-button:first-child {
  margin-left: 0;
}

.package-button:last-child {
  margin-right: 0;
}

.game-data-section {
  margin-top: 30px;
}

.resource-card {
  margin-bottom: 15px;
  transition: all 0.3s;
}

.resource-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.mt-3 {
  margin-top: 15px;
}

.mb-3 {
  margin-bottom: 15px;
}

/* 装备管理按钮区域样式 */
.equipment-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  padding: 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.equipment-actions .el-button {
  flex: 1;
  min-width: 120px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.equipment-actions .el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.time-editor {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.edit-btn {
  margin-left: 10px;
}

.el-table {
  margin-top: 15px;
}

/* 授权状态样式 */
.authorization-status {
  margin: 20px 0;
}

.auth-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.auth-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.auth-info {
  padding: 10px;
}

.auth-status-indicator {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.8);
}

.auth-status-text {
  margin-left: 10px;
  font-weight: 600;
  font-size: 16px;
}

.success-icon {
  color: #67c23a;
  font-size: 20px;
}

.error-icon {
  color: #f56c6c;
  font-size: 20px;
}

.pending-icon {
  color: #e6a23c;
  font-size: 20px;
}

.auth-details {
  margin-top: 15px;
}

.auth-token {
  font-family: monospace;
  background-color: #f0f9ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #e1f5fe;
}

.session-id {
  font-family: monospace;
  background-color: #f0f4ff;
  padding: 2px 6px;
  border-radius: 4px;
  border: 1px solid #d4e7ff;
  font-weight: 600;
}

/* 改进操作按钮样式 */
.action-section {
  display: flex;
  justify-content: space-between;
  margin: 25px 0;
  gap: 15px;
}

.action-button {
  flex: 1;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 改进卡片样式 */
.resource-card {
  margin-bottom: 15px;
  transition: all 0.3s ease;
  border-radius: 10px;
  overflow: hidden;
}

.resource-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* 改进主卡片样式 */
.main-card {
  margin-bottom: 20px;
  border-radius: 12px;
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.main-card :deep(.el-card__body) {
  background: white;
  color: #303133;
  border-radius: 0 0 12px 12px;
}

/* 改进标题样式 */
.title {
  display: flex;
  align-items: center;
  font-size: 22px;
  font-weight: bold;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title .el-icon {
  margin-right: 12px;
  font-size: 28px;
  color: #ffd700;
}

/* 响应式样式 */
@media screen and (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }

  .package-button {
    margin: 5px 0;
  }

  .action-section {
    flex-direction: column;
    gap: 10px;
  }

  .action-button {
    margin: 0;
  }

  .auth-status-indicator {
    flex-direction: column;
    text-align: center;
  }

  .auth-status-text {
    margin-left: 0;
    margin-top: 8px;
  }
}
</style>