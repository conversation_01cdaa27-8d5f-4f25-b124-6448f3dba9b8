<template>
  <div class="game-data-editor">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="基本信息" name="basic">
        <el-form :model="gameData" label-width="120px">
          <el-form-item label="玩家ID">
            <el-input v-model="gameData.playerID" disabled></el-input>
          </el-form-item>
          <el-form-item label="等级">
            <el-input-number v-model="gameData.level" :min="-1"></el-input-number>
          </el-form-item>
          <el-form-item label="金币">
            <el-input-number v-model="gameData.money" :min="0"></el-input-number>
          </el-form-item>
          <el-form-item label="宝石">
            <el-input-number v-model="gameData.gem" :min="0"></el-input-number>
          </el-form-item>
          <el-form-item label="体力">
            <el-input-number v-model="gameData.tiLiNum" :min="0"></el-input-number>
          </el-form-item>
        </el-form>
      </el-tab-pane>

      <el-tab-pane label="角色信息" name="characters">
        <el-table :data="gameData.roleData">
          <el-table-column prop="id" label="ID"></el-table-column>
          <el-table-column prop="grade" label="等级">
            <template #default="scope">
              <el-input-number v-model="scope.row.grade" :min="1"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="star" label="星级">
            <template #default="scope">
              <el-input-number v-model="scope.row.star" :min="1"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="isUse" label="是否使用">
            <template #default="scope">
              <el-switch v-model="scope.row.isUse" :active-value="1" :inactive-value="0"></el-switch>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <el-tab-pane label="技能信息" name="skills">
        <el-table :data="gameData.skillData">
          <el-table-column prop="id" label="ID"></el-table-column>
          <el-table-column prop="lv" label="等级">
            <template #default="scope">
              <el-input-number v-model="scope.row.lv" :min="1"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column prop="unlock" label="是否解锁">
            <template #default="scope">
              <el-switch v-model="scope.row.unlock" :active-value="1" :inactive-value="0"></el-switch>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>

      <!-- 可以根据需要添加更多的标签页来显示其他数据 -->
    </el-tabs>

    <el-button type="primary" @click="saveData">保存修改</el-button>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'GameDataEditor',
  props: {
    initialData: {
      type: Object,
      required: true
    }
  },
  setup(props, { emit }) {
    const activeTab = ref('basic')
    const gameData = reactive(JSON.parse(JSON.stringify(props.initialData)))

    const saveData = () => {
      emit('save', gameData)
    }

    return {
      activeTab,
      gameData,
      saveData
    }
  }
}
</script>

<style scoped>
.game-data-editor {
  margin-top: 20px;
}
</style>
