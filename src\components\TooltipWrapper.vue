<template>
  <div class="tooltip-wrapper">
    <div class="tooltip-content" @mouseenter="showTooltip" @mouseleave="hideTooltip">
      <slot></slot>
    </div>
    <div v-if="isVisible" class="tooltip" :style="tooltipStyle">
      {{ content }}
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'TooltipWrapper',
  props: {
    content: {
      type: String,
      required: true
    }
  },
  setup() {
    const isVisible = ref(false)
    const tooltipPosition = ref({ x: 0, y: 0 })

    const showTooltip = (event) => {
      isVisible.value = true
      tooltipPosition.value = { x: event.clientX, y: event.clientY }
    }

    const hideTooltip = () => {
      isVisible.value = false
    }

    const tooltipStyle = computed(() => ({
      left: `${tooltipPosition.value.x}px`,
      top: `${tooltipPosition.value.y - 40}px`
    }))

    return {
      isVisible,
      showTooltip,
      hideTooltip,
      tooltipStyle
    }
  }
}
</script>

<style scoped>
.tooltip-wrapper {
  position: relative;
  display: inline-block;
  width: 100%;
}

.tooltip-content {
  width: 100%;
}

.tooltip {
  position: fixed;
  background-color: rgba(74, 85, 104, 0.6); /* 修改这里，使用 rgba 来设置透明度 */
  color: #fff;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
  max-width: 200px;
  word-wrap: break-word;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
