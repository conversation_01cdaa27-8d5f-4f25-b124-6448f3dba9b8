<template>
  <div class="slow-piggy">
    <el-row :gutter="20" justify="center">
      <el-col :xs="24" :sm="24" :md="20" :lg="16" :xl="14">
        <!-- 主卡片 -->
        <el-card class="main-card">
          <!-- 头部标题 -->
          <div class="header-section">
            <div class="title">
              <el-icon>
                <Chicken />
              </el-icon>
              <span>慢豚豚的生活</span>
            </div>
            <el-tag type="success" effect="dark" class="status-tag">在线</el-tag>
          </div>

          <!-- 数据输入区域 -->
          <div class="data-input-section">
            <div class="section-header">
              <div class="section-title">
                <el-icon>
                  <Document />
                </el-icon>
                <span>游戏数据</span>
              </div>
              <el-button type="danger" size="small" class="clear-button" @click="handleClearData" :icon="Delete">
                清空数据
              </el-button>
            </div>

            <!-- OpenID输入框 -->
            <el-form :model="formData" label-position="top">
              <el-form-item label="OpenID">
                <el-input v-model="formData.openId" placeholder="请输入OpenID" clearable
                  :disabled="loading.download || loading.upload" />
              </el-form-item>
            </el-form>

            <!-- 操作按钮 -->
            <div class="action-section">
              <el-button type="primary" :loading="loading.download" @click="handleDownload" :icon="Download"
                class="action-button">
                下载数据
              </el-button>
              <el-button type="success" :loading="loading.upload" @click="handleUpload" :icon="Upload"
                :disabled="!formData.openId" class="action-button">
                上传数据
              </el-button>
            </div>
          </div>

          <!-- 解密后的数据显示区域 -->
          <div v-if="gameData" class="game-data-section">
            <el-divider>
              <el-icon>
                <DataAnalysis />
              </el-icon>
              <span>游戏数据</span>
            </el-divider>

            <el-tabs type="border-card">
              <!-- 基础信息 -->
              <el-tab-pane label="基础信息">
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="玩家ID (gmOpenId)">
                        <el-input v-model="gameData.gmOpenId" placeholder="玩家ID"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="玩家昵称">
                        <el-input v-model="gameData.playerData.playerName" placeholder="玩家昵称"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="游戏版本">
                        <el-input v-model="gameData.gameVersion" placeholder="游戏版本"></el-input>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="玩家等级">
                        <el-input-number v-model="gameData.playerData.playerLv" :min="1" :max="100"
                          placeholder="玩家等级"></el-input-number>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="账号创建时间">
                        <el-date-picker v-model="gameData.createTime" type="datetime" placeholder="选择创建时间"
                          format="YYYY-MM-DD HH:mm:ss" value-format="unix">
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="创建版本">
                        <el-input v-model="gameData.createVersion" placeholder="创建版本"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20">
                    <el-col :span="12">
                      <el-form-item label="当前皮肤ID">
                        <el-input-number v-model="gameData.playerData.skinId" :min="1"
                          placeholder="皮肤ID"></el-input-number>
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="是否永久双倍收益">
                        <el-switch v-model="gameData.isDoubleForever" active-text="是" inactive-text="否"></el-switch>
                      </el-form-item>
                    </el-col>
                  </el-row>

                  <el-divider>其他玩家属性</el-divider>
                  <el-row :gutter="20">
                    <el-col :span="8" v-for="item in playerDataFiltered" :key="item.key">
                      <el-form-item :label="item.key">
                        <el-input v-model="gameData.playerData[item.key]"></el-input>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 资源信息 -->
              <el-tab-pane label="资源信息">
                <div class="packages-section mb-3 d-none">
                  <div class="section-header">
                    <div class="section-title">
                      <el-icon>
                        <ShoppingCart />
                      </el-icon>
                      <span>旧版套餐</span>
                    </div>
                  </div>
                </div>

                <div class="packages-section mb-3">
                  <div class="section-header">
                    <div class="section-title">
                      <el-icon>
                        <ShoppingCart />
                      </el-icon>
                      <span>套餐充值</span>
                    </div>
                  </div>
                  <el-row :gutter="10">
                    <el-col :span="8">
                      <el-button type="primary" @click="applyPackage(1)" :icon="ShoppingBag" class="package-button">
                        套餐一: 无限体力+金币
                      </el-button>
                    </el-col>
                    <el-col :span="8">
                      <el-button type="success" @click="applyPackage(2)" :icon="ShoppingBag" class="package-button">
                        套餐二: 无限体力+金币+红钞
                      </el-button>
                    </el-col>
                    <el-col :span="8">
                      <el-button type="danger" @click="applyPackage(3)" :icon="ShoppingBag" class="package-button">
                        套餐三: 无限全部
                      </el-button>
                    </el-col>
                  </el-row>
                </div>

                <div class="packages-section mb-3">
                  <div class="section-header">
                    <div class="section-title">
                      <el-icon>
                        <ShoppingCart />
                      </el-icon>
                      <span>高级套餐充值</span>
                    </div>
                  </div>
                  <el-row :gutter="16">
                    <el-col :span="8">
                      <div class="package-card package-basic" @click="applyPackage(1)">
                        <div class="package-icon">
                          <el-icon>
                            <Money />
                          </el-icon>
                        </div>
                        <div class="package-title">套餐一</div>
                        <div class="package-content">
                          <div class="package-item">
                            <el-icon>
                              <Lightning />
                            </el-icon>
                            <span>无限体力 (99999999)</span>
                          </div>
                          <div class="package-item">
                            <el-icon>
                              <Coin />
                            </el-icon>
                            <span>无限金币 (99999999)</span>
                          </div>
                        </div>
                        <div class="package-footer">
                          <el-button type="primary" size="small">立即充值</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="package-card package-standard" @click="applyPackage(2)">
                        <div class="package-icon">
                          <el-icon>
                            <GoldMedal />
                          </el-icon>
                        </div>
                        <div class="package-title">套餐二</div>
                        <div class="package-content">
                          <div class="package-item">
                            <el-icon>
                              <Lightning />
                            </el-icon>
                            <span>无限体力 (99999999)</span>
                          </div>
                          <div class="package-item">
                            <el-icon>
                              <Coin />
                            </el-icon>
                            <span>无限金币 (99999999)</span>
                          </div>
                          <div class="package-item">
                            <el-icon>
                              <Money />
                            </el-icon>
                            <span>无限红钞 (99999999)</span>
                          </div>
                        </div>
                        <div class="package-footer">
                          <el-button type="success" size="small">立即充值</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="package-card package-premium" @click="applyPackage(3)">
                        <el-tag class="package-tag" type="danger" effect="dark">热门</el-tag>
                        <div class="package-icon">
                          <el-icon>
                            <Trophy />
                          </el-icon>
                        </div>
                        <div class="package-title">套餐三 (全部)</div>
                        <div class="package-content">
                          <div class="package-item">
                            <el-icon>
                              <Lightning />
                            </el-icon>
                            <span>无限体力 (99999999)</span>
                          </div>
                          <div class="package-item">
                            <el-icon>
                              <Coin />
                            </el-icon>
                            <span>无限金币 (99999999)</span>
                          </div>
                          <div class="package-item">
                            <el-icon>
                              <Money />
                            </el-icon>
                            <span>无限红钞 (99999999)</span>
                          </div>
                          <div class="package-item">
                            <el-icon>
                              <Star />
                            </el-icon>
                            <span>无限精力 (99999999)</span>
                          </div>
                          <div class="package-item">
                            <el-icon>
                              <Plus />
                            </el-icon>
                            <span>其他资源 (99999999)</span>
                          </div>
                        </div>
                        <div class="package-footer">
                          <el-button type="danger" size="small">立即充值</el-button>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </div>
                <el-form :model="gameData" label-position="top">
                  <el-row :gutter="20">
                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>体力数据</span>
                            <el-tag size="small" type="success">基础资源</el-tag>
                          </div>
                        </template>
                        <el-form-item label="当前体力">
                          <el-input-number v-model="gameData.physicalData.physicalNum" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                        <el-form-item label="最后更新时间">
                          <el-date-picker v-model="gameData.physicalData.physicalTime" type="datetime"
                            placeholder="选择时间" format="YYYY-MM-DD HH:mm:ss" value-format="unix">
                          </el-date-picker>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>金币</span>
                            <el-tag size="small" type="warning">货币</el-tag>
                          </div>
                        </template>
                        <el-form-item label="当前金币数量">
                          <el-input-number v-model="gameData.moneyNum" :min="0" :step="100"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>红币</span>
                            <el-tag size="small" type="danger">高级货币</el-tag>
                          </div>
                        </template>
                        <el-form-item label="当前红币数量">
                          <el-input-number v-model="gameData.redMoneyNum" :min="0" :step="100"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20" class="mt-3">
                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>体力</span>
                          </div>
                        </template>
                        <el-form-item label="当前体力">
                          <el-input-number v-model="gameData.power" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>精力</span>
                          </div>
                        </template>
                        <el-form-item label="当前精力">
                          <el-input-number v-model="gameData.vigour" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>

                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>鱼饵数量</span>
                          </div>
                        </template>
                        <el-form-item label="当前鱼饵数量">
                          <el-input-number v-model="gameData.yuerCount" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                      </el-card>
                    </el-col>
                  </el-row>

                  <el-row :gutter="20" class="mt-3">
                    <el-col :span="8">
                      <el-card shadow="hover" class="resource-card">
                        <template #header>
                          <div class="card-header">
                            <span>蜜蜂能量值</span>
                          </div>
                        </template>
                        <el-form-item label="蜜蜂能量">
                          <el-input-number v-model="gameData.beePower" :min="0"
                            controls-position="right"></el-input-number>
                        </el-form-item>
                        <el-form-item label="蜜蜂功能">
                          <el-switch v-model="gameData.isOpenBeePower" active-text="已开启"
                            inactive-text="未开启"></el-switch>
                        </el-form-item>
                      </el-card>
                    </el-col>
                  </el-row>
                </el-form>
              </el-tab-pane>

              <!-- 农场信息 -->
              <el-tab-pane label="农场信息">
                <el-alert v-if="gameData.cropDatas && gameData.cropDatas.length > 0" title="农田情况" type="success"
                  :closable="false" class="mb-3">
                  共有 {{ gameData.cropDatas.length }} 块农田
                </el-alert>

                <el-table v-if="gameData.cropDatas && gameData.cropDatas.length > 0" :data="gameData.cropDatas" stripe>
                  <el-table-column label="农田ID" prop="cid">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.cid" :min="1" controls-position="right"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="等级" prop="level">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.level" :min="0" controls-position="right"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="池塘体力1" prop="poolPhy1">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.poolPhy1" :min="0"
                        controls-position="right"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="池塘体力2" prop="poolPhy2">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.poolPhy2" :min="0"
                        controls-position="right"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="最后添加体力时间" prop="lastAddPhyTime">
                    <template #default="scope">
                      <div class="time-editor">
                        {{ formatUnixTime(scope.row.lastAddPhyTime) }}
                        <el-button type="primary" size="small" icon="Edit" circle
                          @click="editTimeField(scope.row, 'lastAddPhyTime')" class="edit-btn">
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否领取体力" prop="isRecPhy">
                    <template #default="scope">
                      <el-switch v-model="scope.row.isRecPhy" :active-value="1" :inactive-value="0" active-text="已领取"
                        inactive-text="未领取">
                      </el-switch>
                    </template>
                  </el-table-column>
                  <el-table-column label="冷却时间" prop="cdTime">
                    <template #default="scope">
                      <el-input-number v-model="scope.row.cdTime" :min="0" controls-position="right"></el-input-number>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="120">
                    <template #default="scope">
                      <el-button type="danger" @click="removeCropData(scope.$index)" size="small">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>

                <div class="action-buttons mt-3 mb-3" v-if="gameData.cropDatas">
                  <el-button type="primary" @click="addCropData">添加农田</el-button>
                </div>

                <el-empty v-if="!gameData.cropDatas || gameData.cropDatas.length === 0" description="未找到农田数据" />

                <el-divider />

                <el-card class="mb-3">
                  <template #header>
                    <div class="card-header">
                      <span>已解锁作物</span>
                      <el-button type="primary" size="small" @click="addCropItem">添加作物</el-button>
                    </div>
                  </template>
                  <div v-if="gameData.unLockCropItemIds && gameData.unLockCropItemIds.length > 0">
                    <el-tag v-for="(item, index) in gameData.unLockCropItemIds" :key="index" class="mr-2 mb-2" closable
                      @close="removeCropItem(index)">
                      <el-input-number v-model="gameData.unLockCropItemIds[index]" :min="1" controls-position="right"
                        size="small"></el-input-number>
                    </el-tag>
                  </div>
                  <el-empty v-else description="未找到已解锁作物数据" />
                </el-card>
              </el-tab-pane>

              <!-- 仓库信息 -->
              <el-tab-pane label="仓库信息">
                <el-tabs>
                  <el-tab-pane label="小仓库 (boxInfos)">
                    <el-alert v-if="gameData.boxInfos && gameData.boxInfos.length > 0" title="仓库物品" type="info"
                      :closable="false" class="mb-3">
                      共有 {{ gameData.boxInfos.length }} 个物品格子
                    </el-alert>

                    <el-table v-if="gameData.boxInfos && gameData.boxInfos.length > 0" :data="gameData.boxInfos" stripe
                      height="400">
                      <el-table-column label="格子ID" prop="Id" width="100" />
                      <el-table-column label="物品ID" prop="ItemId" width="100" />
                      <el-table-column label="状态" width="120">
                        <template #default="scope">
                          <el-tag :type="getItemStatusType(scope.row.State)">
                            {{ getItemStatusText(scope.row.State) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="数量" prop="Count" width="100" />
                      <el-table-column label="其他属性" min-width="200">
                        <template #default="scope">
                          <div v-for="(value, key) in scope.row" :key="key"
                            :class="{ 'hidden': ['Id', 'ItemId', 'State', 'Count'].includes(key) }">
                            {{ key }}: {{ value }}
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>

                    <el-empty v-else description="未找到仓库数据" />
                  </el-tab-pane>

                  <el-tab-pane label="大仓库 (warehouseDatas)">
                    <el-alert v-if="gameData.warehouseDatas && gameData.warehouseDatas.length > 0" title="大仓库物品"
                      type="info" :closable="false" class="mb-3">
                      共有 {{ gameData.warehouseDatas.length }} 个物品格子
                    </el-alert>

                    <el-table v-if="gameData.warehouseDatas && gameData.warehouseDatas.length > 0"
                      :data="gameData.warehouseDatas" stripe height="400">
                      <el-table-column label="格子ID" prop="Id" width="100" />
                      <el-table-column label="物品ID" prop="ItemId" width="100" />
                      <el-table-column label="状态" width="120">
                        <template #default="scope">
                          <el-tag :type="getItemStatusType(scope.row.State)">
                            {{ getItemStatusText(scope.row.State) }}
                          </el-tag>
                        </template>
                      </el-table-column>
                      <el-table-column label="数量" prop="Count" width="100" />
                      <el-table-column label="其他属性" min-width="200">
                        <template #default="scope">
                          <div v-for="(value, key) in scope.row" :key="key"
                            :class="{ 'hidden': ['Id', 'ItemId', 'State', 'Count'].includes(key) }">
                            {{ key }}: {{ value }}
                          </div>
                        </template>
                      </el-table-column>
                    </el-table>

                    <el-empty v-else description="未找到大仓库数据" />
                  </el-tab-pane>
                </el-tabs>
              </el-tab-pane>

              <!-- 玩家信息 -->
              <el-tab-pane label="玩家信息">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>玩家基本信息</span>
                        </div>
                      </template>
                      <el-descriptions :column="1" border>
                        <el-descriptions-item label="玩家昵称">{{ gameData.playerData?.playerName || '-'
                        }}</el-descriptions-item>
                        <el-descriptions-item label="玩家等级">{{ gameData.playerData?.playerLv || '-'
                        }}</el-descriptions-item>
                        <el-descriptions-item label="当前皮肤ID">{{ gameData.playerData?.skinId || '-'
                        }}</el-descriptions-item>
                        <el-descriptions-item v-for="(value, key) in playerDataFiltered" :key="key" :label="key"
                          :class="{ 'hidden': ['playerName', 'playerLv', 'skinId'].includes(key) }">
                          {{ value }}
                        </el-descriptions-item>
                      </el-descriptions>
                    </el-card>
                  </el-col>

                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>皮肤列表</span>
                        </div>
                      </template>
                      <el-table v-if="gameData.skins && gameData.skins.length > 0" :data="gameData.skins" stripe
                        height="400">
                        <el-table-column label="皮肤ID" prop="id" />
                        <el-table-column label="状态">
                          <template #default="scope">
                            <el-tag :type="scope.row.state === 1 ? 'success' : 'info'">
                              {{ scope.row.state === 1 ? '已解锁' : '未解锁' }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="其他属性">
                          <template #default="scope">
                            <div
                              v-for="(value, key) in Object.entries(scope.row).filter(([k]) => !['id', 'state'].includes(k))"
                              :key="key">
                              {{ key }}: {{ value }}
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-empty v-else description="未找到皮肤数据" />
                    </el-card>
                  </el-col>
                </el-row>
              </el-tab-pane>

              <!-- 任务系统 -->
              <el-tab-pane label="任务系统">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>当前订单任务</span>
                        </div>
                      </template>
                      <el-table v-if="gameData.orderDatas && gameData.orderDatas.length > 0" :data="gameData.orderDatas"
                        stripe>
                        <el-table-column label="任务ID" prop="id" />
                        <el-table-column label="订单类型">
                          <template #default="scope">
                            {{ getOrderType(scope.row.type) }}
                          </template>
                        </el-table-column>
                        <el-table-column label="其他信息">
                          <template #default="scope">
                            <div
                              v-for="(value, key) in Object.entries(scope.row).filter(([k]) => !['id', 'type'].includes(k))"
                              :key="key">
                              {{ key }}: {{ value }}
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-empty v-else description="未找到订单任务数据" />
                    </el-card>
                  </el-col>

                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>已完成任务</span>
                        </div>
                      </template>
                      <div v-if="gameData.overOrderIds && gameData.overOrderIds.length > 0">
                        <el-tag v-for="item in gameData.overOrderIds" :key="item" class="mr-2 mb-2" type="success">
                          任务ID: {{ item }}
                        </el-tag>
                      </div>
                      <el-empty v-else description="未找到已完成任务数据" />
                    </el-card>
                  </el-col>
                </el-row>

                <el-card shadow="hover" class="mb-3">
                  <template #header>
                    <div class="card-header">
                      <span>订单类型列表</span>
                    </div>
                  </template>
                  <div v-if="gameData.orderTypes && gameData.orderTypes.length > 0">
                    <el-tag v-for="item in gameData.orderTypes" :key="item" class="mr-2 mb-2" type="info">
                      类型: {{ item }}
                    </el-tag>
                  </div>
                  <el-empty v-else description="未找到订单类型数据" />
                </el-card>
              </el-tab-pane>

              <!-- 教学系统 -->
              <el-tab-pane label="教学系统">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>当前教学任务</span>
                        </div>
                      </template>
                      <el-descriptions v-if="gameData.curTeachData" :column="1" border>
                        <el-descriptions-item label="任务ID">{{ gameData.curTeachData.id || '-' }}</el-descriptions-item>
                        <el-descriptions-item v-for="(value, key) in curTeachDataFiltered.filter(item => key !== 'id')"
                          :key="key" :label="key">
                          {{ value }}
                        </el-descriptions-item>
                      </el-descriptions>
                      <el-empty v-else description="未找到当前教学任务" />
                    </el-card>
                  </el-col>

                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>已完成教学任务</span>
                        </div>
                      </template>
                      <div v-if="gameData.overTeachIds && gameData.overTeachIds.length > 0">
                        <el-tag v-for="item in gameData.overTeachIds" :key="item" class="mr-2 mb-2" type="success">
                          任务ID: {{ item }}
                        </el-tag>
                      </div>
                      <el-empty v-else description="未找到已完成教学任务" />
                    </el-card>
                  </el-col>
                </el-row>
              </el-tab-pane>

              <!-- 小目标系统 -->
              <el-tab-pane label="小目标系统">
                <el-row :gutter="20">
                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>小目标数据</span>
                        </div>
                      </template>
                      <el-table v-if="gameData.samllTargetDatas && gameData.samllTargetDatas.length > 0"
                        :data="gameData.samllTargetDatas" stripe>
                        <el-table-column label="目标ID" prop="id" />
                        <el-table-column label="其他信息">
                          <template #default="scope">
                            <div v-for="(value, key) in Object.entries(scope.row).filter(([k]) => k !== 'id')"
                              :key="key">
                              {{ key }}: {{ value }}
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-empty v-else description="未找到小目标数据" />
                    </el-card>
                  </el-col>

                  <el-col :span="12">
                    <el-card shadow="hover" class="mb-3">
                      <template #header>
                        <div class="card-header">
                          <span>小目标状态</span>
                        </div>
                      </template>
                      <el-table v-if="gameData.smallTargetStateDatas && gameData.smallTargetStateDatas.length > 0"
                        :data="gameData.smallTargetStateDatas" stripe>
                        <el-table-column label="目标ID" prop="id" />
                        <el-table-column label="状态">
                          <template #default="scope">
                            <el-tag :type="scope.row.state ? 'success' : 'info'">
                              {{ scope.row.state ? '已完成' : '未完成' }}
                            </el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="其他信息">
                          <template #default="scope">
                            <div
                              v-for="(value, key) in Object.entries(scope.row).filter(([k]) => !['id', 'state'].includes(k))"
                              :key="key">
                              {{ key }}: {{ value }}
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <el-empty v-else description="未找到小目标状态数据" />
                    </el-card>
                  </el-col>
                </el-row>
              </el-tab-pane>

              <!-- 其他系统 -->
              <el-tab-pane label="其他系统">
                <el-collapse>
                  <el-collapse-item title="在线奖励数据" name="onlineData">
                    <pre v-if="gameData.onlineData">{{ JSON.stringify(gameData.onlineData, null, 2) }}</pre>
                    <el-empty v-else description="未找到在线奖励数据" />
                  </el-collapse-item>

                  <el-collapse-item title="小镇系统数据" name="xzlData">
                    <pre v-if="gameData.xzlData">{{ JSON.stringify(gameData.xzlData, null, 2) }}</pre>
                    <el-empty v-else description="未找到小镇系统数据" />
                  </el-collapse-item>

                  <el-collapse-item title="工厂系统数据" name="gcData">
                    <pre v-if="gameData.gcData">{{ JSON.stringify(gameData.gcData, null, 2) }}</pre>
                    <el-empty v-else description="未找到工厂系统数据" />
                  </el-collapse-item>

                  <el-collapse-item title="微信相关数据" name="wxData">
                    <pre v-if="gameData.wxData">{{ JSON.stringify(gameData.wxData, null, 2) }}</pre>
                    <el-empty v-else description="未找到微信相关数据" />
                  </el-collapse-item>

                  <el-collapse-item title="签到数据" name="sginData">
                    <pre v-if="gameData.sginData">{{ JSON.stringify(gameData.sginData, null, 2) }}</pre>
                    <el-empty v-else description="未找到签到数据" />
                  </el-collapse-item>

                  <el-collapse-item title="装扮系统数据" name="fitUpData">
                    <pre v-if="gameData.fitUpData">{{ JSON.stringify(gameData.fitUpData, null, 2) }}</pre>
                    <el-empty v-else description="未找到装扮系统数据" />
                  </el-collapse-item>

                  <el-collapse-item title="音效设置" name="audioData">
                    <pre v-if="gameData.audioData">{{ JSON.stringify(gameData.audioData, null, 2) }}</pre>
                    <el-empty v-else description="未找到音效设置数据" />
                  </el-collapse-item>
                </el-collapse>
              </el-tab-pane>

              <!-- 原始JSON数据 -->
              <el-tab-pane label="原始数据">
                <el-input type="textarea" :rows="20" v-model="jsonStr" readonly placeholder="暂无数据" />
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 提示信息区域 -->
          <div v-if="!gameData" class="info-section">
            <el-alert title="功能说明" type="info" description="请先输入OpenID，然后点击'下载数据'获取游戏数据，修改后点击'上传数据'保存。" show-icon
              :closable="false" />
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Chicken,
  Document,
  Download,
  Upload,
  Delete,
  DataAnalysis,
  Edit,
  ShoppingCart,
  ShoppingBag,
  Money,
  GoldMedal,
  Trophy,
  Lightning,
  Coin,
  Star,
  Plus
} from '@element-plus/icons-vue'
import axios from 'axios'
import pako from 'pako'

// 表单数据
const formData = ref({
  openId: 'oEqt860jV-TzTJ8-Elx3aZJPNZMA'
})

// 加载状态
const loading = ref({
  download: false,
  upload: false
})

// 游戏数据
const gameData = ref(null)
const jsonStr = ref('')

// 计算属性：针对解决v-for和v-if混用问题
const playerDataFiltered = computed(() => {
  if (!gameData.value || !gameData.value.playerData) return [];

  return Object.entries(gameData.value.playerData)
    .filter(([key]) => !['playerName', 'playerLv', 'skinId'].includes(key))
    .map(([key, value]) => ({ key, value }));
});

const curTeachDataFiltered = computed(() => {
  if (!gameData.value || !gameData.value.curTeachData) return [];

  return Object.entries(gameData.value.curTeachData)
    .filter(([key]) => key !== 'id')
    .map(([key, value]) => ({ key, value }));
});

// 获取对象过滤后的键值对 - 用于表格中显示其他属性
function getFilteredProperties(obj, excludeKeys) {
  if (!obj) return [];

  return Object.entries(obj)
    .filter(([key]) => !excludeKeys.includes(key))
    .map(([key, value]) => ({ key, value }));
}

// 是否有数据可上传
const hasData = computed(() => {
  return !!formData.value.openId
})

// 获取物品状态类型对应的tag类型
function getItemStatusType(state) {
  switch (state) {
    case 1: return 'success' // 正常
    case 2: return 'warning' // 锁定
    case 3: return 'info'    // 空
    default: return 'info'
  }
}

// 获取物品状态文本
function getItemStatusText(state) {
  switch (state) {
    case 1: return '正常'
    case 2: return '锁定'
    case 3: return '空'
    default: return '未知状态'
  }
}

// 获取订单类型名称
function getOrderType(type) {
  // 简单返回类型，实际项目中可对应到具体名称
  return `类型${type}`
}

// 清空数据
const handleClearData = () => {
  ElMessageBox.confirm('确定要清空当前数据吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    formData.value.openId = ''
    gameData.value = null
    jsonStr.value = ''
    ElMessage({
      type: 'success',
      message: '数据已清空'
    })
  }).catch(() => {
    // 取消清空操作
  })
}

// 添加农田
const addCropData = () => {
  if (!gameData.value.cropDatas) {
    gameData.value.cropDatas = [];
  }

  // 获取最后一个农田ID
  const lastCid = gameData.value.cropDatas.length > 0
    ? Math.max(...gameData.value.cropDatas.map(crop => crop.cid))
    : 50100;

  // 创建新农田数据
  const newCrop = {
    cid: lastCid + 1,
    level: 0,
    poolPhy1: 0,
    poolPhy2: 0,
    lastAddPhyTime: Math.floor(Date.now() / 1000),
    cdTime: 0,
    initCdTime: 0,
    enterTimesToDay: 0,
    qiPaoDatas: [],
    hasYg: 0,
    fishTimes: 0,
    curFishTime: 0,
    curFishWholeTime: 0,
    isRecPhy: 0
  };

  gameData.value.cropDatas.push(newCrop);
  ElMessage.success('添加农田成功');
}

// 删除农田
const removeCropData = (index) => {
  gameData.value.cropDatas.splice(index, 1);
  ElMessage.success('删除农田成功');
}

// 添加解锁作物
const addCropItem = () => {
  if (!gameData.value.unLockCropItemIds) {
    gameData.value.unLockCropItemIds = [];
  }

  // 生成一个新ID（实际中可能需要更复杂的逻辑）
  const newId = Math.max(0, ...gameData.value.unLockCropItemIds) + 1;

  gameData.value.unLockCropItemIds.push(newId);
  ElMessage.success('添加作物成功');
}

// 删除解锁作物
const removeCropItem = (index) => {
  gameData.value.unLockCropItemIds.splice(index, 1);
  ElMessage.success('删除作物成功');
}

// Base64解码和解压函数
function decryptUserInfo(base64Data) {
  try {
    // 清理和验证Base64
    const cleaned = cleanAndValidateBase64(base64Data);
    if (!cleaned.isValid) {
      throw new Error(cleaned.error);
    }

    // 步骤1: Base64解码
    const binaryString = atob(cleaned.cleanedString);

    // 转换为Uint8Array供pako使用
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // 步骤2: 使用pako解压缩
    const inflated = pako.inflate(bytes, { raw: true });

    // 步骤3: 转换为JSON对象
    const textDecoder = new TextDecoder();
    const jsonString = textDecoder.decode(inflated);
    return {
      success: true,
      data: JSON.parse(jsonString),
      jsonString
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 清理和验证Base64字符串
function cleanAndValidateBase64(input) {
  if (!input) {
    return {
      isValid: false,
      cleanedString: "",
      error: "输入为空",
    };
  }

  // 移除所有非Base64字符（包括空格、换行等）
  let cleaned = input.replace(/[^A-Za-z0-9+/=]/g, "");

  // 处理URL安全的Base64（替换 - 为 + 和 _ 为 /）
  cleaned = cleaned.replace(/-/g, "+").replace(/_/g, "/");

  // 确保字符串长度是4的倍数，必要时添加=号补充
  while (cleaned.length % 4 !== 0) {
    cleaned += "=";
  }

  // 检查是否是有效的Base64格式
  const validBase64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  const isValid = validBase64Regex.test(cleaned);

  return {
    isValid,
    cleanedString: cleaned,
    error: isValid ? "" : "Base64格式无效",
  };
}

// 格式化日期
function formatDate(timestamp) {
  if (!timestamp) return '-';
  const date = new Date(timestamp * 1000);
  return date.toLocaleString();
}

// 格式化Unix时间戳，更详细的格式化
function formatUnixTime(timestamp) {
  if (!timestamp) return '-';
  const date = new Date(timestamp * 1000);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 下载数据
const handleDownload = async () => {
  if (!formData.value.openId) {
    ElMessage.warning('请先输入OpenID')
    return
  }

  try {
    loading.value.download = true;

    // 调用API获取用户数据
    const response = await axios.post('https://csapi.77can.com/mttsh/loadUserData.php', {
      openid: formData.value.openId,
      platform: 'wx',
      ossname: '132'
    });

    if (response.data && response.data.code === 0) {
      const userData = response.data.data.userdata;

      try {
        // 解析JSON字符串
        const parsedData = JSON.parse(userData);

        // 解密info字段
        if (parsedData.info) {
          const decryptResult = decryptUserInfo(parsedData.info);

          if (decryptResult.success) {
            gameData.value = decryptResult.data;
            jsonStr.value = JSON.stringify(decryptResult.data, null, 2);
            ElMessage.success('数据下载并解密成功!');
          } else {
            ElMessage.error('解密失败: ' + decryptResult.error);
          }
        } else {
          ElMessage.error('返回的数据格式不正确，缺少info字段');
        }
      } catch (error) {
        ElMessage.error('解析JSON失败: ' + error.message);
      }
    } else {
      ElMessage.error('获取数据失败: ' + (response.data.msg || '未知错误'));
    }
  } catch (error) {
    ElMessage.error('下载失败: ' + error.message);
  } finally {
    loading.value.download = false;
  }
}

// 上传数据
const handleUpload = async () => {
  if (!formData.value.openId) {
    ElMessage.warning('请先输入OpenID')
    return
  }

  if (!gameData.value) {
    ElMessage.warning('没有数据可上传，请先下载或输入数据')
    return
  }

  try {
    loading.value.upload = true;

    // 加密游戏数据
    const jsonString = JSON.stringify(gameData.value);

    // 压缩JSON字符串
    const textEncoder = new TextEncoder();
    const bytes = textEncoder.encode(jsonString);
    const deflated = pako.deflate(bytes, { raw: true });

    // 转换为Base64
    let binaryString = "";
    deflated.forEach((byte) => {
      binaryString += String.fromCharCode(byte);
    });
    const base64Data = btoa(binaryString);

    // 构建上传的数据
    const userData = {
      zstate: 2,
      version: 224,
      ctime: 3,
      info: base64Data
    };

    // 发送请求到服务器
    const response = await axios.post('https://csapi.77can.com/mttsh/saveUserData.php', {
      openid: formData.value.openId,
      userdata: JSON.stringify(userData),
      platform: 'wx',
      ossname: '132'
    });

    if (response.data && response.data.code === 0) {
      ElMessage.success('数据上传成功!');
    } else {
      ElMessage.error('上传失败: ' + (response.data.msg || '未知错误'));
    }
  } catch (error) {
    ElMessage.error('上传失败: ' + error.message)
  } finally {
    loading.value.upload = false
  }
}

// 编辑时间字段
function editTimeField(row, field) {
  ElMessageBox.prompt('请输入时间戳（秒级）或日期时间：', '修改时间', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputValue: row[field] || Math.floor(Date.now() / 1000),
    inputValidator: (value) => {
      if (!value) return '时间不能为空';
      return true;
    },
    inputPlaceholder: '输入时间戳或YYYY-MM-DD HH:mm:ss格式'
  }).then(({ value }) => {
    // 尝试识别输入是时间戳还是日期字符串
    let timestamp;
    if (/^\d+$/.test(value)) {
      // 输入是纯数字，当作时间戳处理
      timestamp = Number(value);
    } else {
      // 尝试将输入作为日期字符串处理
      const date = new Date(value);
      if (isNaN(date.getTime())) {
        ElMessage.error('无效的日期格式');
        return;
      }
      timestamp = Math.floor(date.getTime() / 1000);
    }

    row[field] = timestamp;
    ElMessage.success('时间已修改');
  }).catch(() => {
    // 取消操作
  });
}

// 应用套餐
const applyPackage = (packageId) => {
  if (!gameData.value) {
    ElMessage.warning('请先下载数据');
    return;
  }

  const INFINITE_VALUE = 99999999;

  switch (packageId) {
    case 1:
      // 套餐一: 无限体力+无限金币
      gameData.value.physicalData.physicalNum = INFINITE_VALUE;
      gameData.value.moneyNum = INFINITE_VALUE;
      gameData.value.power = INFINITE_VALUE;
      ElMessage.success('套餐一应用成功：无限体力+无限金币');
      break;
    case 2:
      // 套餐二: 无限体力+无限金币+无限红钞
      gameData.value.physicalData.physicalNum = INFINITE_VALUE;
      gameData.value.moneyNum = INFINITE_VALUE;
      gameData.value.power = INFINITE_VALUE;
      gameData.value.redMoneyNum = INFINITE_VALUE;
      ElMessage.success('套餐二应用成功：无限体力+无限金币+无限红钞');
      break;
    case 3:
      // 套餐三: 无限体力+无限金币+无限红钞+无限精力
      gameData.value.physicalData.physicalNum = INFINITE_VALUE;
      gameData.value.moneyNum = INFINITE_VALUE;
      gameData.value.power = INFINITE_VALUE;
      gameData.value.redMoneyNum = INFINITE_VALUE;
      gameData.value.vigour = INFINITE_VALUE;
      gameData.value.beePower = INFINITE_VALUE;
      gameData.value.yuerCount = INFINITE_VALUE;
      ElMessage.success('套餐三应用成功：无限全部资源');
      break;
    default:
      ElMessage.warning('未知的套餐ID');
  }
}
</script>

<style scoped>
.slow-piggy {
  padding: 20px;
}

.main-card {
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.header-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 16px;
}

.header-section .title {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
}

.header-section .title .el-icon {
  margin-right: 10px;
  font-size: 24px;
  color: #409EFF;
}

.status-tag {
  margin-left: 10px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409EFF;
}

.clear-button {
  margin-left: auto;
}

.data-input-section {
  margin-bottom: 24px;
}

.action-section {
  display: flex;
  gap: 16px;
  margin-top: 24px;
}

.action-button {
  flex: 1;
}

.info-section {
  margin-top: 32px;
}

.game-data-section {
  margin-top: 32px;
}

.resource-card {
  margin-bottom: 15px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.resource-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  text-align: center;
  margin: 10px 0;
}

.resource-info {
  font-size: 12px;
  color: #909399;
  text-align: center;
}

.mt-3 {
  margin-top: 15px;
}

.mb-3 {
  margin-bottom: 15px;
}

.mr-2 {
  margin-right: 8px;
}

.mb-2 {
  margin-bottom: 8px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .slow-piggy {
    padding: 16px;
  }

  .action-section {
    flex-direction: column;
    gap: 10px;
  }

  .header-section .title {
    font-size: 18px;
  }
}

.time-editor {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.time-editor .edit-btn {
  opacity: 0.2;
  margin-left: 8px;
  transition: all 0.3s;
}

.time-editor:hover .edit-btn {
  opacity: 1;
}

.packages-section {
  background-color: #f8fafd;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
}

.package-button {
  width: 100%;
  height: 50px;
  font-weight: bold;
  transition: all 0.3s;
}

.package-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.package-button {
  display: none;
  width: 100%;
  height: 50px;
  font-weight: bold;
  transition: all 0.3s;
}

.package-card {
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  padding: 20px;
  height: 100%;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.package-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.package-tag {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1;
}

.package-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  margin: 0 auto 10px;
  border-radius: 50%;
  font-size: 24px;
  color: white;
  transition: all 0.3s;
}

.package-card:hover .package-icon {
  transform: scale(1.1) rotate(10deg);
}

.package-basic .package-icon {
  background: linear-gradient(135deg, #409EFF, #53a8ff);
}

.package-standard .package-icon {
  background: linear-gradient(135deg, #67C23A, #85ce61);
}

.package-premium .package-icon {
  background: linear-gradient(135deg, #F56C6C, #f78989);
}

.package-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
}

.package-content {
  flex: 1;
  margin-bottom: 15px;
}

.package-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: #606266;
}

.package-item .el-icon {
  margin-right: 8px;
  font-size: 16px;
  color: #909399;
}

.package-basic {
  background: linear-gradient(to bottom right, rgba(64, 158, 255, 0.03), rgba(64, 158, 255, 0.08));
}

.package-standard {
  background: linear-gradient(to bottom right, rgba(103, 194, 58, 0.03), rgba(103, 194, 58, 0.08));
}

.package-premium {
  background: linear-gradient(to bottom right, rgba(245, 108, 108, 0.03), rgba(245, 108, 108, 0.08));
}

.package-footer {
  text-align: center;
}

.package-footer .el-button {
  width: 100%;
  font-weight: bold;
}

.d-none {
  display: none;
}
</style>