<template>
  <el-menu :default-active="activeIndex" class="el-menu-demo" mode="horizontal" router @select="handleSelect">
    <el-menu-item index="/">
      <el-icon>
        <HomeFilled />
      </el-icon>
      首页
    </el-menu-item>

    <el-sub-menu index="/recharge">
      <template #title>
        <el-icon>
          <Wallet />
        </el-icon>
        充值管理
      </template>
      <el-menu-item index="/recharge/gunsoulsniping">
        <el-icon>
          <Aim />
        </el-icon>
        魂器狙击
      </el-menu-item>
      <el-menu-item index="/recharge/smallshelter">
        <el-icon>
          <House />
        </el-icon>
        小小庇护所
      </el-menu-item>
      <el-menu-item index="/recharge/cant-beat-me">
        <el-icon>
          <KnifeFork />
        </el-icon>
        砍不过我呀
      </el-menu-item>
      <el-menu-item index="/recharge/crazy-blacksmith">
        <el-icon>
          <Tools />
        </el-icon>
        疯狂铁匠
      </el-menu-item>
      <el-menu-item index="/recharge/three-kingdoms-auto-chess">
        <el-icon>
          <Trophy />
        </el-icon>
        三国争霸自走棋
      </el-menu-item>
    </el-sub-menu>

    <el-sub-menu index="/decoder">
      <template #title>
        <el-icon>
          <Key />
        </el-icon>
        解密工具
      </template>
      <el-menu-item index="/decoder/base64">Base64解密</el-menu-item>
      <el-menu-item index="/decoder/custom-base64">自定义Base64解密</el-menu-item>
    </el-sub-menu>

    <el-sub-menu index="/promotion">
      <template #title>
        <el-icon>
          <Promotion />
        </el-icon>
        宣传中心
      </template>
      <el-menu-item index="/packages">
        <el-icon>
          <Present />
        </el-icon>
        小小庇护所套餐
      </el-menu-item>
      <el-menu-item index="/javelin-packages">
        <el-icon>
          <Position />
        </el-icon>
        标枪王者套餐
      </el-menu-item>
      <el-menu-item index="/used-car-packages">
        <el-icon>
          <Van />
        </el-icon>
        二手车模拟器套餐
      </el-menu-item>
      <el-menu-item index="/cant-beat-me-packages">
        <el-icon>
          <KnifeFork />
        </el-icon>
        砍不过我呀套餐
      </el-menu-item>
      <el-menu-item index="/lazy-brother-packages">
        <el-icon>
          <Magic />
        </el-icon>
        躺平小弟套餐
      </el-menu-item>
      <el-menu-item index="/cultivator-simulator-packages">
        <el-icon>
          <Avatar />
        </el-icon>
        散修生活模拟器套餐
      </el-menu-item>
      <el-menu-item index="/oracle-war-packages">
        <el-icon>
          <Connection />
        </el-icon>
        甲骨文战争套餐
      </el-menu-item>
      <el-menu-item index="/promotion/three-kingdoms">
        <el-icon>
          <Trophy />
        </el-icon>
        三国争霸自走棋
      </el-menu-item>
      <el-menu-item index="/slow-piggy-packages">
        <el-icon>
          <Food />
        </el-icon>
        慢豚豚的生活套餐
      </el-menu-item>
      <el-menu-item index="/who-beat-packages">
        <el-icon>
          <Trophy />
        </el-icon>
        看谁能打过套餐
      </el-menu-item>
    </el-sub-menu>

    <el-menu-item index="/user">
      <el-icon>
        <User />
      </el-icon>
      用户信息
    </el-menu-item>

    <el-menu-item index="/about">
      <el-icon>
        <InfoFilled />
      </el-icon>
      关于
    </el-menu-item>
  </el-menu>
</template>

<script setup>
import { ref } from 'vue'
import {
  HomeFilled,
  Wallet,
  User,
  InfoFilled,
  Key,
  Aim,
  House,
  Promotion,
  Present,
  Position,
  Van,
  KnifeFork,
  Tools,
  Magic,
  Avatar,
  Connection,
  Trophy
} from '@element-plus/icons-vue'

const activeIndex = ref('/')

const handleSelect = (key, keyPath) => {
  console.log(key, keyPath)
}
</script>

<style scoped>
.el-menu-demo {
  padding-left: 20px;
}

:deep(.el-menu--horizontal .el-menu-item),
:deep(.el-menu--horizontal .el-sub-menu__title) {
  display: flex;
  align-items: center;
}

:deep(.el-icon) {
  margin-right: 5px;
  vertical-align: middle;
}
</style>
