<template>
  <el-card class="recharge-form" shadow="hover">
    <template #header>
      <h2>充值表单</h2>
    </template>
    <el-form @submit.prevent="submitForm" :model="form" :rules="rules" ref="formRef" label-position="top">
      <el-form-item label="充值类型" prop="rechargeType">
        <el-select v-model="form.rechargeType" placeholder="请选择充值类型" style="width: 100%;">
          <el-option label="金币" value="gold"></el-option>
          <el-option label="体力" value="energy"></el-option>
          <el-option label="天赋" value="talent"></el-option>
          <el-option label="等级" value="level"></el-option>
          <el-option label="天赋石" value="talentStone"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="充值数量" prop="rechargeAmount">
        <el-input-number v-model="form.rechargeAmount" :min="1" :step="1" step-strictly style="width: 100%;"></el-input-number>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" native-type="submit" :disabled="!isFormValid" style="width: 100%;">提交充值</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
import { ref, reactive, computed } from 'vue'

export default {
  name: 'RechargeForm',
  setup(props, { emit }) {
    const formRef = ref(null)
    const form = reactive({
      rechargeType: '',
      rechargeAmount: 1
    })

    const rules = {
      rechargeType: [
        { required: true, message: '请选择充值类型', trigger: 'change' }
      ],
      rechargeAmount: [
        { required: true, message: '请输入充值数量', trigger: 'blur' },
        { type: 'number', min: 1, message: '充值数量必须为正整数', trigger: 'blur' }
      ]
    }

    const isFormValid = computed(() => {
      return form.rechargeType && form.rechargeAmount > 0
    })

    const submitForm = () => {
      formRef.value.validate((valid) => {
        if (valid) {
          emit('recharge', {
            type: form.rechargeType,
            amount: form.rechargeAmount
          })
          formRef.value.resetFields()
        }
      })
    }

    return {
      formRef,
      form,
      rules,
      isFormValid,
      submitForm
    }
  }
}
</script>

<style scoped>
.recharge-form {
  max-width: 400px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .recharge-form {
    max-width: 100%;
  }
}
</style>
